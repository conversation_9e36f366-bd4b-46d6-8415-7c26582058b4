{"name": "vue-cli3", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode dev", "build:prod": "vue-cli-service build --mode online", "build:qa": "vue-cli-service build --mode qa", "lint": "vue-cli-service lint"}, "dependencies": {"animate.css": "^4.1.1", "axios": "^0.19.2", "better-scroll": "^1.13.2", "callapp-lib": "^2.1.8", "core-js": "^3.6.4", "crypto-js": "^4.0.0", "css-loader": "^3.4.2", "driver.js": "^0.9.8", "echarts": "^5.3.2", "element-resize-detector": "^1.2.3", "element-ui": "^2.13.1", "html2canvas": "^1.4.1", "js-cookie": "^2.2.1", "jsplumb": "^2.15.6", "jwchat": "^1.1.1", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "mathjax": "3.2.2", "moment": "^2.24.0", "mousetrap": "^1.6.5", "nprogress": "^0.2.0", "qrcodejs2": "0.0.2", "sass": "~1.32.6", "sass-loader": "^8.0.2", "scss-loader": "0.0.1", "stream-to-blob": "^2.0.1", "svgaplayerweb": "^2.3.2", "swiper": "^5.3.7", "v-viewer": "^1.7.4", "vant": "^2.12.30", "videojs-contrib-hls": "^5.15.0", "viewerjs": "^1.11.6", "vue": "^2.6.11", "vue-animate-onscroll": "^1.0.8", "vue-awesome-swiper": "^4.1.1", "vue-fullpage.js": "^0.1.7", "vue-lazyload": "^1.3.3", "vue-lottie": "^0.2.1", "vue-router": "^3.1.6", "vue-script2": "^2.1.0", "vue-video-player": "^5.0.2", "vuex": "^3.1.3", "wc-messagebox": "^1.10.1"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.11.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "4.4.4", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "cpx": "^1.5.0", "cross-env": "^7.0.3", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "html-critical-webpack-plugin": "^2.1.0", "html-webpack-plugin": "^3.2.0", "script-ext-html-webpack-plugin": "^2.1.5", "vconsole": "^3.15.0", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions"]}