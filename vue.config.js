const path = require('path')
// const HtmlCriticalWebpackPlugin = require('html-critical-webpack-plugin')

function resolve (dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  lintOnSave: false,
  transpileDependencies: ['vue-animate-onscroll'],
  devServer: {
    disableHostCheck: true,
    proxy: {
      // 配置代理，解决跨域请求后台数据的问题
      '/api': {
        target: 'https://api.qa.bingotalk.cn/', // 后台接口
        changeOrigin: true
      },
      '/Home': {
        target: 'https://admin.qa.bingotalk.cn/', // 后台接口
        changeOrigin: true
      }
    }
  },

  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    resolve: {
      alias: {
        '@': resolve('src'),
        rootpath: resolve('./'),
        assets: path.join(__dirname, 'src', 'assets')
      }
    },
    // plugins: [
    //   new HtmlCriticalWebpackPlugin({
    //     base: path.resolve(__dirname, 'dist'),
    //     src: 'index.html',
    //     dest: 'index.html',
    //     inline: true,
    //     minify: true,
    //     extract: true,
    //     width: 375,
    //     height: 565,
    //     penthouse: {
    //       blockJSRequests: false
    //     }
    //   })
    // ],
    // module: {
    //   rules: [
    //     {
    //       test: /\.css$/,
    //       use: [
    //         {
    //           loader: MiniCssExtractPlugin.loader,
    //           options: {
    //             // both options are optional
    //             publicPath: '../',
    //             hmr: process.env.NODE_ENV === 'development'
    //             // reloadAll: true
    //           }
    //         },
    //         'css-loader'
    //       ]
    //     }
    //   ]
    // },
    devtool: 'source-map'
  },
  css: {
    // 全局配置utils.scss,详细配置参考vue-cli官网
    loaderOptions: {
      sass: {
        prependData: '@import "@/styles/datacenter.scss";'
      }
    }
  },
  chainWebpack (config) {
    // it can improve the speed of the first screen, it is recommended to turn on preload
    // it can improve the speed of the first screen, it is recommended to turn on preload
    // config.output.filename('js/[name].[hash].js').end()
    // config.plugin('webpack-bundle-analyzer').use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')
    config.module.rule('mjs').test(/\.mjs$/).include.add(resolve('node_modules')).end().type('javascript/auto')
    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config.plugin('html').tap((args) => {
            // args[0].scriptLoading = 'blocking'
            // or
            args[0].inject = 'body'
            return args
          })
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
            // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial' // only package third parties that are initially dependent
                },
                elementUI: {
                  name: 'chunk-elementUI', // split elementUI into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
                },
                agoraRtcSdkNg: {
                  name: 'chunk-agoraRtcSdkNg', // split white-web-sdk into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?agora-rtc-sdk-ng(.*)/ // in order to adapt to cnpm
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'), // can customize your rules
                  minChunks: 3, //  minimum common number
                  priority: 5,
                  reuseExistingChunk: true
                },
                styles: {
                  name: 'styles',
                  test: /\.css$/,
                  chunks: 'all',
                  enforce: true // 忽略splitChunks的其他配置，强制将匹配到的缓存组中的文件合并为一个styles.css文件
                }
              }
            })
          // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
          config.optimization.runtimeChunk('single')
        }
      )
  }
}
