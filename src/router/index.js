import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)
const AIClassPlay = () => import('@/views/ai/play')
const AIGame = () => import('@/views/ai/components/game/components/game.vue')
const AIClassRecorded = () => import('@/views/ai/recorded')

const AiInteract = () => import('@/views/ai-interact/index')
const AiPackage = () => import('@/views/ai-interact/ai-package')
const AiPrepare = () => import('@/views/ai-interact/ai-prepare')

export default new Router({
  mode: 'history',
  scrollBehavior () {
    return { x: 0, y: 0 }
  },
  routes: [
    {
      path: '/',
      redirect: '/ai/1/50/1' // '/ai/0/6/34' // /ai/2/1/32
    },
    {
      path: '/ai/:courseId/:studentCourseId',
      component: AIClassRecorded,
      name: 'AIRecorded',
      meta: {
        title: '缤果双师AI课堂'
      }
    },
    {
      // path: '/ai/:courseId/:studentCourseId/:index',
      path: '/ai/:courseId/:studentCourseId/:unitId',
      component: AIClassPlay,
      name: 'AIPlay',
      meta: {
        title: '缤果双师AI课堂'
      }
    },
    {
      path: '/aigame',
      component: () => import('@/layout/game/index.vue'),
      children: [
        {
          path: '/',
          component: AIGame,
          name: 'AIGame',
          meta: {
            title: '缤果双师AI课堂'
          }
        }
      ]
    },
    // AI互动课页面
    {
      path: '/aiInteract',
      component: AiInteract,
      name: 'aiInteract',
      meta: {
        title: '缤果双师AI课堂'
      }
    },
    {
      path: '/aiPackage/:courseId/:studentCourseId/:assistantId',
      component: AiPackage,
      name: 'aiPackage',
      meta: {
        title: '缤果双师AI课堂'
      }
    },
    {
      path: '/aiPrepare/:courseId/:studentCourseId/:index/:unitId/:assistantId',
      component: AiPrepare,
      name: 'aiPrepare',
      meta: {
        title: '缤果双师AI课堂'
      }
    },
    // 数据中心页面 必须有datacenter!
    {
      path: '/datacenter',
      component: () => import('@/layout/datacenter/index.vue'),
      children: [
        {
          path: '/',
          name: 'dataCenter',
          component: () => import('@/viewsdatacenter/index/index.vue'),
          meta: {
            title: '数据中心'
          }
        },
        {
          path: 'detail',
          component: () => import('@/viewsdatacenter/detail.vue'),
          name: 'dataCenterDetail',
          meta: {
            title: '精彩课堂'
          }
        },
        {
          path: 'onlineDetail',
          component: () => import('@/viewsdatacenter/onlneDetail.vue'),
          name: 'onlineDetail',
          meta: {
            title: '精彩课堂'
          }
        }
      ]
    },
    {
      path: '/datacenter/login',
      component: () => import('@/viewsdatacenter/login.vue'),
      name: 'dataCenterLogin',
      meta: {
        title: '登录'
      }
    }
  ]
})
