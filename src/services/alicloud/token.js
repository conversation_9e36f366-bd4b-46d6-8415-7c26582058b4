import request from '@/utils/request'
import { getToken } from '@/utils/auth'

const API_PATH = 'api/v2/comm/vt/getAliAudioTokenAndAppkey'

class AliCloudTokenManager {
  constructor () {
    this._token = null
    this._appkey = null
    this._expiry = 0
    this._validDuration = 3600000 * 24
  }

  async getToken () {
    if (this._token && this._expiry > Date.now()) {
      return {
        token: this._token,
        appkey: this._appkey
      }
    }

    return this.refreshToken()
  }

  async refreshToken () {
    try {
      const resp = await request({
        url: API_PATH,
        method: 'get',
        urlType: 'api',
        headers: {
          Authorization: getToken()
        }
      })
      const json = resp.data || resp
      if (json.code === 200 && json.data) {
        this._token = json.data.aliToken
        this._appkey = json.data.appkey
        this._expiry = Date.now() + this._validDuration
        return {
          token: this._token,
          appkey: this._appkey
        }
      }
      throw new Error('获取阿里云token失败')
    } catch (error) {
      this._token = null
      this._appkey = null
      this._expiry = 0
      throw error
    }
  }
}

export default new AliCloudTokenManager()
