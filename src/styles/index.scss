@import './variables';
@import './mixin';
@import './utils.scss';

* {
  outline: none;
  -webkit-tap-highlight-color: transparent;
}

body {
  background: #fbfbfb;
  min-height: 100vh;
  -moz-osx-font-smoothing: grayscale;
  // -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

// ::-webkit-scrollbar-track-piece {
//   background: #d3dce6;
// }
// ::-webkit-scrollbar {
//   width: 6px;
// }
// ::-webkit-scrollbar-thumb {
//   background: #99a9bf;
//   border-radius: 20px;
// }

::-webkit-scrollbar-track-piece {
  background: transparent;
}
::-webkit-scrollbar {
  width: 6px;
}
::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 3px;
}

.common-ul-hover {
  li {
    img {
      transition: all 0.2s;
    }

    &:hover {
      cursor: pointer;

      img {
        transform: scale(1.08);
      }

      p {
        color: $bc !important;
      }
    }
  }
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  i {
    width: 150px;
    height: 150px;
    background: url(../assets/images/<EMAIL>) center center no-repeat;
    background-size: cover;
    margin-bottom: 10px;
  }

  .text {
    font-size: 14px;
    color: #a1a1a1;
    padding-bottom: 20px;
  }
}

.vjs-poster {
  // background-size: cover !important;
  background-color: transparent !important;
}

.video-js {
  & .vjs-time-control {
    display: block !important;
  }
  & .vjs-remaining-time {
    display: none !important;
  }
}

.vjs-error {
  .vjs-poster {
    display: block !important;
    // background-image: url("~static/lazy-load.png") !important;
    // background-size: cover;
    transform: scale(1.5);
    @include filterBlur(7px);
  }

  .vjs-error-display:before {
    content: '' !important;
  }
}

.is-error {
  input::placeholder {
    color: transparent;
    font-size: 12px;
  }
}

.flex {
  display: flex;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.justify-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.full-h {
  height: 100%;
}

.full-w {
  width: 100%;
}

.full-img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.fit-img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}

.exchange-confirm-box {
  display: none;
  align-items: center;
  justify-content: center;
  background: transparent;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  &.fade {
    display: flex;
  }
  .confirm-inner {
    width: 580px;
    display: flex;
    justify-content: center;
    padding: 45px 0px;
    background: rgba(0, 0, 0, 0.8);
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
    border-radius: 23px;
    flex-direction: column;
    align-items: center;
    position: relative;
    .confirm-title {
      font-size: 16px;
      color: #fff;
      margin-bottom: 20px;
      text-align: center;
    }
    .exchange-input-box {
      margin: 20px 0px 10px;
      position: relative;
      display: flex;
      align-items: center;
      label {
        font-size: 16px;
        color: #fff;
        margin-right: 20px;
      }
      .exchange-input {
        display: flex;
        align-items: center;
        input {
          width: 300px;
          background: #fff;
          outline: none;
          border: 0;
          border-radius: 23px;
          height: 44px;
          line-height: 44px;
          padding-left: 15px;
          font-size: 16px;
        }
        .exchange-btn-small {
          width: 107px;
          height: 44px;
          background: $pcbc;
          color: #fff;
          font-size: 14px;
          line-height: 44px;
          text-align: center;
          border-radius: 23px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
    .exchange-tip {
      font-size: 14px;
      color: #bdbdbd;
      width: 100%;
      padding-left: 220px;
      margin-bottom: 20px;
    }
    .close-exchange-confirm-box {
      width: 40px;
      height: 40px;
      background: url(../assets/images/<EMAIL>) center
        center no-repeat;
      background-size: cover;
      position: absolute;
      left: 50%;
      margin-left: -20px;
      bottom: -20px;
      z-index: 1;
      cursor: pointer;
    }
  }
}

.pointer {
  cursor: pointer;
}

.animate__delay-100 {
  animation-delay: 0.1s;
}

.animate__delay-200 {
  animation-delay: 0.2s;
}

.animate__delay-300 {
  animation-delay: 0.3s;
}

.animate__delay-400 {
  animation-delay: 0.4s;
}

.animate__delay-500 {
  animation-delay: 0.5s;
}

.animate__delay-600 {
  animation-delay: 0.6s;
}

.animate__delay-700 {
  animation-delay: 0.7s;
}

.animate__delay-800 {
  animation-delay: 0.8s;
}

.animate__delay-900 {
  animation-delay: 0.9s;
}

.animate__delay-1000 {
  animation-delay: 1s;
}

.animate__delay-1300 {
  animation-delay: 1.3s;
}

.animate__delay-1700 {
  animation-delay: 1.7s;
}

p {
  margin: 0;
}

@font-face {
  font-family: 'DouYuFont';
  font-display: auto;
  src: url('https://static.bingotalk.cn/courses/courseware/6257d0d3301c2.otf');
}

.ai-btn-middle {
  background: url('~assets/images/ai/bg-btn-middle.png') center center no-repeat;
  background-size: contain;
  font-family: DOUYUFont;
  color: #ffffff;
  text-shadow: 0 1px 0 #0f3e7a;
  text-align: center;
  cursor: pointer;

  &:hover {
    background: url('~assets/images/ai/bg-btn-yellow.png') center center
      no-repeat;
    background-size: contain;
  }
}

.ai-btn {
  background: url('~assets/images/ai/bg-btn.png') center center no-repeat;
  background-size: contain;
  font-family: DOUYUFont;
  color: #ffffff;
  text-shadow: 0 1px 0 #0f3e7a;
  text-align: center;
  cursor: pointer;

  &:hover {
    background: url('~assets/images/ai/bg-btn-yellow-small.png') center center
      no-repeat;
    background-size: contain;
  }
}

.data-empty {
  color: #909399;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
}
.el-dialog{
  border-radius: 10px;
}
.el-dialog__header{
  border-bottom: 1PX solid #DADFEA;
}
