@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin userSelect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin ellipsisMore($line) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
}

@mixin wordbreak {
  word-wrap: break-word;
  text-align:justify;
  text-justify:inter-ideograph;
}

@mixin wrapping {
  word-wrap: break-word;
  word-break: break-all;
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: white;
    border-radius: 20px;
  }
}

@mixin aiScrollBar {
  &::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: white;
    border-radius: 3px;
  }
}

@mixin aiPkScrollBar {
  &::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #99a9bf;
    border-radius: 3px;
  }
}

@mixin strok-outside($color) {
  text-shadow: 0 2px $color, 2px 0 $color, -2px 0 $color, 0 -2px $color;
}
@mixin strok-outside-4px($color) {
  text-shadow: 0 4px $color, 4px 0 $color, -4px 0 $color, 0 -4px $color;
}

@mixin bg-image($url) {
  background-image: url("~assets/images/"+ $url + "@2x.png");
  @media (-webkit-min-device-pixel-ratio: 3), (min-device-pixel-ratio: 3) {
    background-image: url("~assets/images/"+ $url + "@2x.png");
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin pct($pct) {
  width: #{$pct};
  position: relative;
  margin: 0 auto;
}

@mixin filterBlur($val) {
  -webkit-filter: blur($val);
  -moz-filter: blur($val);
  -ms-filter: blur($val);
  filter: blur($val);
}

@mixin triangle($width, $height, $color, $direction) {
  $width: $width/2;
  $color-border-style: $height solid $color;
  $transparent-border-style: $width solid transparent;
  height: 0;
  width: 0;
  @if $direction==up {
    border-bottom: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  }
  @else if $direction==right {
    border-left: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
  @else if $direction==down {
    border-top: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  }
  @else if $direction==left {
    border-right: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
}

@mixin scrollBarHidden {
  &::-webkit-scrollbar {
    display: none;
  }
}
