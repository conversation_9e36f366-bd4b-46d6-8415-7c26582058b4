//使用scss的math函数，https://sass-lang.com/documentation/breaking-changes/slash-div
// @use "sass:math"; 


//默认设计稿的宽度
$designWidth: 1440;
//默认设计稿的高度
$designHeight: 900;

//px转为vw的函数
@function vw($px) {
  @return $px * 100vw / 1440;
}

//px转为vh的函数
@function vh($px) {
  @return $px * 100vh / 900;
}

//px转为vw的函数
@function vw2($px) {
  @return $px * 100vmax / 965;
}

//px转为vh的函数
@function vh2($px) {
  @return $px * 100vmin / 650;
}