<template>
  <div v-if="list.length > 0" class="detail">
    <div v-for="(item, index) in list" :key="index" class="detail-item" @click="goRecord(item)">
      <img :src="item.lesson_cover || dfCover" alt="" />
      <div class="title">{{ item.school_name }}</div>
      <div class="subtitle">课程名称：{{ item.lesson_name }}</div>
      <div class="subtitle">上课时间：{{ item.lesson_time }}</div>
      <div class="subtitle">上课班级：{{ item.class_name }}</div>
      <div class="name-box flex align-center" @click.stop="handleTeacherInfo(item)">
        <div class="name">
          上课老师：{{ item.teacher_name }}
        </div>
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    <teacherDialog v-if="show" :id="teacherId" @close="show = false" />
  </div>
  <div v-else class="flex flex-col justify-center items-center" style="height: 100%">
    <img class="empty-img" :src="empty" alt="占位图" />
    <div class="empty-text">暂无数据</div>
  </div>
</template>

<script>
import dfCover from '@/assets/datacenter/dfCover.jpg'
import { getLiveClassList } from '@/api/center.js'
import teacherDialog from '@/viewsdatacenter/components/teacherDialog.vue'
import empty from '@/assets/datacenter/empty.svg'
import { getAdminToken } from '@/utils/auth'
export default {
  components: {
    teacherDialog
  },
  data () {
    return {
      dfCover,
      empty,
      page: 1,
      limit: 20,
      list: [],
      isScroll: true,
      show: false,
      teacherId: 0
    }
  },
  created () {
    this._getLiveClassList()
  },
  mounted () {
    document.addEventListener('scroll', this.loadData, true)
    this.$bus.$on('updateGoodClass', () => {
      this._getLiveClassList()
    })
  },
  destroyed () {
    document.removeEventListener('scroll', this.loadData, true)
  },
  methods: {
    loadData () {
      const el = document.querySelector('.detail')
      const offsetHeight = el.offsetHeight
      el.onscroll = () => {
        const scrollTop = el.scrollTop
        const scrollHeight = el.scrollHeight
        if (offsetHeight + scrollTop - scrollHeight >= -10) {
          console.log('滚动到了底部')
          if (!this.isScroll) {
            return
          }
          this.page++
          this._getLiveClassList()
        }
      }
    },
    _getLiveClassList () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      var parent_path = this.$store.getters.organization.parent_path
      var school_id = this.$store.getters.school.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      if (school_id && school_id !== -1) formData.append('school_id', school_id)
      formData.append('page', this.page)
      formData.append('limit', this.limit)
      getLiveClassList(formData).then(
        response => {
          if (this.page > 1) {
            this.list = this.list.concat(response.data.data.items)
          } else {
            this.list = response.data.data.items
          }
          if (response.data.data.items < this.limit) {
            this.isScroll = false
          }
        }
      )
    },
    goRecord (item) {
      window.open(item.lesson_url + '&AuthCenter=' + getAdminToken(), '_blank')
    },
    handleTeacherInfo (item) {
      this.teacherId = item.teacher_id
      this.show = true
    }
  }
}
</script>

  <style lang="scss" scoped>
  @import '@/styles/mixin';
  .detail {
    height: calc(100% - #{vw(34)});
    padding: vw(17) vw(28) 0;
    display: flex;
    flex-wrap: wrap;
    overflow: scroll;
    align-content: flex-start;
    overflow-x: hidden;
    @include aiScrollBar;

    .detail-item {
      height: 280px;
      background: white;
      border-radius: 10px;
      margin-bottom: vw(27);
      cursor: pointer;

      img {
        width: 100%;
        height: 131px;
        object-fit: cover;
        border-radius: 10px 10px 0 0;
      }

      .title {
        font-weight: 500;
        font-size: 16px;
        color: #0B0B0B;
        padding: 15px 15px 5px;
      }

      .subtitle {
        font-weight: 400;
        font-size: 14px;
        color: #0B0B0B;
        padding: 0 15px 5px;
        height: 20px;
        max-width: calc(100% - 10px);
        @include ellipsisMore(1);
      }

      .name-box {
        font-weight: 400;
        font-size: 14px;
        color: #0B0B0B;
        padding: 0 15px 5px;
        height: 20px;
        .name {
          max-width: calc(100% - 20px);
          @include ellipsisMore(1);
        }
      }
    }
  }

  .empty-text {
    font-size: 25px;
    color: #8A96A3;
    line-height: 30px;
  }

  .empty-img {
    width: 160px;
    height: 160px;
    object-fit: contain;
  }
  @media screen and (min-width: 0px) {
    .detail {
      gap: 5%;
    }
    .detail-item {
      width: 30%;
    }
  }
  @media screen and (min-width: 1281px) {
    .detail {
      gap: 4%;
    }
    .detail-item {
      width: 22%;
    }
  }
  @media screen and (min-width: 1441px) {
    .detail {
      gap: 2.5%;
    }
    .detail-item {
      width: 18%;
    }
  }
  @media screen and (min-width: 1921px) {
    .detail {
      gap: 2%;
    }
    .detail-item {
      width: 15%;
    }
  }
  </style>
