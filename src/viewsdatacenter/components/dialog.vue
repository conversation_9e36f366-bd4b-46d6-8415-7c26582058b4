<template>
  <div class="t-list">
    <div class="t-box">
      <div class="close">
        <i class="el-icon-circle-close" @click="close"></i>
      </div>
      <div class="t-head">
        授课老师（{{ total }}人）
      </div>
      <div class="t-content" v-infinite-scroll="_getTeacherList" infinite-scroll-distance="1" infinite-scroll-disabled="disabled" infinite-scroll-immediate="false">
        <div class="item-card" v-for="item in list" :key="item.id">
          <div class="i-teacher">
            <div class="line"></div>
            <div class="name">{{item.display_name}}</div>
          </div>
          <div class="i-t-detail">
            <img class="img" :src="item.avatar || dfAvatar">
            <div class="flex flex-col img-detail">
              <div class="flex align-center">
                <i class="el-icon-location"></i>
                {{ item.nationality }}
              </div>
              <div class="w flex align-center justify-between mt10">
                <el-rate disabled v-model="item.avg_star"></el-rate>
                <div class="f12 pointer" @click="handleTeacherInfo(item)">
                  简介<i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="loading" class="w flex justify-center f14">加载中...</div>
        <div v-if="noMore" class="w flex justify-center f14">没有更多了</div>
      </div>
    </div>
    <teacherDialog v-if="show" @close="show = false" :id="teacherId"></teacherDialog>
  </div>
</template>

<script>
import teacherDialog from "./teacherDialog.vue";
import { getTeacherList } from '@/api/center.js'
import dfAvatar from "@/assets/datacenter/dfAvatar.png";
export default {
  components: {
    teacherDialog
  },
  data () {
    return {
      list: [],
      value1: null,
      show: false,
      page: 1,
      limit: 20,
      total: 0,
      loading: false,
      teacherId: null,
      dfAvatar
    }
  },
  mounted() {
    this._getTeacherList()
  },
  computed: {
    noMore () {
      return this.list.length >= +this.total
    },
    disabled () {
      return this.loading || this.noMore
    }
  },
  methods: {
    close () {
      this.$emit('close')
    },
    async _getTeacherList () {
      try {
        this.loading = true
        let formData = new FormData()
        formData.append('page', this.page)
        formData.append('limit', this.limit)
        const { data: { data } } = await getTeacherList(formData)
        this.page = this.page + 1
        this.total = data.total
        this.list = this.list.concat(data.items)
        console.log(this.list)
        this.loading = false
      } catch (error) {
        this.loading = false
        console.log(error)
      }
    },
    handleTeacherInfo (item) {
      this.teacherId = item.id
      this.show = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.t-list {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($color: #000000, $alpha: 0.62);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  .f14 {
    font-size: vh2(14);
  }

  .close {
    position: absolute;
    bottom: -#{vh2(60)};
    cursor: pointer;
    font-size: vh2(26);
    color: #fff;
    left: 0;
    right: 0;
    text-align: center;
  }

  .t-box {
    background: #0A1016;
    border: 2px solid #496E91;
    border-radius: vh2(22);
    padding: vh2(20) vh2(10) vh2(20) vh2(20);
    box-sizing: border-box;
    width: vh2(670);
    height: vh2(400);
    position: relative;

    .t-head {
      font-weight: 500;
      font-size: vh2(16);
      color: #fff;
      margin-bottom: vh2(10);
    }
    .t-content {
      width: vh2(635);
      height: vh2(320);
      overflow-y: auto;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      &::-webkit-scrollbar-track-piece {
        background: transparent;
      }
      &::-webkit-scrollbar {
        width: 4px;
      }
      &::-webkit-scrollbar-thumb {
        background: #99a9bf;
        border-radius: 3px;
      }
      .item-card {
        background: #254D75;
        box-shadow: 0px vh2(6) vh2(12) rgba(0, 0, 0, 0.05);
        border-radius: vh2(12);
        color: #fff;
        width: vh2(200);
        height: vh2(90);
        margin: 0 vh2(10) vh2(10) 0;
        padding: vh2(5) 0;

        .i-teacher {
          font-size: vh2(16);
          width: 100%;
          height: vh2(30);
          display: flex;
          flex-wrap: nowrap;
          align-items: center;

        }
        .line {
          width: 4px;
          background: #1F66FF;
          height: vh2(20);
          margin-right: vh2(10);
        }

        .name {
          width: calc(100% - #{vh2(15)});
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .i-t-detail {
          padding: 0 vh2(10);
          box-sizing: border-box;
          display: flex;
          font-size: vh2(14);

          .img {
            width: vh2(60);
            height: vh2(60);
            border-radius: 50%;
            margin-right: vh2(5);
            object-fit: cover;
          }

          .el-icon-location {
            color: #1F66FF;
            margin-right: vh2(5);
            font-size: vh2(18);
          }

          .img-detail {
            padding: vh2(5);
            box-sizing: border-box;
            width: calc(100% - #{vh2(65)});
          }

          .mt10 {
            margin-top: vh2(10);
          }
          .f12 {
            font-size: vh2(12);
          }

          ::v-deep .el-rate {
            display: flex;
            align-items: center;
          }

          ::v-deep .el-rate__icon {
            margin: 0 !important;
            font-size: vh2(10) !important;
          }
        }
      }
    }
  }
}
</style>