<template>
  <div class="card-img-box">
    <div class="card-img-head">
      <div class="title">
        <div>{{ title }}</div>
      </div>
      <img class="img" src="../../assets/datacenter/card-b-3.png" alt="" />
    </div>
    <div class="card-img-content">
      <div class="card-detail">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data () {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.card-img-box {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.95);
  box-sizing: border-box;
  position: relative;

  .card-img-head {
    width: 100%;
    height: 25px;
    position: relative;
    box-sizing: border-box;
    padding: 0 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    .title {
      position: relative;
      // background: linear-gradient(180deg, #ffffff 0%, #a3d4f6 100%);
      // -webkit-background-clip: text;
      // color: transparent;
      color: #fff;
      font-size: 16px;
    }
    .img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  .card-img-content {
    width: 100%;
    height: calc(100% - 25px);
    position: relative;
    border: 1px solid rgba(105, 192, 255, 0.55);
    border-top: none;
    border-radius: 0 0 10px 10px;
    // border-bottom: none;
    background: rgba(63, 116, 153, 0.18);
    box-sizing: border-box;

    .card-detail {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
  .card-img-footer {
    height: 10px;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}

</style>
