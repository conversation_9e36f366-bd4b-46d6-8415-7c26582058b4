<template>
  <div class="tree-name" ref="treebox">
    <div class="pointer" :class="{ active: isShow }" @click="isShow = !isShow">
      <div class="flex items-center">
        <div class="name">{{ selectNode.name || defaultName }}</div>
        <i class="el-icon-arrow-down"></i>
      </div>
    </div>
    <div v-show="isShow" class="tree-detail">
      <el-tree
        :data="treeData"
        ref="tree"
        :indent="5"
        :props="defaultProps"
        node-key="id"
        show-checkbox
        check-strictly
        @check-change="handleNodeClick"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    treeData: {
      type: Array,
      require: true,
      default: () => {
        return []
      }
    },
    orignSelectNode: {
      type: Object,
      require: true,
      default: () => {}
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'name'
        }
      }
    },
    defaultName: {
      type: String,
      require: false,
      default: '全部'
    }
  },
  data () {
    return {
      isShow: false,
      selectNode: {} // 选中的节点
    }
  },
  watch: {
    orignSelectNode () {
      this.selectNode = this.orignSelectNode;
    }
  },
  mounted () {
    this.selectNode = this.orignSelectNode;
    window.addEventListener('click', this.hiddenTree)
  },
  destroyed () {
    window.removeEventListener('click', this.hiddenTree)
  },
  methods: {
    hiddenTree (e) {
      if (!this.$refs.treebox) return
      if (!this.$refs.treebox.contains(e.target)) {
        this.isShow = false
      }
    },
    handleNodeClick (data, checked) {
      if (checked) {
        this.selectNode = data
        this.$refs.tree.setCheckedNodes([data])
        this.isShow = false
        this.$emit('handleNodeClick', data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.tree-name {
  position: relative;

  .name {
    flex: 1;
    max-width: 120px;
    @include ellipsisMore(1);
  }
}
.active {
  color: #61b6ff;
}
.tree-detail {
  position: absolute;
  top: vh(40);
  left: -#{vw(200)};
  width: vw(300);
  height: vh(260);
  overflow-x: hidden;
  overflow-y: auto;
  border: 1px solid rgba(105, 192, 255, 0.54);
  background: #081017;
  border-radius: 5px;
  padding: vw(10);
  box-sizing: border-box;
  z-index: 11;

  .el-tree {
    background: transparent;
    color: #ffffff;

    ::v-deep .el-tree-node__content {
      background: transparent !important;
    }
  }
}
</style>