<template>
  <div class="card-img-box">
    <div class="card-img-head">
      <img class="img" src="../../assets/datacenter/card-b-1.png" alt="" />
      <div class="title">
        <template v-if="tabs.length">
          <div
            v-for="(tab, index) in tabs"
            :key="index"
            :class="['tab-item', { 'is-active': currentTab === index }]"
            @click.stop="handleTabClick(index)"
          >
            {{ tab.label }}
          </div>
        </template>
        <template v-else>
          <div>{{ title }}</div>
        </template>
        <img src="../../assets/datacenter/titleline.png" alt="" />
      </div>
    </div>
    <div class="card-img-content">
      <div class="card-detail">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Card',
  props: {
    title: {
      type: String,
      default: ''
    },
    tabs: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      currentTab: 0
    }
  },
  methods: {
    handleTabClick (index) {
      this.currentTab = index
      this.$emit('tab-change', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.card-img-box {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.95);
  box-sizing: border-box;
  position: relative;

  .card-img-head {
    width: 100%;
    height: 25px;
    position: relative;
    box-sizing: border-box;
    padding: 0 15px;
    display: flex;
    align-items: center;

    .img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .title {
      position: relative;
      z-index: 2;
      color: #fff;
      font-size: 16px;
      display: flex;
      align-items: baseline;
      width: 60%;
      .tab-item {
        padding: 0 15px;
        cursor: pointer;
        opacity: 0.7;
        font-size: 16px;
        transition: all 0.3s;
        position: relative;
        z-index: 2;

        &.is-active {
          opacity: 1;
          font-weight: 500;
          &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: #fff;
            transition: all 0.3s;
          }
        }

        // &:not(:last-child) {
        //   border-right: 1px solid rgba(255,255,255,0.3);
        // }

        &:hover {
          opacity: 1;
        }
      }
    }
  }
  .card-img-content {
    width: 100%;
    height: calc(100% - 25px);
    position: relative;
    border: 1px solid rgba(105, 192, 255, 0.55);
    border-top: none;
    border-radius: 2px 0 10px 10px;
    // border-bottom: none;
    background: rgba(63, 116, 153, 0.18);
    box-sizing: border-box;

    .card-detail {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
  .card-img-footer {
    height: 10px;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}

@media screen and (max-width: 768px) {
  .card-img-box .card-img-head {
    padding: 0 8px;
  }
  .card-img-box .card-img-head .title img {
    display: none;
  }
}

@media screen and (max-width: 960px) {
  .card-img-box .card-img-head {
    padding: 0 8px;
  }
  .card-img-box .card-img-head .title img {
    display: none;
  }
}

@media screen and (min-width: 960px) {
  .card-img-box .card-img-head {
    padding: 0 10px;
  }
  .card-img-box .card-img-head .title img {
    display: none;
  }
}

@media screen and (min-width: 1280px) {
  .card-img-box .card-img-head {
    padding: 0 15px;
  }
  .card-img-box .card-img-head .title img {
    display: block;
  }
}

@media screen and (min-width: 1440px) {
  .card-img-box .card-img-head {
    padding: 0 20px;
  }
}

@media screen and (min-width: 1536px) {

}

@media screen and (min-width: 1632px) {

}

@media screen and (min-width: 1728px) {

}

@media screen and (min-width: 1824px) {

}

@media screen and (min-width: 1920px) {
  .card-img-box .card-img-head {
    padding: 0 25px;
  }
}
</style>
