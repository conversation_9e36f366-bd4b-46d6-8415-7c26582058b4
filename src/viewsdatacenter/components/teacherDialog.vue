<template>
  <div class="t-list">
    <div class="t-box">
      <div class="close">
        <i class="el-icon-circle-close" @click="close"></i>
      </div>
      <template v-if="info">
        <div v-if="info" class="i-teacher">
          <div class="line"></div>
          <div class="name">{{ info.display_name }}</div>
        </div>
        <div class="i-t-detail-pop">
          <img class="img" :src="info.avatar || dfAvatar">
          <div class="flex flex-col img-detail">
            <div class="flex align-center">
              <i class="el-icon-location"></i>
              {{info.nationality}}
            </div>
            <div class="w flex align-center mt10">
              <el-rate disabled v-model="info.avg_star"></el-rate>
            </div>
          </div>
        </div>
        <div class="pop-detail">
          <div class="pop-text" v-html="info.desc"></div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { getTeacherInfo } from '@/api/center.js'
import dfAvatar from "@/assets/datacenter/dfAvatar.png";
export default {
  props: {
    id: {
      type: [Number, String],
      default: 0
    }
  },
  data () {
    return {
      info: null,
      dfAvatar
    }
  },
  watch: {
    id: {
      handler(val, newVal) {
        if (val && val !== newVal) {
          this._getTeacherInfo()
        }
      },
      immediate: true
    }
  },
  methods: {
    close () {
      this.$emit('close')
    },
    async _getTeacherInfo () {
      let formData = new FormData()
      formData.append('id', this.id)
      const { data: { data } } = await getTeacherInfo(formData)
      data.desc = (data && data.introduction && data.introduction.split('\n').join('<br><br>')) || '暂无简介'
      this.info = data
    }
  }
}
</script>

<style lang="scss" scoped>
.t-list {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($color: #000000, $alpha: 0.62);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: #fff;

  .close {
    position: absolute;
    bottom: -60px;
    cursor: pointer;
    font-size: vh2(26);
    color: #fff;
    left: 0;
    right: 0;
    text-align: center;
  }
  .t-box {
    background: #0A1016;
    border: 2px solid #496E91;
    border-radius: vh2(22);
    padding: vh2(20);
    box-sizing: border-box;
    width: vh2(400);
    height: vh2(300);
    position: relative;

    .i-teacher {
      font-size: vh2(18);
      width: 100%;
      height: vh2(30);
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      color: #fff;
    }
    .line {
      width: 4px;
      background: #1F66FF;
      height: vh2(20);
      margin-right: vh2(10);
    }

    .name {
      width: calc(100% - #{vh2(15)});
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .i-t-detail-pop {
      display: flex;
      font-size: vh2(14);
      margin-top: vh2(10);

      .img {
        width: vh2(60);
        height: vh2(60);
        border-radius: 50%;
        margin-right: vh2(5);
      }

      i {
        color: #1F66FF;
        margin-right: vh2(5);
        font-size: vh2(18);
      }

      .img-detail {
        padding: vh2(5);
        box-sizing: border-box;
        width: calc(100% - #{vh2(65)});
      }

      .mt10 {
        margin-top: vh2(10);
      }
      .f12 {
        font-size: vh2(12);
      }

      ::v-deep .el-rate {
        display: flex;
        align-items: center;
      }

      ::v-deep .el-rate__icon {
        margin: 0 !important;
        font-size: vh2(16) !important;
      }
    }

    .pop-detail {
      background: #254D75;
      border: 1px solid rgba(31, 102, 255, 0.3);
      border-radius: vh2(19);
      width: 100%;
      height: vh2(140);
      color: #fff;
      font-size: vh2(14);
      margin-top: vh2(15);
      padding: vh2(10);
      box-sizing: border-box;

      .pop-text {
        width: calc(100% - #{vh2(10)});
        height: vh2(110);
        overflow-y: auto;
        margin: vh2(5);
        box-sizing: border-box;
        color: #fff;
        font-size: vh2(14);
      }
    }
  }
}
</style>