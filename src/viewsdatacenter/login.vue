<template>
  <div class="datacenter-login flex align-center justify-center">
    <div class="left">
      <img class="title fit-img" :src="title" alt="数据监控" />
      <div class="line"></div>
    </div>
    <div class="right">
      <img class="fit-img" :src="bgLogin" alt="登录背景" />
      <div class="login-inner flex-col">
        <img class="favicon" :src="favicon" alt="图标" />
        <span>欢迎登录</span>
        <img class="decorate" :src="decorate" alt="线" />
        <el-form
          ref="loginRuleForm"
          :model="loginRuleForm"
          :rules="loginRules"
        >
          <el-form-item prop="username" class="inputs">
            <el-input
              v-model="loginRuleForm.username"
              type="text"
              placeholder="请输入手机号"
              auto-complete="off"
              @keyup.enter.native="doLogin()"
            >
              <div slot="prefix" class="input-prefix">
                <img class="phone" :src="phone" alt="图标" />
                <div class="line"></div>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginRuleForm.password"
              type="password"
              placeholder="请输入密码"
              class="inputs"
              auto-complete="off"
              @keyup.enter.native="doLogin()"
            >
              <div slot="prefix" class="input-prefix">
                <img class="phone" :src="lock" alt="图标" />
                <div class="line"></div>
              </div>
            </el-input>
          </el-form-item>
        </el-form>
        <div class="login-btn" @click="doLogin">登录</div>
      </div>
    </div>
  </div>
</template>

<script>
import title from '@/assets/datacenter/login/title.png'
import bgLogin from '@/assets/datacenter/login/bg-login.png'
import favicon from '@/assets/datacenter/favicon.png'
import decorate from '@/assets/datacenter/login/decorate.png'
import phone from '@/assets/datacenter/login/phone.svg'
import lock from '@/assets/datacenter/login/lock.svg'
import { mapGetters } from 'vuex'
export default {
  data () {
    return {
      title,
      bgLogin,
      favicon,
      decorate,
      phone,
      lock,
      loginRuleForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          {
            required: true,
            message: '请输入手机号',
            trigger: ['blur', 'change']
          }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: ['blur', 'change'] }
        ]
      },
      loading: false
    }
  },
  computed: {
    ...mapGetters(['loginPath'])
  },
  methods: {
    doLogin () {
      this.$refs['loginRuleForm'].validate(valid => {
        if (valid && !this.loading) {
          this.loading = true
          this.$store
            .dispatch('LoginByData', {
              mobile: this.loginRuleForm.username,
              password: this.loginRuleForm.password,
              remember: 0
            })
            .then(response => {
              this.loading = false
              if (response.data.code === 200) {
                this.$router.replace({
                  path: this.loginPath || '/datacenter'
                })
              } else {
                this.$toast(response.data.message)
              }
            })
            .catch(err => {
              this.loading = false
              console.log(err)
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.datacenter-login {
  width: 100vw;
  height: 100vh;
  background: url('~assets/datacenter/bg.png') no-repeat no-repeat center;
  background-size: cover;

  .left {
    margin-right: vw(155);
    .title {
      width: vw(371);
      height: vw(44);
    }

    .line {
      margin-top: vw(28);
      width: vw(80);
      height: vw(8);
      background: #61B6FF;
      border-radius: 4px;
    }
  }

  .right {
    width: vw(480);
    position: relative;

    .login-inner {
      position: absolute;
      left: vw(50);
      top: vw(60);
      width: vw(380);
    }

    .favicon {
      width: vw(52);
      height: vw(52);
    }

    span {
      font-weight: 500;
      font-size: vw(24);
      color: #FFFFFF;
      letter-spacing: 0;
      text-align: justify;
      margin: vw(15) 0
    }

    .decorate {
      width: vw(35);
      height: vw(2.5);
      margin-bottom: vw(40);
    }

    .input-prefix {
      display: flex;
      align-items: center;
      height: 100%;

      .phone {
        width: vw(16);
        height: vw(16);
      }

      .line {
        width: vw(1);
        height: vw(12);
        opacity: 0.55;
        background: #BFBFBF;
        margin-left: auto;
      }
    }

    .login-btn {
      cursor: pointer;
      width: 100%;
      height: vw(40);
      background: #61B6FF;
      border-radius: 4px;
      line-height: vw(40);
      text-align: center;
      font-weight: 400;
      font-size: vw(16);
      color: #FFFFFF;
    }
  }
}
</style>

<style lang='scss'>
.datacenter-login {
  .el-input__prefix {
    width: vw(25);
    padding-left: vw(10);
  }

  .el-input--prefix .el-input__inner {
    padding-left: vw(46);
    height: vw(40);
    font-size: vw(14) !important;
  }
}
</style>
