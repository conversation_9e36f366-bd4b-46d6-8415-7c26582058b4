<template>
  <!-- 课程分布 -->
  <card :title="'课程分布'">
    <chart
      v-if="basicOption.radar.indicator.length > 0"
      :series-data="dataList"
      :extra-option="extraOption"
      :basic-option="basicOption"
    ></chart>
    <div v-else class="data-empty">暂无数据</div>
  </card>
</template>

<script>
import Chart from "@/viewsdatacenter/components/chart.vue"
import Card from "@/viewsdatacenter/components/card.vue"
// import { fitChartSize } from '@/utils/dataUtil.js'
import { packageDistribute } from '@/api/center.js'
export default {
  components: {
    Chart,
    Card
  },
  data () {
    return {
      indicator: [],
      basicOption: {
        title: {
          text: ''
        },
        // tooltip: {
        //   borderColor: '#5CB4FF',
        //   backgroundColor: '#234761',
        //   textStyle: {
        //     color: 'white', //设置文字颜色
        //   },
        //   borderWidth: 1
        // },
        legend: {
          icon: 'circle',
          orient: 'vertical',
          align: 'left',
          top: '5%',
          left: 0,
          itemGap: 5,
          itemWidth: 6,
          textStyle: {
            color: '#fff',
            padding: [0, 5, 0, 0],
            fontSize: 12
          },
          data: ['总课时分布', '已上课时分布']
        },
        radar: {
          // shape: 'circle',
          indicator: [],
          axisLine: {
            lineStyle: {
              color: '#1D364A',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#1D364A', // 设置网格的颜色
            },
          },
          splitArea: {
            show: false,
          },
          radius: '50%',
          // name: {
          //   rich: {
          //     a: {
          //       fontSize: 12,
          //       color: '#fff'
          //     },
          //     b: {
          //       color: '#61B6FF',
          //       fontSize: 12
          //     },
          //     d: {
          //       color: '#61FFB6',
          //       fontSize: 12
          //     },
          //     triggerEvent: true,
          //   },
          //   formatter: (a) => {
          //     let i = this.contains(this.indicator, a); // 处理对应要显示的样式
          //     return `{a| ${a}}{b| ${this.dataList[0]['value'][i]}}{d| ${this.dataList[1]['value'][i]}}`;
          //   },
          // },
        },
        series: [
          {
            type: 'radar',
            symbolSize: 0,
            data: []
          }
        ]
      },
      dataList: [
        {
          areaStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.5,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(137, 190, 252, 0.05)', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(97, 182, 255, 0.3)', // 100% 处的颜色
                },
              ],
            },
          },
          value: [],
          name: '总课时分布'
        },
        {
          areaStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.5,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(137, 190, 252, 0.05)', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(97, 255, 182, 0.3)', // 100% 处的颜色
                },
              ],
            },
          },
          value: [],
          name: '已上课时分布'
        }
      ],
      extraOption: {
        color: ["#61B6FF", "#61FFB6"],
        // grid: {
        //   top: fitChartSize(30),
        //   right: fitChartSize(10),
        //   left: fitChartSize(20),
        //   bottom: fitChartSize(20) //间距自适应
        // },
        textStyle: {
          color: "#fff",
          fontSize: 12 //字体自适应
        },
      },
    }
  },
  created () {
    this._packageDistribute()
  },
  methods: {
    _packageDistribute () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      var parent_path = this.$store.getters.organization.parent_path
      var school_id = this.$store.getters.school.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      if (school_id && school_id !== -1) formData.append('school_id', school_id)
      packageDistribute(formData).then(
        response => {
          var indicators = []
          var valueAll = []
          var valueFinished = []
          if (response.data.data) {
            for (var item of response.data.data) {
              var packageName = { name: item.package_name }
              indicators.push(packageName)
              valueAll.push(+item.lesson_total)
              valueFinished.push(+item.lesson_finish)
            }
          }
          this.basicOption.radar.indicator = indicators
          this.indicator = indicators
          this.dataList[0].value = valueAll
          this.dataList[1].value = valueFinished
        }
      )
    },
    contains (arr, obj) {
      var i = arr.length;
      while (i--) {
        if (arr[i].name === obj) {
          return i;
        }
      }
      return false;
    }
  }
}
</script>

<style lang="scss" scoped>

</style>