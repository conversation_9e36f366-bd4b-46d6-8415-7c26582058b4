<template>
  <!-- 上课趋势 -->
  <card :title="'上课趋势'">
    <chart
      v-if="dataList.length > 0"
      :series-data="dataList"
      :extra-option="extraOption"
      :basic-option="basicOption"
    ></chart>
  </card>
</template>

<script>
import Chart from "@/viewsdatacenter/components/chart.vue"
import Card from "@/viewsdatacenter/components/card.vue"
// import { fitChartSize } from '@/utils/dataUtil.js'
import { lessonTrend } from '@/api/center.js'
export default {
  components: {
    Chart,
    Card
  },
  data () {
    return {
      basicOption: {
        title: {
          text: '已上课时数',
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          left: 20
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '15%',
          left: '15%',
          bottom: '10%',
          right: '5%',
        },
        xAxis: {
          type: 'category',
          data: [],
          splitLine: {
            show: false
          },
          axisTick: {//刻度
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: false
          },
          minInterval: 1,
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: [
          {
            // symbol: 'circle',
            // showAllSymbol: true,
            // symbolSize: 4,
            // label: {
            //   show: true,
            //   position: 'top',
            //   color: '#fff',

            // },
            data: [],
            type: 'line',
            smooth: true,
            areaStyle: {
                //折线图颜色半透明
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(97, 255, 182, 0.5)' // 0% 处的颜色
                    }, {
                        offset: 1, color: 'rgba(97, 255, 182, 0.1)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                }
            }
          }
        ]
      },
      dataList: [],
      extraOption: {
        color: '#61FFB6',
        textStyle: {
          color: '#fff',
          fontSize: 12 //字体自适应
        },
      },
    }
  },
  created () {
    this._lessonTrend()
  },
  methods: {
    _lessonTrend () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      var parent_path = this.$store.getters.organization.parent_path
      var school_id = this.$store.getters.school.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      if (school_id && school_id !== -1) formData.append('school_id', school_id)
      lessonTrend(formData).then(
        response => {
          const list = response.data.data
          this.basicOption.xAxis.data = []
          this.dataList = []
          var xData = []
          var yData = []
          list.forEach(item => {
            xData.push(item.ym)
            yData.push(item.lesson_finish)
          })
          this.basicOption.xAxis.data = xData
          this.dataList = yData
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
</style>