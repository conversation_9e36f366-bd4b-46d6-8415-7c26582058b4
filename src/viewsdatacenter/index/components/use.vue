<template>
  <!-- 课程使用情况 -->
  <!-- 2022年06月08日15:19:36 wanfen说用玫瑰图 -->
  <card :title="'课程使用情况'">
    <chart
      v-if="dataList.length > 0"
      :series-data="dataList"
      :extra-option="extraOption"
      :basic-option="basicOption"
    ></chart>
    <div v-else class="data-empty">暂无数据</div>
  </card>
</template>

<script>
import Chart from "@/viewsdatacenter/components/chart.vue"
import Card from "@/viewsdatacenter/components/card.vue"
// import { fitChartSize } from '@/utils/dataUtil.js'
import { packageUsage } from '@/api/center.js'
export default {
  components: {
    Chart,
    Card
  },
  data () {
    return {
      basicOption: {
        title: {
          text: '注:百分比为各\n课程的上课进度',
          textStyle: {
            color: '#fff',
            fontSize: 12,
            fontWeight: 'normal'
          },
          right: 0
        },
        series: [
          {
            name: '课程使用情况',
            type: 'pie',
            radius: [30, 60],
            center: ['50%', '50%'],
            roseType: 'area',
            label: {
              show: true,
              position: 'outside',
              formatter: function (parm) {
                return [`{a|${parm.name}}` + '\n' + `{b|${parm.value}}` + `{b|%}`];
              },
              rich: {
                a: {
                  fontSize: 12,
                  color: '#fff',

                },
                b: {
                  fontSize: 12,
                  color: '#fff'
                }
              },
            },
            data: []
          }
        ]
      },
      dataList: [],
      extraOption: {
        // color: ["#61B6FF", "#61FFB6"],
        // grid: {
        //   top: fitChartSize(30),
        //   right: fitChartSize(10),
        //   left: fitChartSize(20),
        //   bottom: fitChartSize(20) //间距自适应
        // }
      },
    }
  },
  created () {
    this._packageUsage()
  },
  methods: {
    pre () { },
    next () { },
    _packageUsage () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      var parent_path = this.$store.getters.organization.parent_path
      var school_id = this.$store.getters.school.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      if (school_id && school_id !== -1) formData.append('school_id', school_id)
      packageUsage(formData).then(
        response => {
          var _dataList = []
          for (var item of response.data.data) {
            _dataList.push({ value: item.lesson_percent, name: item.package_name })
          }
          this.dataList = _dataList
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  height: vw(20);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    height: vw(15);
    cursor: pointer;
  }
}
.mr20 {
  margin-right: vw(20);
}

.u-detail {
  height: calc(100% - #{vw(20)});
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.chat-box {
  width: 25%;
  height: 50%;
  // padding: vw(10);
  box-sizing: border-box;
}
</style>