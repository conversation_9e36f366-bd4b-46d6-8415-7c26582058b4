<template>
  <!-- 数据概览 -->
  <card :title="'数据概览'">
    <div class="overview-box">
      <div class="item-text" @click="showTeacherList = true">授课老师<i class="el-icon-arrow-right"></i></div>
      <div class="col-1">
        <div class="overvied-bg">
          <div class="items">
            <img src="../../../assets/datacenter/school.png" />
            <div>学校数量</div>
          </div>
          <div class="items-num">
            {{ data.school_total || '-' }}
          </div>
        </div>
        <div class="overvied-bg">
          <div class="items">
            <img src="../../../assets/datacenter/people.png" />
            <div>班级数量</div>
          </div>
          <div class="items-num">
            {{ data.class_total || '-' }}
          </div>
        </div>
      </div>
      <div class="col-2">
        <div class="data-item">
          <div class="num">{{ data.package_total || '0' }}</div>
          <div>课程数量</div>
        </div>
        <div class="data-item">
          <div class="num">{{ data.lesson_total || '0' }}</div>
          <div>总课时</div>
        </div>
        <div class="data-item">
          <div class="num">{{ data.lesson_finish || '0' }}</div>
          <div>已上课时</div>
        </div>
        <div class="data-item">
          <!-- <div class="num">{{ `${data.lesson_percent}%` || '-' }}</div> -->
          <el-tooltip
            class="item"
            effect="dark"
            content="上课率=已上课时/合同签订课时*100%，所以上课率可能超过100%哦"
            placement="top-end"
          >
            <div class="num pointer">{{ data.lesson_percent !== null ? `${data.lesson_percent}%` || '0' : '-' }}</div>
          </el-tooltip>
          <div>上课率</div>
        </div>
      </div>
    </div>
    <CenterDialog v-if="showTeacherList" @close="showTeacherList = false"></CenterDialog>
  </card>
</template>

<script>
import Card from "@/viewsdatacenter/components/card.vue"
import CenterDialog from "@/viewsdatacenter/components/dialog.vue"
import { dataOverview } from '@/api/center.js'
export default {
  components: {
    Card,
    CenterDialog
  },
  created () {
    this._dataOverview()
  },
  data () {
    return {
      data: {},
      showTeacherList: false
    }
  },
  methods: {
    _dataOverview () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      const parent_path = this.$store.getters.organization.parent_path
      const school_id = this.$store.getters.school.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      if (school_id && school_id !== -1) formData.append('school_id', school_id)
      dataOverview(formData).then(
        response => {
          this.data = response.data.data
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.overview-box {
  padding: vw(20);
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;

  .item-text {
    position: absolute;
    right: 5px;
    top: -3px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
  }

  .col-1 {
    width: 100%;
    height: 47%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .col-2 {
    width: 100%;
    height: 47%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .data-item {
    width: 24%;
    height: 80%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    font-size: 14px;
    .num {
      font-weight: 500;
      font-size: 22px;
      color: #ffd561;
    }
  }

  .overvied-bg {
    width: 48%;
    height: 75%;
    min-height: 70px;
    background: url('../../../assets/datacenter/bg-card.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: vw(10);
    box-sizing: border-box;

    .items {
      font-size: 12px;
      img {
        width: 20px;
      }
    }

    .items-num {
      height: 100%;
      font-weight: 500;
      font-size: 22px;
      color: #ffd561;
      display: flex;
      align-items: center;
    }
  }
}
</style>