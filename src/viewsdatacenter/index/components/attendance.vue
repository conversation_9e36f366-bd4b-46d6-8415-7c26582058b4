<template>
  <!-- 学校上课情况 -->
  <card :title="'学校上课情况'">
    <div class="flex w h">
      <div class="att-left">
        <chart
          v-if="this.basicOption.yAxis.data.length > 0"
          :series-data="dataList"
          :extra-option="extraOption"
          :basic-option="basicOption"
          :use-series="true"
        />
        <div v-else class="data-empty">暂无数据</div>
      </div>
      <div class="att-right">
        <img class="mb20" :src="pageNum > 1 ? Up : UpGrey" @click="up" />
        <img :src="pageNum < totalNum ? Down : DownGrey" @click="down" />
      </div>
    </div>
  </card>
</template>

<script>
import Chart from '@/viewsdatacenter/components/chart.vue'
import Card from '@/viewsdatacenter/components/card2.vue'
import Up from '@/assets/datacenter/up.png'
import UpGrey from '@/assets/datacenter/up-grey.png'
import Down from '@/assets/datacenter/down.png'
import DownGrey from '@/assets/datacenter/down-grey.png'
// import { fitChartSize } from '@/utils/dataUtil.js'
import { schoolBarList } from '@/api/center.js'
export default {
  components: {
    Chart,
    Card
  },
  data () {
    return {
      basicOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // Use axis to trigger tooltip
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          },
          borderColor: '#5CB4FF',
          backgroundColor: '#234761',
          textStyle: {
            color: 'white' // 设置文字颜色
          },
          borderWidth: 1
        },
        legend: {
          icon: 'circle',
          align: 'left',
          left: '2%',
          itemGap: 5,
          itemWidth: 6,
          textStyle: {
            color: '#fff',
            fontSize: 12,
            padding: [0, 5, 0, 0]
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          minInterval: 1,
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'category',
          axisTick: { // 刻度
            show: false
          },
          splitLine: {
            show: false
          },
          max: function (value) {
            if (value.max < 5) {
              value.max = 5
            }
            return value.max
          },
          axisLabel: {
            interval: 0,
            verticalAlign: 'middle',
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            // 多出字可以省略显示成点
            formatter: function (params) {
              var index = 8 // 字数为3个超出就显示成点
              var newstr = ''
              for (var i = 0; i < params.length; i += index) {
                var tmp = params.substring(i, i + index)
                newstr += tmp + ''
              }
              if (newstr.length > index) { return newstr.substring(0, index) + '...' } else { return newstr }
            }
          },
          data: []
        },
        series: []
      },
      dataList: [],
      subjectList: [],
      extraOption: {
        // color: ["#61B6FF", "#4CE29D"],
        // grid: {
        //   top: fitChartSize(30),
        //   right: fitChartSize(10),
        //   left: fitChartSize(20),
        //   bottom: fitChartSize(20) //间距自适应
        // },
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12 // 字体自适应
        }
      },
      pageSize: 10,
      pageNum: 1,
      totalNum: 1,
      Up,
      UpGrey,
      Down,
      DownGrey
    }
  },
  created () {
    this._schoolBarList()
  },
  methods: {
    up () {
      if (this.pageNum > 1) {
        this.pageNum--
        console.log(this.pageNum)
        this.renderData()
      }
    },
    down () {
      if (this.pageNum < this.totalNum) {
        this.pageNum++
        console.log(this.pageNum)
        this.renderData()
      }
    },
    _schoolBarList () {
      this.pageNum = 1
      this.subjectList = []
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      var parent_path = this.$store.getters.organization.parent_path
      var school_id = this.$store.getters.school.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      if (school_id && school_id !== -1) formData.append('school_id', school_id)
      schoolBarList(formData).then(
        response => {
          this.list = response.data.data
          //  计算所有科目
          for (var item of this.list) {
            for (var item2 of item.packages) {
              this.subjectList.push(item2.package_name)
            }
          }
          this.subjectList = this.subjectList.reduce(function (tempArr, item) {
            if (tempArr.findIndex((ele) => ele === item) === -1) {
              tempArr.push(item)
            }
            return tempArr
          }, [])
          // 计算总页数
          this.totalNum = Math.ceil(this.list.length / this.pageSize)
          this.renderData()
        }
      )
    },
    // 渲染页面
    renderData () {
      var renderList = []
      var schoolList = []
      this.basicOption.yAxis.data = []
      this.dataList = []
      renderList = this.list.slice(
        (this.pageNum - 1) * this.pageSize,
        this.pageNum >= this.totalNum ? this.list.length : ((this.pageNum - 1) * this.pageSize + this.pageSize)
      )
      renderList = renderList.reverse()
      for (var item of renderList) {
        schoolList.push(item.school_name)
      }
      this.subjectList.forEach((item) => {
        this.dataList.push({
          name: item,
          type: 'bar',
          stack: 'total',
          barWidth: '60%',
          emphasis: {
            focus: 'series'
          },
          data: []
        })
      })
      this.dataList.forEach((dataItem) => {
        renderList.forEach((listItem) => {
          var filterList = listItem.packages.filter(item => item.package_name === dataItem.name)
          var length = filterList.length
          if (length > 0 && +filterList[0].lesson_finish > 0) {
            dataItem.data.push(+filterList[0].lesson_finish)
          } else {
            dataItem.data.push({ value: 0, label: { show: false }})
          }
        })
      })
      this.basicOption.yAxis.data = schoolList
    }
  }
}
</script>

<style lang="scss" scoped>
.att-left {
  width: calc(100% - #{vw(20)});
  height: 100%;
}
.att-right {
  width: vw(20);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    width: vw(15);
    cursor: pointer;
  }
}
.mb20 {
  margin-bottom: vw(20);
}
</style>
