<template>
  <!-- 学校上课活跃度 -->
  <card :title="!schoolId || schoolId === -1 ? '学校上课活跃度' : '班级上课活跃度'" class="table-wrapper">
    <div class="flex flex-col w h">
      <div id="activebox" ref="activebox" class="activebox">
        <el-table
          size="mini"
          :data="renderList"
          :height="tHeight"
          style="width: 100%"
          :header-row-class-name="tableHeader"
          :header-cell-class-name="borderLeft"
          :row-class-name="rowClass"
          :cell-class-name="borderLeft"
        >
          <template v-if="!schoolId || schoolId === -1">
            <el-table-column prop="rank" label="活跃度" align="center" width="70" :show-overflow-tooltip="true" />
            <el-table-column prop="school_name" label="学校名称" align="center" :show-overflow-tooltip="true" />
            <el-table-column prop="class_total" label="班级数量" align="center" width="82" :show-overflow-tooltip="true" />
            <el-table-column prop="lesson_total" label="总课时数" align="center" width="82" :show-overflow-tooltip="true" />
            <el-table-column prop="lesson_finish" label="已上课时数" align="center" width="100" :show-overflow-tooltip="true" />
            <el-table-column prop="lesson_percent" label="上课率" align="center" width="70" :show-overflow-tooltip="true" />
          </template>
          <template v-else>
            <el-table-column prop="rank" label="活跃度" align="center" width="70" :show-overflow-tooltip="true" />
            <el-table-column prop="class_name" label="班级名称" align="center" :show-overflow-tooltip="true" />
            <el-table-column prop="lesson_total" label="总课时数" align="center" width="82" :show-overflow-tooltip="true" />
            <el-table-column prop="lesson_finish" label="已上课时数" align="center" width="100" :show-overflow-tooltip="true" />
            <el-table-column prop="lesson_percent" label="上课率" align="center" width="70" :show-overflow-tooltip="true" />
          </template>
        </el-table>
      </div>
      <!-- <div class="page">
        <img
          v-if="pageNum > 1"
          @click="pre"
          class="mr20"
          src="@/assets/datacenter/pre.png"
        />
        <img v-if="pageNum < totalNum" @click="next" src="@/assets/datacenter/next.png" />
      </div> -->
    </div>
  </card>
</template>

<script>
import Card from '@/viewsdatacenter/components/card2.vue'
import { schoolTableList, classTableList } from '@/api/center.js'
export default {
  components: {
    Card
  },
  data () {
    return {
      tHeight: '200', // 初始高度设置
      tableData: [],
      pageSize: 10,
      pageNum: 1,
      totalNum: 1,
      schoolId: -1,
      renderList: []
    }
  },
  created () {
    this._getTableData()
  },
  mounted () {
    // 页面创建时执行一次getHeight进行赋值，顺道绑定resize事件
    this.$nextTick(() => {
      this.setTableHeight()
    })
    // 页面创建完成后添加监听
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy () {
    // 页面销毁时清空监听
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    setTableHeight () {
      this.tHeight = this.$refs.activebox.clientHeight
      console.log(this.tHeight)
      // this.tHeight = window.innerHeight - 350;//数值350根据不同页面高度自己设置
    },
    pre () {
      if (this.pageNum > 1) {
        this.pageNum--
        this.renderData()
      }
    },
    next () {
      if (this.pageNum < this.totalNum) {
        this.pageNum++
        this.renderData()
      }
    },
    tableHeader () {
      return 'activity-table-header'
    },
    borderLeft (row) {
      if (row.columnIndex > 0) return 'activity-border-left'
    },
    rowClass () {
      return 'activity-row-class'
    },
    _getTableData () {
      this.tableData = []
      this.renderList = []
      this.schoolId = this.$store.getters.school.id
      if (this.schoolId && this.schoolId !== -1) {
        this._classTableList()
      } else {
        this._schoolTableList()
      }
    },
    _schoolTableList () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      var parent_path = this.$store.getters.organization.parent_path
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      schoolTableList(formData).then(
        response => {
          this.tableData = response.data.data
          for (var i = 0; i < this.tableData.length; i++) {
            this.tableData[i].rank = i + 1
            this.tableData[i].lesson_percent = this.tableData[i].lesson_percent !== null ? this.tableData[i].lesson_percent + '%' : '-'
          }
          this.totalNum = Math.ceil(this.tableData.length / this.pageSize)
          this.renderList = this.tableData
          // this.renderData()
        }
      )
    },
    _classTableList () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      formData.append('school_id', this.schoolId)
      classTableList(formData).then(
        response => {
          this.tableData = response.data.data
          for (var i = 0; i < this.tableData.length; i++) {
            this.tableData[i].rank = i + 1
            this.tableData[i].lesson_percent = this.tableData[i].lesson_percent !== null ? this.tableData[i].lesson_percent + '%' : '-'
          }
          this.totalNum = Math.ceil(this.tableData.length / this.pageSize)
          // this.renderData()
          this.renderList = this.tableData
        }
      )
    },
    // 渲染页面
    renderData () {
      this.renderList = this.tableData.slice(
        (this.pageNum - 1) * this.pageSize,
        this.pageNum >= this.totalNum ? this.tableData.length : this.pageNum + this.pageSize - 1
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper ::v-deep .el-table {
  background-color: transparent;
}

.table-wrapper ::v-deep .el-table__expanded-cell {
  background-color: transparent;
}

.page {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: vh(10);
  img {
    height: vw(15);
    cursor: pointer;
  }
}
.mr20 {
  margin-right: vw(20);
}

.activebox {
  // height: calc(100% - #{vw(45)});
  height: 100%;
  width: 95%;
  margin: 0 auto;
  margin-top: vh(15);
}
</style>

<style lang="scss">
@import "@/styles/mixin";
.activebox {

  .activity-table-header {
      font-weight: 500;
      background: transparent !important;
      font-size: 14px;
      color: #ACD9FF;
      letter-spacing: 0;

      tr {
        background: transparent !important;
      }

      th {
        background: transparent !important;
        border-bottom: 0.5px solid rgba(97, 182, 255, 0.36) !important;
      }
  }

  .activity-row-class {
      background-color: transparent !important;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      letter-spacing: 0;

      td {
        border-bottom: none !important;
      }
  }

  &:hover {
    td {
      background: transparent !important;
    }
  }

  .activity-border-left {
    border-left: 0.5px solid rgba(97, 182, 255, 0.36) !important;
  }

  .el-table::before {
    height: 0;
  }

  .el-table__body-wrapper {
      @include aiScrollBar;
  }
}
</style>
