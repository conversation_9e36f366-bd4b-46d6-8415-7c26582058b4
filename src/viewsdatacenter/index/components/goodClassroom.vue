<template>
  <!-- 精彩课堂 -->
  <card
    :tabs="[{label: '精彩课堂'}, {label: '实时监课'}]"
    @tab-change="handleTabChange"
  >
    <div class="good-class">
      <!-- 精彩课堂内容 -->
      <template v-if="activeTab === 0">
        <div class="more-data" @click="goDetailPage">查看更多<i class="el-icon-arrow-right"></i></div>
        <template v-if="list.length > 0">
          <div v-for="item in list" :key="item.id" class="class-item" @click="goRecord(item)">
            <div class="cover">
              <img :src="item.lesson_cover || dfCover" alt="" />
              <div class="shadow">
                <div class="bg-white">
                  <img class="trigger" :src="IconTrigger" alt="" />
                </div>
              </div>
            </div>
            <div class="info">
              <div class="good-class-title">{{ item.school_name }}</div>
              <div class="subtitle">课程名称：{{ item.lesson_name }}</div>
              <div class="subtitle">上课班级：{{ item.class_name }}</div>
              <div class="subtitle">上课时间：{{ $moment.formatYYYYMMDD2(item.lesson_time) }}</div>
              <div class="name-box flex align-center" @click.stop="handleTeacherInfo(item)">
                <div class="name">
                  上课老师：{{ item.teacher_name }}
                </div>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>
        </template>
        <div v-else class="data-empty">暂无数据</div>
      </template>

      <!-- 实时监课内容 -->
      <template v-else>
        <div class="more-data" @click="goOnlineDetailPage">查看更多<i class="el-icon-arrow-right"></i></div>
        <div v-if="liveList.length > 0" class="live-monitor">
          <div v-for="item in liveList" :key="item.id" class="live-item" @click="goLiveRoom(item)">
            <div class="cover">
              <img :src="item.cover || dfCover" alt="" />
              <div v-if="item.lesson_status === 1" class="live-status">直播中</div>
              <div v-else class="live-status-end">回放</div>
            </div>
            <div class="info">
              <div class="good-class-title">{{ item.school_name }}</div>
              <div class="subtitle">课程名称：{{ item.lesson_name }}</div>
              <div class="subtitle">上课班级：{{ item.class_name }}</div>
              <div class="name-box flex align-center">
                <div class="name">上课老师：{{ item.teacher_name }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="data-empty">暂无直播课程</div>
      </template>
    </div>
    <teacherDialog v-if="show" :id="teacherId" @close="show = false" />
  </card>
</template>

<script>
import dfCover from '@/assets/datacenter/dfCover.jpg'
import Card from '@/viewsdatacenter/components/card.vue'
import IconTrigger from '@/assets/images/icon-trigger.svg'
import { highlightReplaysList, getLiveClassList } from '@/api/center.js'
import teacherDialog from '@/viewsdatacenter/components/teacherDialog.vue'
import { getAdminToken } from '@/utils/auth'
export default {
  components: {
    Card,
    teacherDialog
  },
  data () {
    return {
      dfCover,
      page: 1,
      limit: 3,
      IconTrigger,
      list: [],
      show: false,
      teacherId: 0,
      activeTab: 0,
      liveList: []
    }
  },
  created () {
    this._highlightReplaysList()
  },
  methods: {
    goDetailPage () {
      this.$router.push({ path: '/datacenter/detail' })
    },
    goOnlineDetailPage () {
      this.$router.push({ path: '/datacenter/onlineDetail' })
    },
    goRecord (item) {
      window.open(item.lesson_video + '&AuthCenter=' + getAdminToken(), '_blank')
    },
    _highlightReplaysList () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      var parent_path = this.$store.getters.organization.parent_path
      var school_id = this.$store.getters.school.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      if (school_id && school_id !== -1) formData.append('school_id', school_id)
      formData.append('page', this.page)
      formData.append('limit', this.limit)
      highlightReplaysList(formData).then(
        response => {
          this.list = response.data.data.items
        }
      )
    },
    handleTeacherInfo (item) {
      console.log(item)
      this.teacherId = item.teacher_id
      this.show = true
    },
    handleTabChange (index) {
      this.activeTab = index
      if (index === 0) {
        this._highlightReplaysList()
      } else {
        this._getLiveClassList()
      }
    },
    _getLiveClassList () {
      var formData = new FormData()
      const yearId = this.$store.getters.year.id
      const termId = this.$store.getters.term.id
      var parent_path = this.$store.getters.organization.parent_path
      var school_id = this.$store.getters.school.id
      if (yearId && yearId !== -1) formData.append('year', yearId)
      if (termId && termId !== -1) formData.append('term', termId)
      if (parent_path) formData.append('parent_path', parent_path)
      if (school_id && school_id !== -1) formData.append('school_id', school_id)
      formData.append('page', this.page)
      formData.append('limit', this.limit)
      getLiveClassList(formData).then(
        response => {
          this.liveList = response.data.data.items
        }
      )
    },
    goLiveRoom (item) {
      // 实现进入直播间的逻辑
      window.open(item.lesson_url + '&AuthCenter=' + getAdminToken(), '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.good-class {
  width: 100%;
  height: 100%;
}
.more-data {
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  letter-spacing: 0;
  width: vh(84);
  min-width: 60px;
  margin-left: auto;
  padding-top: vh(6);
  margin-bottom: vh(25);
  cursor: pointer;
}

.class-item {
  display: flex;
  margin: 0 vh(20);
  width: 100%;
  height: vh(110);
  min-height: 90px;
  margin-bottom: vh(25);
  cursor: pointer;

  .cover {
    position: relative;

    img {
      width: vh(156);
      height: 100%;
      border-radius: 5px;
    }

    .shadow {
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.62);
      position: absolute;
      left: 0;
      top: 0;
    }

    .bg-white {
      width: vh(30);
      height: vh(20);
      background: white;
      border-radius: 4px;
      position: absolute;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50%;
    }

    .trigger {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: calc(100% - #{vh(166)});
    .good-class-title {
      font-weight: 500;
      font-size: 16px;
      color: #FFFFFF;
      letter-spacing: 0;
      margin-bottom: vh(9);
      padding-left: vh(20);
      @include ellipsisMore(1);
    }

    .subtitle {
      font-weight: 400;
      font-size: 12px;
      color: #FFFFFF;
      letter-spacing: 0;
      padding-left: vh(20);
      @include ellipsisMore(1);
    }

    .name-box {
      font-weight: 400;
      font-size: 12px;
      color: #FFFFFF;
      letter-spacing: 0;
      padding-left: vh(20);
      .name {
        max-width: calc(100% - #{vh(20)});
        @include ellipsisMore(1);
      }
    }
  }
}

.live-monitor {
  padding: 0 vh(20);

  .live-item {
    // 复用 class-item 的样式
    @extend .class-item;

    .live-status {
      position: absolute;
      top: 10px;
      right: 10px;
      background: rgba(255, 59, 48, 0.9);
      padding: 2px 8px;
      border-radius: 4px;
      color: white;
      font-size: 12px;
    }
    .live-status-end {
      position: absolute;
      top: 10px;
      right: 10px;
      background: rgba(85, 200, 221, 0.9);
      padding: 2px 8px;
      border-radius: 4px;
      color: white;
      font-size: 12px;
    }
  }
}
</style>
