<template>
  <div class="datacenter-box">
    <div class="datacenter-detail">
      <div class="left-box">
        <div class="d-three">
          <Overview ref="overview" />
        </div>
        <div class="d-three">
          <Distribution ref="distribution" />
        </div>
        <div class="d-three">
          <Uses ref="uses" />
        </div>
      </div>
      <div class="center-box">
        <div class="d-tow">
          <Attendance ref="attendance" />
        </div>
        <div class="d-tow">
          <Activity ref="activity" />
        </div>
      </div>
      <div class="right-box">
        <div class="d-40">
          <Trends ref="trends" />
        </div>
        <div class="d-60">
          <GoodClassroom ref="goodClassroom" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import Tree from "@/viewsdatacenter/components/tree.vue"
import Activity from './components/activity.vue'
import Attendance from './components/attendance.vue'
import Distribution from './components/distribution.vue'
import GoodClassroom from './components/goodClassroom.vue'
import Overview from './components/overview.vue'
import Trends from './components/trends.vue'
import Uses from './components/use.vue'
export default {
  components: {
    Activity,
    Attendance,
    Distribution,
    GoodClassroom,
    Overview,
    Trends,
    Uses
    // Tree
  },
  data () {
    return {
      tCurrTime: null,
      currTime: '',
      week: '',
      dateName: ''
    }
  },
  mounted () {
    this.currTime = this.$moment.formathhmmss(new Date())
    this.tCurrTime = setInterval(() => {
      this.currTime = this.$moment.formathhmmss(new Date())
    }, 1000)
    this.week = this.$moment.getWeek2(new Date())
    this.dateName = this.$moment.formatYYYYMMDD2(new Date())

    this.$bus.$on('updateData', () => {
      this.$refs.overview._dataOverview()
      this.$refs.distribution._packageDistribute()
      this.$refs.uses._packageUsage()
      this.$refs.attendance._schoolBarList()
      this.$refs.trends._lessonTrend()
      this.$refs.goodClassroom._highlightReplaysList()
      this.$refs.activity._getTableData()
    })
  },
  beforeDestroy () {
    clearInterval(this.tCurrTime)
  }
}
</script>

<style lang="scss">
.datacenter-box {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  min-width: 1024px;

  .datacenter-detail {
    color: #fff;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: vh(10);
    display: flex;
    overflow: hidden;

    .left-box,
    .center-box,
    .right-box {
      height: 100%;
      box-sizing: border-box;
      padding: 0 vw(10);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .d-three {
      height: 32.33333333%;
    }
    .d-tow {
      height: 49%;
    }

    .d-40 {
      height: 34%;
    }
    .d-60 {
      height: 64%;
    }
    .left-box {
      width: 30.16667%;
    }
    .center-box {
      width: 39.66667%;
    }
    .right-box {
      width: 30.16667%;
    }
  }
}
</style>
<style lang="scss">
.pop-name {
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  border: 1px solid rgba(105, 192, 255, 0.54);
  border-radius: 5px;
  padding: vw(10);
  box-sizing: border-box;
  min-width: 60px;

  .popper__arrow {
    border-bottom-color: rgba(105, 192, 255, 0.54) !important;
  }
  .popper__arrow::after {
    border-bottom-color: rgba(105, 192, 255, 0.54) !important;
  }
}
</style>
