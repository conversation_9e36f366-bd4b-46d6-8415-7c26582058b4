import axios from '@/utils/request'
import { getAdminToken } from '@/utils/auth'

export function dologin(params) {
  params.userType = 'STUDENT';
  params.loginType = 'PASSWORD';
  return axios({
    url: 'api/v2/user/vt/login',
    method: 'get',
    params,
    urlType: 'api'
  })
}

export function doregister(data) {
  var formData = new FormData()
  formData.append('userType', 'STUDENT')
  formData.append('mobileOrEmail', data.mobileOrEmail)
  formData.append('verifyCode', data.verifyCode)
  formData.append('password', data.password)
  return axios({
    url: 'api/v2/user/vt/register',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: formData,
    urlType: 'api'
  })
}

export function forgetPassword(data) {
  data.noAuth = true
  return axios({
    url: '/api/v1/students/forgetPassword',
    method: 'post',
    data,
    urlType: 'api'
  })
}

export function slideToGetSmsCode(data) {
  var formData = new FormData()
  formData.append('smsCodeType', data.smsCodeType)
  formData.append('mobile', data.mobile)
  formData.append('sig', data.sig)
  formData.append('sessionId', data.sessionId)
  formData.append('token', data.token)
  formData.append('scene', data.scene)
  return axios({
    url: 'api/v1/systemManager/vt/slideToGetSmsCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: formData,
    urlType: 'api'
  })
}

//  数据中心接口
//  登录
export function doDatalogin(data) {
  var formData = new FormData()
  formData.append('mobile', data.mobile)
  formData.append('password', data.password)
  formData.append('remember', data.remember)
  return axios({
    url: 'center/user/login',
    method: 'post',
    data:formData,
    urlType: 'admin'
  })
}

//  退出登录
export function doDatalogout() {
  return axios({
    url: 'center/user/logout',
    method: 'post',
    urlType: 'admin',
    headers: {
      'Cms-Token': getAdminToken()
    },
  })
}