import axios from '@/utils/request'
import {
  getToken
} from '@/utils/auth'

export function getRecommendVideoGoodsList(params) {
  return axios({
    url: 'api/v2/videoGoods/vt/getRecommendVideoGoodsList',
    method: 'get',
    params,
    urlType: 'api'
  })
}

export function getLastAppVersion(headers) {
  return axios({
    url: 'api/v2/comm/vt/getLastAppVersion',
    method: 'get',
    urlType: 'api',
    headers
  })
}

export function getVideoGoodsCategory(params) {
  return axios({
    url: 'api/v2/videoGoods/vt/getVideoGoodsCategory',
    method: 'get',
    params,
    urlType: 'api'
  })
}
export function getGategoryVideogoodsList(params) {
  return axios({
    url: 'api/v2/videoGoods/vt/getGategoryVideogoodsList',
    method: 'get',
    params,
    urlType: 'api'
  })
}

export function getVideoGoodsList(params) {
  return axios({
    url: 'api/v2/videoGoods/vt/getVideoGoodsList',
    method: 'get',
    params,
    urlType: 'api'
  })
}

export function getVideoGoods(params) {
  return axios({
    url: 'api/v2/videoGoods/vt/getVideoGoods',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getScheduledLessons(params) {
  return axios({
    url: 'api/v1/students/scheduledLessons',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getBannerForTraditional(params) {
  return axios({
    url: 'Home/BannerApi/bannerForTraditional',
    method: 'get',
    params,
    urlType: 'admin'
  })
}

export function getNewsList(params) {
  return axios({
    url: 'Home/InformationApi/list',
    method: 'get',
    params,
    urlType: 'admin'
  })
}
export function getNewsDetail(params) {
  return axios({
    url: 'Home/InformationApi/detail',
    method: 'get',
    params,
    urlType: 'admin'
  })
}