import axios from '@/utils/request'
import {
  getAdminToken,
  getToken
} from '@/utils/auth'
export function fetchUserInfo (params) {
  return axios({
    url: 'api/v2/user/vt/getUserInfo',
    method: 'get',
    params,
    headers: {
      'Authorization': getToken()
    },
    urlType: 'api'
  })
}
export function updateUserInfo (data) {
  var formData = new FormData()
  formData.append('displayName', data.displayName)
  return axios({
    url: 'api/v2/user/updateUserInfo',
    method: 'post',
    data: formData,
    headers: {
      'Authorization': getToken()
    },
    urlType: 'api'
  })
}
export function updatePassword (data) {
  return axios({
    url: 'api/v1/students/updatePassword',
    method: 'post',
    data: data,
    headers: {
      'Authorization': getToken()
    },
    urlType: 'api'
  })
}

// 获取有关联关系的所有账号，比如助教获取下面所有的子账号，而子账号也可以获取同一个助教下面的所有账号
export function getUserRelationListWithSameMainUser (params) {
  return axios({
    url: 'api/v2/user/getUserRelationListWithSameMainUser',
    method: 'get',
    params: params,
    urlType: 'api',
    headers: {
      'Authorization': getToken()
    }
  })
}

// 账号切换,获取新账号的授权token
export function switchAccount (params) {
  return axios({
    url: '/api/v2/user/switchAccount',
    method: 'post',
    params,
    urlType: 'api',
    headers: {
      'Authorization': getToken()
    }
  })
}

//  数据中心
export function getUserInfo () {
  return axios({
    url: 'center/user/info',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    urlType: 'admin'
  })
}

export function castingShareFile (data, params) {
  return axios({
    url: '/api/v2/comm/vt/castingShareFile',
    method: 'post',
    data,
    params,
    urlType: 'api',
    headers: {
      'Authorization': getToken()
    }
  })
}

export function getCastingShareFileList (params) {
  return axios({
    url: '/api/v2/comm/vt/getCastingShareFileList',
    method: 'get',
    params,
    urlType: 'api',
    headers: {
      'Authorization': getToken()
    }
  })
}
