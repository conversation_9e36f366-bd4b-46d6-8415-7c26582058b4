import axios from '@/utils/request'
import {
  getToken
} from '@/utils/auth'

/**
 * 获取用户AI课程包某单元答题结果汇总信息
 * @param {number} studentCourseId
 * @param {number} aicourseUnitId
 * @returns
 */
export function getAicourseUnitUser ({
  studentCourseId,
  aicourseUnitId
}) {
  return axios({
    url: 'api/v2/aicourse/getAicourseUnitUser',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params: {
      studentCourseId,
      aicourseUnitId
    },
    urlType: 'api'
  })
}

/**
 * 答题,返回正确答案
 * @param {number} studentCourseId
 * @param {number}  aicourseUnitId
 * @param {number} questionId
 * @param {number} answer
 * @param {number} times
 * @returns 用户ai课单元信息表
 */
export function updateAicourseUserAnswer ({
  studentCourseId,
  aicourseUnitId,
  questionId,
  answer,
  times,
  token,
  aicourseUnitSectionId
}) {
  var formData = new FormData()
  formData.append('studentCourseId', studentCourseId)
  formData.append('aicourseUnitId', aicourseUnitId)
  formData.append('questionId', questionId)
  formData.append('answer', answer)
  formData.append('times', times)
  formData.append('aicourseUnitSectionId', aicourseUnitSectionId)
  return axios({
    url: 'api/v2/aicourse/vt/updateAicourseUserAnswer',
    method: 'post',
    headers: {
      'Authorization': token || getToken()
    },
    data: formData,
    urlType: 'api'
  })
}

/**
 * 更新ai课单元的进度
 * @param {number} studentCourseId
 * @param {number} aicourseUnitId
 * @param {number} aicourseUnitSectionId
 * @returns 用户ai课单元信息表
 */
export function updateAicourseUnitUserProgress ({
  studentCourseId,
  aicourseUnitId,
  aicourseUnitSectionId,
  token
}) {
  var formData = new FormData()
  formData.append('studentCourseId', studentCourseId)
  formData.append('aicourseUnitId', aicourseUnitId)
  formData.append('aicourseUnitSectionId', aicourseUnitSectionId)
  return axios({
    url: 'api/v2/aicourse/updateAicourseUnitUserProgress',
    method: 'post',
    headers: {
      'Authorization': token || getToken()
    },
    data: formData,
    urlType: 'api'
  })
}

/**
 * 获取AI课程包信息
 * @param {number} aicourseId
 * @returns
 */
export function getAiCourse (params) {
  return axios({
    url: 'api/v2/aicourse/vt/getAiCourse',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getStudentCourseById (params) {
  return axios({
    url: 'api/v2/lesson/getStudentCourseById',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getStudentCourseList (params) {
  return axios({
    url: '/api/v2/lesson/getStudentCourseList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getUserCompletedAicourseUnitList (params) {
  return axios({
    url: 'api/v2/aicourse/getUserCompletedAicourseUnitList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

/**
 * 获取AI课程包单元列表信息
 * @param {number} aicourseId
 * @returns
 */
export function getAiCourseUnitList ({
  aicourseId,
  studentCourseId,
  token
}) {
  const t = token || getToken()
  return axios({
    url: 'api/v2/aicourse/vt/getAiCourseUnitList',
    method: 'get',
    headers: {
      'Authorization': t
    },
    params: {
      aicourseId,
      studentCourseId
    },
    urlType: 'api'
  })
}

/**
 * 获取AI课程包个环节模块信息
 * @param {number} aicourseUnitSectionId
 * @returns
 */
export function getAiCourseUnitSection (params) {
  return axios({
    url: 'api/v2/aicourse/vt/getAiCourseUnitSection',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

/**
 * 获取AI课程包单元包信息
 * @param {number} aicourseUnitId
 * @returns
 */
export function getAiCourseUnitSectionList ({
  aicourseUnitId,
  studentCourseId
}) {
  return axios({
    url: 'api/v2/aicourse/vt/getAiCourseUnitSectionList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params: {
      aicourseUnitId,
      studentCourseId
    },
    urlType: 'api'
  })
}

/**
 * 获取用户答题结果
 * @param {number} questionId
 * @param {string} questionSource
 * @param {string} sourceId
 * @returns
 */
export function getUserAnswer ({
  questionId,
  questionSource,
  sourceId,
  userId,
  aiCourseUnitSectionId
}) {
  return axios({
    url: 'api/v2/question/vt/getUserAnswer',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params: {
      questionId,
      questionSource,
      sourceId,
      userId,
      aiCourseUnitSectionId
    },
    urlType: 'api'
  })
}

/**
 * 获取ai互动课首页用户详情
 * @returns
 */
export function getUserInfo (params) {
  return axios({
    url: 'api/v2/user/vt/getUserInfo',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

/**
 * 获取ai互动课首页宾果数
 * @returns
 */
export function getWallet () {
  return axios({
    url: 'api/v2/wallet/getWallet',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    urlType: 'api'
  })
}

/**
 * 获取ai互动课排名
 * @returns
 */
export function getAiCourseRank (params) {
  return axios({
    url: 'api/v2/aicourse/getAiCourseRank',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

/**
 * 获取ai互动课我的排名
 * @returns
 */
export function getUserAiCourseRank (params) {
  return axios({
    url: 'api/v2/aicourse/getUserAiCourseRank',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getVirtualCharacterList () {
  return axios({
    url: 'api/v2/aicourse/vt/getVirtualCharacterList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    urlType: 'api'
  })
}

export function virtualCharacterInteractionList ({
  virtualCharacterId,
  aiCourseId
}) {
  return axios({
    url: 'api/v2/aicourse/vt/virtualCharacterInteractionList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params: {
      'virtualCharacterId': virtualCharacterId,
      'aiCourseId': aiCourseId
    },
    urlType: 'api'
  })
}

export function getSecondsConfig () {
  return axios({
    url: 'api/v1/dictionary/getConfig',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params: {
      'configType': 'AI_COURSE_QUESTION_STAY_SECONDS'
    },
    urlType: 'api'
  })
}

export function getConfig (params) {
  return axios({
    url: 'api/v1/dictionary/getConfig',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params: params,
    urlType: 'api'
  })
}

export function getAiCourseUnitInfo ({
  aicourseUnitId,
  studentCourseId
}) {
  return axios({
    url: 'api/v2/aicourse/vt/getAiCourseUnitInfo',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params: {
      aicourseUnitId,
      studentCourseId
    },
    urlType: 'api'
  })
}

export function reportUserInteraction ({
  interactionId
}) {
  return axios({
    url: '/api/v2/aicourse/vt/reportUseInterAction',
    method: 'get',
    params: {
      interactionId
    },
    urlType: 'api'
  })
}

export function ignoreInteraction ({
  aiCourseUnitId,
  studentCourseId
}) {
  return axios({
    url: '/api/v2/aicourse/ignoreInterAction',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    params: {
      aiCourseUnitId,
      studentCourseId
    },
    urlType: 'api'
  })
}

export function finishInteraction ({
  aiCourseUnitId,
  studentCourseId
}) {
  return axios({
    url: '/api/v2/aicourse/finishInterAction',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    params: {
      aiCourseUnitId,
      studentCourseId
    },
    urlType: 'api'
  })
}

export function getUserAicourseVideoList (params) {
  return axios({
    url: '/api/v2/aicourse/getUserAicourseVideoList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getUserLotteryInfo (params) {
  return axios({
    url: '/api/v2/user/getUserInfo',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function saveCoursewareRemark (params) {
  return axios({
    url: '/api/v2/aicourse/saveCoursewareRemark',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getLessonGroup (params) {
  return axios({
    url: '/api/v2/lesson/vt/getLessonGroup',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function groupLesson (params) {
  return axios({
    url: '/api/v2/lesson/vt/groupLesson',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    data: params,
    urlType: 'api'
  })
}

export function getAiCourseWorkInfo (params) {
  return axios({
    url: '/api/v2/aicourse/vt/getAiCourseWorkInfo',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function changeGroupScore (params) {
  return axios({
    url: '/api/v2/lesson/vt/changeGroupScore',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getAiCourseWorkList (params) {
  return axios({
    url: '/api/v2/aicourse/vt/getAiCourseWorkList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function aiLessonRank (params) {
  return axios({
    url: '/api/v2/aicourse/vt/aiLessonRank',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}
export function markAiCourseWork (params) {
  return axios({
    url: 'api/v2/aicourse/vt/markAiCourseWork',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getAicourseUnitUser2 (params) {
  return axios({
    url: '/api/v2/aicourse/getAicourseUnitUser',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function addCourseToClass (params) {
  return axios({
    url: '/api/v2/course/vt/addCourseToClass',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getSchoolCourseList (params) {
  return axios({
    url: '/api/v2/course/getSchoolCourseList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getUserClassList (params) {
  return axios({
    url: '/api/v2/user/getUserClassList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getGameInfo (params) {
  return axios({
    url: 'api/v2/aicourse/vt/getGameInfo',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getAiCourseBook (params) {
  return axios({
    url: 'api/v2/aicourse/vt/getAiCourseBook',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}
export function getFileUploadAuthor (params) {
  return axios({
    url: '/api/v2/comm/vt/getFileUploadAuthor',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getTrainingPresetFile (params) {
  return axios({
    url: '/api/v1/training/vt/setTrainingPresetFile',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}
