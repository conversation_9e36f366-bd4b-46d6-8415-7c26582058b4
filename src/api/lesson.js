import axios from '@/utils/request'
import {
  getToken
} from '@/utils/auth'

export function getLessonListAllType(params) {
  return axios({
    url: 'api/v2/lesson/getLessonListAllType',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getUserAicourseUnitList(params) {
  return axios({
    url: 'api/v2/aicourse/getUserAicourseUnitList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function updateVideoGoodsUnitProgress(data) {
  var formData = new FormData()
  formData.append('studentsCourseId', data.studentsCourseId)
  formData.append('videoGoodsUnitId', data.videoGoodsUnitId)
  formData.append('progress', data.progress)
  return axios({
    url: 'api/v2/videoGoods/updateVideoGoodsUnitProgress',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    data: formData,
    urlType: 'api'
  })
}

export function getStudentCourseList(params) {
  return axios({
    url: 'api/v2/lesson/getStudentCourseList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getCourseLessonPlanList(params) {
  return axios({
    url: 'api/v2/course/getCourseLessonPlanList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}
export function getStudentCourseById(params) {
  return axios({
    url: 'api/v2/lesson/getStudentCourseById',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}
export function getSubscribeTeacherList(params) {
  return axios({
    url: 'api/v1/students/teacherList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getOngoingMainAicourseUnitList(params) {
  return axios({
    url: 'api/v2/course/getOngoingMainAicourseUnitList',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getTeacherListByCourseId(params) {
  return axios({
    url: 'api/v1/students/teacherListByCourseId',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function getTeacherAvailableTime(params) {
  return axios({
    url: 'api/v1/students/getTeacherAvailableTime',
    method: 'get',
    headers: {
      'Authorization': getToken()
    },
    params,
    urlType: 'api'
  })
}

export function scheduleTeacher(data) {
  var formData = new FormData()
  formData.append('teacherId', data.teacherId)
  formData.append('studentCourseId', data.studentCourseId)
  formData.append('startTime', data.startTime)
  return axios({
    url: 'api/v2/lesson/scheduleTeacher',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    data: formData,
    urlType: 'api'
  })
}

export function exchangeCourse(data) {
  var formData = new FormData()
  formData.append('exchangeCode', data.exchangeCode)
  return axios({
    url: 'api/v2/course/exchangeCourse',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    data: formData,
    urlType: 'api'
  })
}

export function cancelScheduledLesson(data) {
  return axios({
    url: 'api/v1/students/cancelScheduledLesson',
    method: 'post',
    headers: {
      'Authorization': getToken()
    },
    data,
    urlType: 'api'
  })
}