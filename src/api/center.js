import axios from '@/utils/request'
import { getAdminToken } from '@/utils/auth'

//  获取组织树状列表
export function getOrganizationList() {
  return axios({
    url: 'center/acceptor/getOrganizationList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    urlType: 'admin'
  })
}

//  获取组织树状列表
export function getSchoolList(formData) {
  return axios({
    url: 'center/acceptor/getSchoolList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  数据概览
export function dataOverview(formData) {
  return axios({
    url: 'center/screen/dataOverview',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  课程分布
export function packageDistribute(formData) {
  return axios({
    url: 'center/screen/packageDistribute',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  课程使用情况
export function packageUsage(formData) {
  return axios({
    url: 'center/screen/packageUsage',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  学校上课情况
export function schoolBarList(formData) {
  return axios({
    url: 'center/screen/schoolBarList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  上课趋势
export function lessonTrend(formData) {
  return axios({
    url: 'center/screen/lessonTrend',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  上课趋势
export function highlightReplaysList(formData) {
  return axios({
    url: 'center/lesson/highlightReplaysList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  学校上课活跃度
export function schoolTableList(formData) {
  return axios({
    url: 'center/screen/schoolTableList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  班级上课活跃度
export function classTableList(formData) {
  return axios({
    url: 'center/screen/classTableList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

//  获取最老学年
export function getStartYear(formData) {
  return axios({
    url: 'center/acceptor/getStartYear',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

// 老师列表
export function getTeacherList(formData) {
  return axios({
    url: '/center/teacher/index',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

// 老师详情列表
export function getTeacherInfo(formData) {
  return axios({
    url: '/center/teacher/info',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

// 获取直播列表
export function getLiveClassList(formData) {
  return axios({
    url: '/center/lesson/realTimeLessonList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}