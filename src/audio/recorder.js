class RecorderService {
  constructor () {
    this._mediaRecorder = null
    this._audioStream = null
    this._audioContext = null
    this._scriptProcessor = null
    this._audioInput = null
    this._dataHandlers = []
    this._stopHandlers = []
    this._eventHandlers = {}
    this._stopped = false
  }

  start (options = {}) {
    this._stopped = false
    const { mode = 'webm', mimeType = 'audio/webm', audioBitsPerSecond, chunkMs = 250, sampleRate = 16000 } = options
    if (this._mediaRecorder || this._audioContext) return

    if (mode === 'pcm') {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.error('[Recorder] 浏览器不支持麦克风访问')
        throw new Error('浏览器不支持麦克风访问，请使用最新版 Chrome/Edge 并确保 HTTPS 环境')
      }

      navigator.mediaDevices.getUserMedia({ audio: true }).then(stream => {
        this._audioStream = stream
        this._audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate })
        this._audioInput = this._audioContext.createMediaStreamSource(stream)
        this._scriptProcessor = this._audioContext.createScriptProcessor(2048, 1, 1)
        this._scriptProcessor.onaudioprocess = event => {
          if (this._stopped) return
          const inputData = event.inputBuffer.getChannelData(0)
          const pcm16 = new Int16Array(inputData.length)
          for (let i = 0; i < inputData.length; ++i) {
            const s = Math.max(-1, Math.min(1, inputData[i]))
            pcm16[i] = s < 0 ? s * 0x8000 : s * 0x7FFF
          }
          const buffer = pcm16.buffer
          this._dataHandlers.forEach(h => h(buffer))
          this.emit('data', buffer)
        }
        this._audioInput.connect(this._scriptProcessor)
        this._scriptProcessor.connect(this._audioContext.destination)
        this.emit('start')
      }).catch(err => {
        console.error('[Recorder] 麦克风权限获取失败', err)
        throw new Error('麦克风权限获取失败，请检查浏览器设置并允许麦克风访问')
      })
      return
    }

    navigator.mediaDevices.getUserMedia({ audio: true }).then(stream => {
      this._audioStream = stream
      this._mediaRecorder = new window.MediaRecorder(stream, { mimeType, audioBitsPerSecond })
      this._mediaRecorder.ondataavailable = e => {
        if (this._stopped) return
        this._dataHandlers.forEach(h => h(e.data))
        this.emit('data', e.data)
      }
      this._mediaRecorder.onstop = () => {
        this._stopHandlers.forEach(h => h())
        this.emit('stop')
        this._mediaRecorder = null
        if (this._audioStream) {
          this._audioStream.getTracks().forEach(track => track.stop())
          this._audioStream = null
        }
      }
      this._mediaRecorder.start(chunkMs)
      this.emit('start')
    })
  }

  stop () {
    this._stopped = true
    if (this._mediaRecorder) {
      this._mediaRecorder.stop()
      this._mediaRecorder = null
    }
    if (this._scriptProcessor) {
      this._scriptProcessor.disconnect()
      this._scriptProcessor = null
    }
    if (this._audioInput) {
      this._audioInput.disconnect()
      this._audioInput = null
    }
    if (this._audioContext) {
      this._audioContext.close()
      this._audioContext = null
    }
    if (this._audioStream) {
      this._audioStream.getTracks().forEach(track => track.stop())
      this._audioStream = null
    }
    this.emit('stop')
    this._dataHandlers.length = 0
    this._stopHandlers.length = 0
  }

  onData (handler) {
    this._dataHandlers.push(handler)
  }

  offData (handler) {
    this._dataHandlers = this._dataHandlers.filter(h => h !== handler)
  }

  onStop (handler) {
    this._stopHandlers.push(handler)
  }

  on (event, handler) {
    if (!this._eventHandlers[event]) this._eventHandlers[event] = []
    this._eventHandlers[event].push(handler)
  }

  off (event, handler) {
    if (!this._eventHandlers[event]) return
    this._eventHandlers[event] = this._eventHandlers[event].filter(h => h !== handler)
  }

  emit (event, ...args) {
    if (this._eventHandlers[event]) {
      this._eventHandlers[event].forEach(h => h(...args))
    }
  }
}

export default RecorderService
