import AudioPlayer from './audioPlayer'
import AliCloudTokenManager from '@/services/alicloud/token'

const generatePayload = () => {
  return {
    voice: 'zhixiaoxia',
    format: 'PCM',
    sample_rate: 24000,
    volume: 100,
    speech_rate: 0,
    pitch_rate: 0,
    enable_subtitle: true,
    platform: 'javascript'
  }
}

const generateUUID = () => {
  let d = new Date().getTime()
  let d2 = (performance && performance.now && (performance.now() * 1000)) || 0
  return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16
    if (d > 0) {
      r = (d + r) % 16 | 0
      d = Math.floor(d / 16)
    } else {
      r = (d2 + r) % 16 | 0
      d2 = Math.floor(d2 / 16)
    }
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
}

class TTSService {
  constructor (options = {}) {
    this._ws = null
    this._wsState = 'closed'
    this._isSynthesisStarted = false
    this._pingInterval = null
    this._currentTaskId = null
    this.token = null
    this.appkey = null
    this.player = new AudioPlayer(options.sampleRate || 24000)
    this._lastPingTime = 0
    this._pingCount = 0
    this._textQueue = []
    this._isProcessing = false
    this._waitingForStart = false
    this._lastSentenceIndex = -1
    this._reconnectAttempts = 0
    this._maxReconnectAttempts = 3
    this._reconnectDelay = 1000
    this._isReconnecting = false
    this._lastErrorTime = 0
    this._errorCount = 0
    this._connectionPromise = null
    this._queueLoopRunning = false

    this.onMessage = null
    this.onData = null
    this.onEnd = null
    this.onError = null
  }

  async _ensureToken () {
    if (this.token && this.appkey) {
      return true
    }
    try {
      const { token, appkey } = await AliCloudTokenManager.getToken()
      if (token && appkey) {
        this.token = token
        this.appkey = appkey
        return true
      }
      throw new Error('Invalid token data')
    } catch (error) {
      if (this.onError) {
        this.onError(error)
      }
      return false
    }
  }

  _getHeader (name) {
    return {
      message_id: generateUUID(),
      task_id: this._currentTaskId,
      namespace: 'FlowingSpeechSynthesizer',
      name: name,
      appkey: this.appkey
    }
  }

  async connect () {
    if (!(await this._ensureToken())) {
      return false
    }

    if (this._ws) {
      this.disconnect()
    }

    return new Promise((resolve, reject) => {
      const wsUrl = `wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1?token=${this.token}`
      this._ws = new WebSocket(wsUrl)
      this._ws.binaryType = 'arraybuffer'
      this._wsState = 'connecting'
      this._waitingForStart = true

      const setupTimeout = setTimeout(() => {
        this.disconnect()
        reject(new Error('Connection timeout'))
      }, 10000)

      this._ws.onopen = () => {
        clearTimeout(setupTimeout)
        this._wsState = 'open'

        const taskId = generateUUID()
        this._currentTaskId = taskId
        this.player.setTaskId(taskId)
        this.player.setReadyState(true)

        const header = this._getHeader('StartSynthesis')
        const params = {
          header: header,
          payload: generatePayload()
        }
        this._ws.send(JSON.stringify(params))
        this._lastPingTime = Date.now()
      }

      this._ws.onclose = () => {
        clearTimeout(setupTimeout)
        this._resetState()
      }

      this._ws.onerror = (event) => {
        clearTimeout(setupTimeout)
        reject(new Error('connection error'))
      }

      this._ws.onmessage = this._handleMessage.bind(this)
    })
  }

  _handleMessage (event) {
    const data = event.data
    if (data instanceof ArrayBuffer) {
      this.player.pushPCM(data, this._currentTaskId)
    } else {
      const body = JSON.parse(data)

      if (body.header.name === 'SynthesisStarted' && body.header.status === 20000000) {
        this._isSynthesisStarted = true
        this._waitingForStart = false
        this.startPing()
        this._startQueueLoop()
      }

      if (body.header.name === 'TaskFailed') {
        if (body.header.status === 40000004) {
          console.log('IDLE_TIMEOUT')
        }
      }
    }
  }

  async sendText (text) {
    this._textQueue.push(text)
    this._startQueueLoop()
    return true
  }

  _startQueueLoop () {
    this._queueLoopRunning = true
    void this._processQueueLoop()
  }

  async _processQueueLoop () {
    while (this._queueLoopRunning) {
      if (!this._ws || this._wsState !== 'open' || !this._isSynthesisStarted) {
        await this._waitForConnection()
        continue
      }
      if (this._textQueue.length === 0) {
        await this._sleep(200)
        continue
      }
      const text = this._textQueue.shift()
      if (text) {
        try {
          const params = {
            header: this._getHeader('RunSynthesis'),
            payload: { text: text }
          }
          this._ws.send(JSON.stringify(params))
          this._lastPingTime = Date.now()
          await this._sleep(300)
        } catch (error) {
          if (this.onError) this.onError(error)
          this._textQueue.unshift(text)
          await this._sleep(500)
        }
      }
    }
  }

  async _waitForConnection () {
    let waited = 0
    const timeout = 10000
    if (!this._ws || this._wsState === 'closed') {
      if (!this._connectionPromise) {
        this._connectionPromise = this.connect().catch(e => { this._connectionPromise = null })
      }
      try {
        await this._connectionPromise
      } catch (e) {
        // 连接失败，稍后重试
      }
    }
    while ((!this._ws || this._wsState !== 'open' || !this._isSynthesisStarted) && waited < timeout) {
      await this._sleep(200)
      waited += 200
    }
  }

  _sleep (ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  startPing () {
    if (this._pingInterval) {
      this.stopPing()
    }

    this._pingInterval = setInterval(async () => {
      const params = {
        header: this._getHeader('RunSynthesis'),
        payload: { text: ' ' }
      }
      this._ws.send(JSON.stringify(params))
    }, 8000)
  }

  stopPing () {
    if (this._pingInterval) {
      clearInterval(this._pingInterval)
      this._pingInterval = null
    }
  }

  _resetState () {
    this._wsState = 'closed'
    this._currentTaskId = null
    this._isSynthesisStarted = false
    this._waitingForStart = false
    this._lastSentenceIndex = -1
    this.stopPing()
    this._ws = null
    this._lastPingTime = 0
    this._reconnectAttempts = 0
    this._isReconnecting = false
    this._errorCount = 0
    this._lastErrorTime = 0
    this._connectionPromise = null
    this._isProcessing = false
    this._queueLoopRunning = false
  }

  disconnect () {
    if (this._ws) {
      this.stopPing()
      this._ws.close(1000, 'User requested disconnect')
      this._ws = null
    }
    this._queueLoopRunning = false
  }
}

export default TTSService
