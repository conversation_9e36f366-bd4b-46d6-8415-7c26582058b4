import AliCloudTokenManager from '@/services/alicloud/token'

function generateUUID () {
  return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
    (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
  ).replace(/-/g, '')
}

const DEFAULT_WS_URL = 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token='

class ASRService {
  constructor (websocketService) {
    this._wsService = websocketService
    this._resultHandlers = []
    this._eventHandlers = {}
    this._readyToSendAudio = false
    this._wsService.onMessage(this._handleMessage.bind(this))
    this._wsService.on('open', this._onWSOpen.bind(this))
    this._wsService.on('error', this._handleWSError.bind(this))
    this._wsService.on('close', this._handleWSClose.bind(this))
    this._pendingOptions = null
    this._pendingTokenAppkey = null
    this._stopped = false
    this._hasRetried = false
  }
  async start (options = {}) {
    this._readyToSendAudio = false
    this._pendingOptions = options
    this._stopped = false
    this._hasRetried = false
    let appkey, token
    if (!options.appkey || !options.token) {
      try {
        const tokenObj = await AliCloudTokenManager.getToken()
        appkey = tokenObj.appkey
        token = tokenObj.token
      } catch (e) {
        console.error('[ASRService]  error', e)
        return
      }
    } else {
      appkey = options.appkey
      token = options.token
    }
    this._pendingTokenAppkey = { appkey, token }
    let wsUrl = options.url
    if (!wsUrl && token) {
      wsUrl = DEFAULT_WS_URL + encodeURIComponent(token)
    }
    if (this._wsService) this._wsService.connect(wsUrl, options.wsOptions)
    this.emit('start')
  }
  _onWSOpen () {
    const options = this._pendingOptions || {}
    const tokenAppkey = this._pendingTokenAppkey || {}
    const appkeyToUse = tokenAppkey.appkey || options.appkey
    if (!appkeyToUse) return
    const startMsg = {
      header: {
        appkey: appkeyToUse,
        namespace: 'SpeechTranscriber',
        name: 'StartTranscription',
        task_id: generateUUID(),
        message_id: generateUUID()
      },
      payload: {
        format: 'pcm',
        sample_rate: 16000,
        enable_intermediate_result: true,
        enable_punctuation_prediction: true,
        enable_inverse_text_normalization: true
      }
    }
    this._wsService.send(JSON.stringify(startMsg))
  }
  _handleWSError (err) {
    console.error('[ASRService] WebSocket error', err)
  }
  async _handleWSClose (event) {
    const reason = event && (event.reason || event.message || '')
    const code = event && event.code
    const isTokenExpired = (code === 4001 || code === 401 || (reason && reason.toLowerCase().includes('token')))
    if (isTokenExpired && !this._hasRetried) {
      this._hasRetried = true
      try {
        const tokenObj = await AliCloudTokenManager.refreshToken()
        this._pendingTokenAppkey = { appkey: tokenObj.appkey, token: tokenObj.token }
        let wsUrl = this._pendingOptions.url
        if (!wsUrl && tokenObj.token) {
          wsUrl = DEFAULT_WS_URL + encodeURIComponent(tokenObj.token)
        }
        if (this._wsService) this._wsService.connect(wsUrl, this._pendingOptions.wsOptions)
        this.emit('start')
        return
      } catch (e) {
        console.error('[ASRService] refresh token error', e)
      }
    }
  }
  feedAudioChunk (chunk) {
    if (this._wsService && this._readyToSendAudio) {
      this._wsService.send(chunk)
    }
  }
  stop () {
    if (this._wsService) this._wsService.disconnect()
    this.emit('stop')
    this._stopped = true
    this._resultHandlers.length = 0
  }
  onResult (handler) {
    this._resultHandlers.push(handler)
  }
  _handleMessage (msg) {
    if (this._stopped) return
    let data
    try {
      data = JSON.parse(msg)
    } catch (e) {
      return
    }
    const eventName = data.header && data.header.name
    if (eventName === 'TranscriptionStarted') {
      this._readyToSendAudio = true
      this.emit('transcriptionStarted')
    }
    if (data.payload && data.payload.result) {
      this._resultHandlers.forEach(h => h({
        eventName: eventName,
        payload: data.payload
      }))
      this.emit('result', {
        eventName: eventName,
        payload: data.payload
      })
    }
  }
  on (event, handler) {
    if (!this._eventHandlers[event]) this._eventHandlers[event] = []
    this._eventHandlers[event].push(handler)
  }
  off (event, handler) {
    if (!this._eventHandlers[event]) return
    this._eventHandlers[event] = this._eventHandlers[event].filter(h => h !== handler)
  }
  emit (event, ...args) {
    if (this._eventHandlers[event]) {
      this._eventHandlers[event].forEach(h => h(...args))
    }
  }
  clearResultHandlers () {
    this._resultHandlers.length = 0
  }
}

export default ASRService
