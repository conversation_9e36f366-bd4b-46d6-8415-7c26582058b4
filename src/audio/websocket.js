class WebSocketService {
  constructor () {
    this._ws = null
    this._messageHandlers = []
    this._eventHandlers = {}
  }

  connect (url, options = {}) {
    if (this._ws && this._ws.readyState === 1) return
    this._ws = new window.WebSocket(url)
    this._ws.onopen = () => this.emit('open')
    this._ws.onclose = () => {
      this.emit('close')
      this._ws = null
    }
    this._ws.onerror = err => this.emit('error', err)
    this._ws.onmessage = e => {
      this._messageHandlers.forEach(h => h(e.data))
      this.emit('message', e.data)
    }
  }

  disconnect () {
    if (this._ws) {
      this._ws.close()
      this._ws = null
    }
  }

  send (data) {
    if (this._ws && this._ws.readyState === 1) this._ws.send(data)
  }

  getState () {
    return {
      readyState: this._ws ? this._ws.readyState : -1,
      connected: this._ws && this._ws.readyState === 1
    }
  }

  onMessage (handler) {
    this._messageHandlers.push(handler)
  }

  on (event, handler) {
    if (!this._eventHandlers[event]) this._eventHandlers[event] = []
    this._eventHandlers[event].push(handler)
  }

  off (event, handler) {
    if (!this._eventHandlers[event]) return
    this._eventHandlers[event] = this._eventHandlers[event].filter(h => h !== handler)
  }

  emit (event, ...args) {
    if (this._eventHandlers[event]) {
      this._eventHandlers[event].forEach(h => h(...args))
    }
  }
}

export default WebSocketService
