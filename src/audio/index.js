import RecorderService from './recorder'
import WebSocketService from './websocket'
import ASRService from './asr'
import TTSService from './ttsService'

class AudioService {
  constructor () {
    this._instanceId = Date.now()
    this.recorder = new RecorderService()
    this.wsService = new WebSocketService()
    this.asrService = new ASRService(this.wsService)
    this._asrResultHandlers = []
    this._bindASREvents()
    this._ttsService = new TTSService()
    this._accumulatedText = ''
    this._isConnected = false
    this._isSynthesisStarted = false
  }
  _bindASREvents () {
    this._boundResultHandler = result => {
      this._asrResultHandlers.forEach(h => h(result))
      this.emit && this.emit('asrResult', result)
    }
    this.asrService.on('result', this._boundResultHandler)
  }
  async startRecordASR () {
    console.log('[AudioService] 开始录音识别')
    try {
      await this.asrService.start({})
      this._boundTranscriptionStartedHandler = () => {
        console.log('[AudioService] 开始录音')
        this.recorder.start({ mode: 'pcm', sampleRate: 16000 })
      }
      this.asrService.on('transcriptionStarted', this._boundTranscriptionStartedHandler)
      this._boundDataHandler = buffer => {
        this.asrService.feedAudioChunk(buffer)
      }
      this.recorder.onData(this._boundDataHandler)
    } catch (e) {
      console.error('[AudioService] 启动录音识别失败', e)
    }
  }
  stopRecordASR () {
    console.log('[AudioService] 停止录音识别')
    if (this._boundTranscriptionStartedHandler) {
      this.asrService.off('transcriptionStarted', this._boundTranscriptionStartedHandler)
      this._boundTranscriptionStartedHandler = null
    }
    if (this._boundDataHandler) {
      this.recorder.offData && this.recorder.offData(this._boundDataHandler)
      this._boundDataHandler = null
    }
    this.recorder.stop()
    this.asrService.stop()
  }
  onASRResult (handler) {
    if (!this._asrResultHandlers.includes(handler)) {
      this._asrResultHandlers.push(handler)
    }
  }
  offASRResult (handler) {
    this._asrResultHandlers = this._asrResultHandlers.filter(h => h !== handler)
  }

  on (event, handler) {
    if (!this._handlers) this._handlers = {}
    if (!this._handlers[event]) this._handlers[event] = []
    this._handlers[event].push(handler)
  }
  off (event, handler) {
    if (!this._handlers || !this._handlers[event]) return
    this._handlers[event] = this._handlers[event].filter(h => h !== handler)
  }
  emit (event, ...args) {
    if (this._handlers && this._handlers[event]) {
      this._handlers[event].forEach(h => h(...args))
    }
  }

  startRecord (options) {}
  stopRecord () {}
  startASR (audioData) {}
  stopASR () {}
  startTTS (text) {}
  stopTTS () {
    if (this._ttsService) {
      this._ttsService.disconnect()
      this._ttsService.player.stop()
    }
  }
  playAudio (audioBufferOrUrl) {}
  pauseAudio () {}
  stopAudio () {}

  feedTextFragment (fragmentObj) {
    this._accumulatedText += fragmentObj.result

    if (fragmentObj.is_end) {
      this._ttsService.sendText(this._accumulatedText)
      this._accumulatedText = ''
    } else {
      if (fragmentObj.result.endsWith('\n')) {
        const textFragments = this._splitTextBySentence(this._accumulatedText)
        const totalFragments = textFragments.length
        for (let i = 0; i < totalFragments; i++) {
          const frag = textFragments[i]
          this._ttsService.sendText(frag)
        }
        this._accumulatedText = ''
      }
    }
  }

  _splitTextBySentence (text) {
    return text.match(/[^。！？\n]+[。！？\n]?/g) || [text]
  }

  disconnect () {
    if (this._ttsService) {
      this._ttsService.disconnect()
    }
  }
}

export default new AudioService()
