class AudioPlayer {
  constructor (sampleRate = 24000) {
    this.sampleRate = sampleRate
    this.audioContext = null
    this.audioQueue = []
    this.isPlaying = false
    this.currentSource = null
    this.currentTaskId = null
    this.isReadyState = false
  }

  setTaskId (taskId) {
    if (this.currentTaskId !== taskId) {
      this.currentTaskId = taskId
      this.stop()
    }
  }
  setReadyState (isReady) {
    this.isReadyState = isReady
  }
  connect () {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
    }
  }

  pushPCM (arrayBuffer, taskId) {
    this.connect()
    if (!this.isReadyState) { return }
    if (taskId && taskId !== this.currentTaskId) {
      return
    }
    this.audioQueue.push(arrayBuffer)
    this._playNextAudio()
  }

  _bufferPCMData (pcmData) {
    const sampleRate = this.sampleRate
    const length = pcmData.byteLength / 2
    const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate)
    const channelData = audioBuffer.getChannelData(0)
    const int16Array = new Int16Array(pcmData)
    for (let i = 0; i < length; i++) {
      channelData[i] = int16Array[i] / 32768
    }
    return audioBuffer
  }

  async _playAudio (arrayBuffer) {
    try {
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }
      const audioBuffer = this._bufferPCMData(arrayBuffer)
      this.currentSource = this.audioContext.createBufferSource()
      this.currentSource.buffer = audioBuffer
      this.currentSource.connect(this.audioContext.destination)
      this.currentSource.onended = () => {
        this.isPlaying = false
        this.currentSource = null
        this._playNextAudio()
      }
      this.currentSource.start()
      this.isPlaying = true
    } catch (e) {
      this.isPlaying = false
      this.currentSource = null
      this._playNextAudio()
    }
  }

  _playNextAudio () {
    if (this.audioQueue.length > 0 && !this.isPlaying) {
      const buffer = this.audioQueue.shift()
      this._playAudio(buffer)
    }
  }
  disconnect () {
    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }
  }
  stop () {
    if (this.currentSource) {
      this.currentSource.stop()
      this.currentSource = null
      this.isPlaying = false
      this.setReadyState(false)
    }
    this.disconnect()
    this.audioQueue = []
  }
}

export default AudioPlayer
