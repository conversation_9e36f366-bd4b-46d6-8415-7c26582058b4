import AliCloudTokenManager from '@/services/alicloud/token'
import AudioPlayer from './audioPlayer'

const generateUUID = () => {
  let d = new Date().getTime()
  let d2 = (performance && performance.now && (performance.now() * 1000)) || 0
  return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16
    if (d > 0) {
      r = (d + r) % 16 | 0
      d = Math.floor(d / 16)
    } else {
      r = (d2 + r) % 16 | 0
      d2 = Math.floor(d2 / 16)
    }
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
}

const generatePayload = () => {
  return {
    voice: 'zhixiaoxia',
    format: 'PCM',
    sample_rate: 24000,
    volume: 100,
    speech_rate: 0,
    pitch_rate: 0,
    enable_subtitle: true,
    platform: 'javascript'
  }
}

class TTSService {
  constructor (options = {}) {
    this.player = new AudioPlayer(options.sampleRate || 24000)
    this.token = null
    this.appkey = null
    this._ws = null
    this._wsState = 'closed'
    this._currentTaskId = null
    this._isSynthesisStarted = false
    this._pingInterval = null

    this.onMessage = null
    this.onData = null
    this.onEnd = null
    this.onError = null
  }

  _getHeader (name) {
    const header = {
      message_id: generateUUID(),
      task_id: this._currentTaskId,
      namespace: 'FlowingSpeechSynthesizer',
      name: name,
      appkey: this.appkey
    }
    return header
  }

  async _ensureToken () {
    if (this.token && this.appkey) {
      return true
    }
    console.log('[TTSService] [Token] Fetching new token...')
    try {
      const { token, appkey } = await AliCloudTokenManager.getToken()
      if (token && appkey) {
        this.token = token
        this.appkey = appkey
        console.log('[TTSService] [Token] New token fetched successfully appkey ', appkey, '+token ', token)
        return true
      }
      throw new Error('Invalid token data')
    } catch (error) {
      console.error('[TTSService] [Token] Error:', error)
      if (this.onError) {
        this.onError(error)
      }
      return false
    }
  }

  sendRunSynthesis (text) {
    if (!this._ws || !this._isSynthesisStarted) {
      console.log('[TTSService] RunSynthesis failed: WebSocket not ready or synthesis not started')
      return false
    }

    const params = {
      header: this._getHeader('RunSynthesis'),
      payload: { text }
    }
    this._ws.send(JSON.stringify(params))
    return true
  }

  async sendStopSynthesis () {
    if (!this._ws || !this._isSynthesisStarted) {
      console.log('[TTSService] StopSynthesis failed: WebSocket not ready or synthesis not started')
      return false
    }

    const stopParams = { header: this._getHeader('StopSynthesis') }
    this._ws.send(JSON.stringify(stopParams))
    console.log('[TTSService] 发送 StopSynthesis 指令')
    return true
  }

  _splitTextBySentence (text) {
    return text.match(/[^。！？\n]+[。！？\n]?/g) || [text]
  }

  async connectAndStartSynthesis (text) {
    if (!(await this._ensureToken())) {
      return false
    }

    if (this._ws) {
      this.stop()
    }

    const taskId = generateUUID()
    this._currentTaskId = taskId
    this.player.setTaskId(taskId)

    return new Promise((resolve, reject) => {
      const wsUrl = `wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1?token=${this.token}`
      this._ws = new WebSocket(wsUrl)
      this._ws.binaryType = 'arraybuffer'
      this._wsState = 'connecting'

      const setupTimeout = setTimeout(() => {
        this.stop()
        reject(new Error('Connection timeout'))
      }, 10000)

      this._ws.onopen = () => {
        clearTimeout(setupTimeout)
        this._wsState = 'open'
        console.log('[TTSService] WebSocket connected')
        this.startPing()
        if (this._ws.readyState === WebSocket.OPEN) {
          this._currentTaskId = taskId
          const header = this._getHeader('StartSynthesis')
          const params = {
            header: header,
            payload: generatePayload()
          }
          this._ws.send(JSON.stringify(params))
          resolve(true)
        }
      }

      const textFragments = this._splitTextBySentence(text)
      this._totalFragments = textFragments.length
      this._sentFragments = 0
      this._receivedSentenceEnds = 0
      this._pendingStop = false

      this._ws.onmessage = (event) => {
        const data = event.data

        if (data instanceof ArrayBuffer) {
          this.player.pushPCM(data, this._currentTaskId)
        } else {
          const body = JSON.parse(data)
          console.log('[TTSService] Message received:', JSON.stringify(body, null, 2))

          if (body.header.name === 'SynthesisStarted' && body.header.status === 20000000) {
            console.log('[TTSService] Synthesis started')
            this._isSynthesisStarted = true

            for (let i = 0; i < this._totalFragments; i++) {
              const frag = textFragments[i]
              this.sendRunSynthesis(frag)
              const preview = frag.length > 40 ? `${frag.slice(0, 20)}...${frag.slice(-20)}` : frag
              console.log(`[TTSService] 发送分片 ${i + 1}/${this._totalFragments}, 长度: ${frag.length}, 内容预览:`, preview)
              this._sentFragments++
            }

            console.log('[TTSService] 所有分片已发送完毕，发送 StopSynthesis 指令')
            this.sendStopSynthesis()
          }

          if (body.header.name === 'SentenceEnd' && body.header.status === 20000000) {
            this._receivedSentenceEnds++
            console.log(`[TTSService] 收到 SentenceEnd 事件 ${this._receivedSentenceEnds}/${this._totalFragments}`)
          }

          if (body.header.name === 'SynthesisCompleted' && body.header.status === 20000000) {
            console.log('[TTSService] 收到 Synthesis completed 事件,本次单个问题合成结束')
          }

          if (body.header.name === 'TaskFailed') {
            console.error('[TTSService] TaskFailed:', JSON.stringify(body, null, 2))
            if (body.header.status === 40000004) {
              console.log('[TTSService] 收到 TaskFailed 事件, 停止合成')
            }
          }
        }
      }

      this._ws.onerror = (event) => {
        clearTimeout(setupTimeout)
        console.error('[TTSService] WebSocket error:', event)
        const error = new Error('WebSocket connection error')
        if (this.onError) {
          this.onError(error)
        }
        reject(error)
      }

      this._ws.onclose = () => {
        clearTimeout(setupTimeout)
        console.log('[TTSService] WebSocket closed')
        this.stopPing()
        this._ws = null
        this._resetState()
      }
    })
  }

  _resetState () {
    this._wsState = 'closed'
    this._currentTaskId = null
    this._isSynthesisStarted = false
    this._totalFragments = 0
    this._sentFragments = 0
    this._receivedSentenceEnds = 0
    this._pendingStop = false
    console.log('[TTSService] websocket及TTS 重置状态...')
  }

  stop () {
    console.log('[TTSService] Stopping synthesis')

    if (this._ws) {
      if (this._wsState === 'open') {
        this.sendStopSynthesis()
      }
      this._ws.close(1000, 'User requested stop')
      this._ws = null
    }

    console.log('[TTSService] Synthesis stopped')
  }

  synthesizeAudioWith (text) {
    console.log('[TTSService] 开始收到需要合成音频的文本内容为：', text)
    return this.connectAndStartSynthesis(text)
  }

  stopWebSocket () {
    if (this._ws) {
      this._ws.close()
      this._ws = null
    }
  }

  startPing () {
    if (this._pingInterval) {
      this.stopPing()
    }

    this._pingInterval = setInterval(() => {
      if (this._ws && this._ws.readyState === WebSocket.OPEN) {
        this.sendRunSynthesis(' ')
        const now = new Date().toLocaleString()
        console.log(`[TTSService] 发送心跳 Ping，时间: ${now}`)
      }
    }, 8000)
  }

  stopPing () {
    if (this._pingInterval) {
      clearInterval(this._pingInterval)
      this._pingInterval = null
      console.log('[TTSService] Ping stopped')
    }
  }
}

export default TTSService
