<template>
  <div id="app" @contextmenu="handleContextmenu($event)">
    <router-view />
    <ElectronScreenProtection />
    <ScreenProtectionEffect ref="screenProtectionEffect" />
  </div>
</template>

<script>
import ElectronScreenProtection from '@/components/ElectronScreenProtection/index.vue'
import ScreenProtectionEffect from '@/components/ScreenProtectionEffect/index.vue'

export default {
  components: {
    ElectronScreenProtection,
    ScreenProtectionEffect
  },
  mounted () {
    // document.querySelector('.loading').style.display = 'inline'
    // document.querySelector('.loading').style.display = 'none'
  },
  methods: {
    handleContextmenu (e) {
      e.preventDefault()
      return false
    }
  }
}
</script>

<style lang="scss">
@import '@/styles/normalize.scss';
body {
  background: linear-gradient(109.39deg, #FCECFF -23.25%, #ABF6FF 97.11%);
}
</style>
