<template>
  <div id="app" @contextmenu="handleContextmenu($event)">
    <router-view />
    <ElectronScreenProtection />
    <ScreenProtectionEffect ref="screenProtectionEffect" />
  </div>
</template>

<script>
import ElectronScreenProtection from '@/components/ElectronScreenProtection/index.vue'
import ScreenProtectionEffect from '@/components/ScreenProtectionEffect/index.vue'

export default {
  components: {
    ElectronScreenProtection,
    ScreenProtectionEffect
  },
  mounted () {
    // document.querySelector('.loading').style.display = 'inline'
    // document.querySelector('.loading').style.display = 'none'

    // 为防截屏组件提供全局方法
    this.$showScreenProtection = (options) => {
      if (this.$refs.screenProtectionEffect) {
        this.$refs.screenProtectionEffect.show(options)
      }
    }
  },
  methods: {
    handleContextmenu (e) {
      e.preventDefault()
      return false
    },
    setupGlobalProtection() {
      this.$root.$showScreenProtection = (options) => {
        if (this.$refs.screenProtectionEffect) {
          this.$refs.screenProtectionEffect.show(options)
        }
      }

      this.$root.$hideScreenProtection = () => {
        if (this.$refs.screenProtectionEffect) {
          this.$refs.screenProtectionEffect.hide()
        }
      }

      this.$root.$showWatermark = (options = {}) => {
        if (this.$refs.dynamicWatermark) {
          if (Object.keys(options).length > 0) {
            this.$refs.dynamicWatermark.updateConfig(options)
          }
          this.$refs.dynamicWatermark.show()
        }
      }

      this.$root.$hideWatermark = () => {
        if (this.$refs.dynamicWatermark) {
          this.$refs.dynamicWatermark.hide()
        }
      }

      this.$root.$refreshWatermark = () => {
        if (this.$refs.dynamicWatermark) {
          this.$refs.dynamicWatermark.refresh()
        }
      }
    }
  }
}
</script>

<style lang="scss">
@import '@/styles/normalize.scss';
body {
  background: linear-gradient(109.39deg, #FCECFF -23.25%, #ABF6FF 97.11%);
}
</style>
