import Vue from 'vue'
import App from './App.vue'
import store from './store'
import router from './router'
import './styles/index.scss'
import VueLazyload from 'vue-lazyload'
import {
  Alert,
  Confirm,
  Toast,
  Loading
} from 'wc-messagebox'
import 'wc-messagebox/style.css'
import VueAwesomeSwiper from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '@/utils/mathjax'
import 'mathjax/es5/tex-mml-svg'
Vue.use(ElementUI)

import 'viewerjs/dist/viewer.css'
import VueViewer from 'v-viewer'
Vue.use(VueViewer)

import moment from '@/utils/moment'
Vue.prototype.$moment = moment

import { message } from '@/utils/singeMessage.js'
Vue.prototype.$message = message

import './permission'
Vue.use(<PERSON><PERSON>, {})
Vue.use(Confirm, {})
Vue.use(To<PERSON>, {})
Vue.use(Loading)
Vue.use(VueLazyload, {
  error: require('../public/static/lazy-load.png'),
  loading: require('../public/static/lazy-error.png')
})
Vue.use(VueAwesomeSwiper)
import JwChat from 'jwchat'
Vue.use(JwChat)
// import Vconsole from 'vconsole'

// const vConsole = new Vconsole()

// Vue.use(vConsole)

import 'animate.css'
import VueAnimateOnScroll from 'vue-animate-onscroll'
Vue.use(VueAnimateOnScroll)

import 'fullpage.js/vendors/scrolloverflow'
import VueFullPage from 'vue-fullpage.js'
Vue.use(VueFullPage)

import touchmove from '@/directives/touchmove.js'
Vue.directive('swipetouch', touchmove)

Vue.config.productionTip = false
// 全局事件总线
Vue.prototype.$bus = new Vue()
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
