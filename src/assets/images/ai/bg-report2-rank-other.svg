<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="301px" height="70px" viewBox="0 0 301 70" version="1.1">
  <title>编组 24</title>
  <defs>
    <linearGradient x1="100.984567%" y1="50%" x2="-0.792009808%" y2="49.6255954%" id="linearGradient-1">
      <stop stop-color="#7FCEFF" stop-opacity="0.652043269" offset="0%"></stop>
      <stop stop-color="#77CFFF" stop-opacity="0" offset="51.248361%"></stop>
      <stop stop-color="#70CFFF" stop-opacity="0.889040647" offset="100%"></stop>
      <stop stop-color="#70CFFF" offset="100%"></stop>
    </linearGradient>
    <linearGradient x1="-2.61494481%" y1="48.5181487%" x2="111.232567%" y2="50%" id="linearGradient-2">
      <stop stop-color="#B0EBFF" offset="0%"></stop>
      <stop stop-color="#AFF7FF" stop-opacity="0" offset="100%"></stop>
    </linearGradient>
    <polygon id="path-3" points="4.16653458e-14 5.41422216 -8.59241065e-14 62.1934458 279.325225 62.1934458 286.091319 56.0293688 286.091319 29.3380569 292.707785 23.1838014 292.707785 16.4946731 286.091319 9.59419782 72.7043886 9.59419782 59.859707 -1.01828682e-13 6.27953549 -7.6572443e-14"></polygon>
    <filter x="-2.0%" y="-6.4%" width="104.1%" height="119.3%" filterUnits="objectBoundingBox" id="filter-4">
      <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
      <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
      <feColorMatrix values="0 0 0 0 0.0242624908   0 0 0 0 0.123169137   0 0 0 0 0.271144701  0 0 0 0.105140953 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <polygon id="path-5" points="9.09131898 9.47627001 9.09131898 53.452054 10.3387807 54.8583467 59.0934325 54.8583467 60.6243807 53.4139564 60.6243807 43.6871696 66.091319 38.8031004 66.091319 24.3302162 60.6243807 19.2444084 60.6243807 9.47627001 59.3376209 7.8583467 10.5568444 7.8583467"></polygon>
    <filter x="-5.3%" y="-4.3%" width="110.5%" height="108.5%" filterUnits="objectBoundingBox" id="filter-6">
      <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.68627451   0 0 0 0 0.968627451   0 0 0 0 1  0 0 0 0.672831075 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
      <feOffset dx="-1" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
      <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.68627451   0 0 0 0 0.968627451   0 0 0 0 1  0 0 0 0.651005245 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
      <feMerge>
        <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
        <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
      </feMerge>
    </filter>
    <filter x="-6.1%" y="-5.3%" width="112.3%" height="110.6%" filterUnits="objectBoundingBox" id="filter-7">
      <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
      <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
      <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
      <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.228884397 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
    </filter>
  </defs>
  <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="课程报告" transform="translate(-625.000000, -428.000000)">
      <g id="编组-24" transform="translate(628.908681, 430.141653)">
        <g id="路径-140">
          <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
          <use stroke="url(#linearGradient-2)" stroke-width="1" fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-3"></use>
        </g>
        <g id="路径-141备份-2">
          <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
          <use fill="#172D43" fill-rule="evenodd" xlink:href="#path-5"></use>
          <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-5"></use>
        </g>
      </g>
    </g>
  </g>
</svg>
