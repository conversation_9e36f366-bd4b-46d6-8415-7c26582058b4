<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="151px" height="56px" viewBox="0 0 151 56" version="1.1">
  <title>编组 13备份 2</title>
  <defs>
    <linearGradient x1="-5.38129248%" y1="50%" x2="99.7006213%" y2="50%" id="linearGradient-1">
      <stop stop-color="#8A50D0" stop-opacity="0" offset="0%"></stop>
      <stop stop-color="#834DC8" offset="55.46875%"></stop>
      <stop stop-color="#884ECD" stop-opacity="0" offset="100%"></stop>
    </linearGradient>
    <filter x="-12.4%" y="-57.7%" width="124.8%" height="215.4%" filterUnits="objectBoundingBox" id="filter-2">
      <feGaussianBlur stdDeviation="5" in="SourceGraphic"></feGaussianBlur>
    </filter>
    <linearGradient x1="-5.38129248%" y1="50%" x2="99.7006213%" y2="50%" id="linearGradient-3">
      <stop stop-color="#582C88" stop-opacity="0" offset="0%"></stop>
      <stop stop-color="#864ECB" offset="55.46875%"></stop>
      <stop stop-color="#542982" stop-opacity="0" offset="100%"></stop>
    </linearGradient>
    <filter x="-15.0%" y="-46.2%" width="130.0%" height="192.3%" filterUnits="objectBoundingBox" id="filter-4">
      <feGaussianBlur stdDeviation="4" in="SourceGraphic"></feGaussianBlur>
    </filter>
  </defs>
  <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" opacity="0.72437686">
    <g id="课程报告" transform="translate(-309.000000, -547.000000)">
      <g id="编组-10" transform="translate(324.000000, 498.000000)">
        <g id="编组-13备份-2" transform="translate(0.000000, 64.000000)">
          <rect id="矩形" fill="url(#linearGradient-1)" opacity="0.631626674" filter="url(#filter-2)" x="0" y="0" width="121" height="26"></rect>
          <rect id="矩形" fill="url(#linearGradient-3)" opacity="0.505324591" filter="url(#filter-4)" x="21" y="0" width="80" height="26"></rect>
        </g>
      </g>
    </g>
  </g>
</svg>
