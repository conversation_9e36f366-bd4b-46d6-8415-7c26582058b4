<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="301px" height="70px" viewBox="0 0 301 70" version="1.1">
  <title>编组 24</title>
  <defs>
    <linearGradient x1="4.23213945e-15%" y1="59.3546214%" x2="100%" y2="59.3546214%" id="linearGradient-1">
      <stop stop-color="#FFB67B" stop-opacity="0.844350962" offset="0%"></stop>
      <stop stop-color="#FFD395" stop-opacity="0" offset="46.8558785%"></stop>
      <stop stop-color="#FFF4B2" stop-opacity="0.726753715" offset="100%"></stop>
    </linearGradient>
    <linearGradient x1="14.0944956%" y1="47.1701298%" x2="16.1348398%" y2="53.0747673%" id="linearGradient-2">
      <stop stop-color="#FEBE05" offset="0%"></stop>
      <stop stop-color="#FFF497" offset="100%"></stop>
    </linearGradient>
    <polygon id="path-3" points="4.16653458e-14 5.41422216 -8.59241065e-14 62.1934458 279.325225 62.1934458 286.091319 56.0293688 286.091319 29.3380569 292.707785 23.1838014 292.707785 16.4946731 286.091319 9.59419782 72.7043886 9.59419782 59.859707 -1.01828682e-13 6.27953549 -7.6572443e-14"></polygon>
    <filter x="-2.0%" y="-6.4%" width="104.1%" height="119.3%" filterUnits="objectBoundingBox" id="filter-4">
      <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
      <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
      <feColorMatrix values="0 0 0 0 0.0242624908   0 0 0 0 0.123169137   0 0 0 0 0.271144701  0 0 0 0.105140953 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <polygon id="path-5" points="9 9.61792332 9 53.5937073 10.2474617 55 59.0021135 55 60.5330617 53.5556097 60.5330617 43.8288229 66 38.9447538 66 24.4718695 60.5330617 19.3860617 60.5330617 9.61792332 59.2463019 8 10.4655254 8"></polygon>
    <filter x="-5.3%" y="-4.3%" width="110.5%" height="108.5%" filterUnits="objectBoundingBox" id="filter-6">
      <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.996078431   0 0 0 0 0.968627451   0 0 0 0 0.737254902  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
      <feOffset dx="-1" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
      <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.996078431   0 0 0 0 0.956862745   0 0 0 0 0.650980392  0 0 0 1 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
      <feMerge>
        <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
        <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
      </feMerge>
    </filter>
    <filter x="-6.1%" y="-5.3%" width="112.3%" height="110.6%" filterUnits="objectBoundingBox" id="filter-7">
      <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
      <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
      <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
      <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.228884397 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
    </filter>
  </defs>
  <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="课程报告" transform="translate(-625.000000, -213.000000)">
      <g id="编组-24" transform="translate(628.908681, 215.141653)">
        <g id="路径-140">
          <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
          <use stroke="url(#linearGradient-2)" stroke-width="1" fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-3"></use>
        </g>
        <g id="路径-141">
          <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
          <use fill="#554004" fill-rule="evenodd" xlink:href="#path-5"></use>
          <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-5"></use>
        </g>
      </g>
    </g>
  </g>
</svg>
