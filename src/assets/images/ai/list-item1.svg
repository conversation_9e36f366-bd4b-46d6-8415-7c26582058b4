<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="128px" height="69px" viewBox="0 0 128 69" version="1.1">
  <title>编组 3</title>
  <defs>
    <linearGradient x1="-8.23810951%" y1="50%" x2="104.39408%" y2="50%" id="linearGradient-1">
      <stop stop-color="#42B9F2" stop-opacity="0" offset="0%"></stop>
      <stop stop-color="#3EB6F2" stop-opacity="0.55591674" offset="100%"></stop>
      <stop stop-color="#FFFFFF" offset="100%"></stop>
    </linearGradient>
    <rect id="path-2" x="0" y="0" width="124" height="49"></rect>
    <filter x="-66.1%" y="-73.6%" width="232.2%" height="247.2%" filterUnits="objectBoundingBox" id="filter-4">
      <feGaussianBlur stdDeviation="13" in="SourceGraphic"></feGaussianBlur>
    </filter>
    <filter x="-40.8%" y="-45.5%" width="181.7%" height="190.9%" filterUnits="objectBoundingBox" id="filter-5">
      <feGaussianBlur stdDeviation="5" in="SourceGraphic"></feGaussianBlur>
    </filter>
    <rect id="path-6" x="124" y="8" width="2" height="51"></rect>
    <filter x="-450.0%" y="-13.7%" width="800.0%" height="127.5%" filterUnits="objectBoundingBox" id="filter-7">
      <feOffset dx="-2" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.62745098   0 0 0 0 0.933333333   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <rect id="path-8" x="18" y="53" width="8" height="8"></rect>
    <filter x="-75.0%" y="-75.0%" width="250.0%" height="250.0%" filterUnits="objectBoundingBox" id="filter-9">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.62745098   0 0 0 0 0.933333333   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <rect id="path-10" x="108" y="47" width="8" height="8"></rect>
    <filter x="-75.0%" y="-75.0%" width="250.0%" height="250.0%" filterUnits="objectBoundingBox" id="filter-11">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.62745098   0 0 0 0 0.933333333   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <rect id="path-12" x="118" y="0" width="4" height="4"></rect>
    <filter x="-150.0%" y="-150.0%" width="400.0%" height="400.0%" filterUnits="objectBoundingBox" id="filter-13">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.62745098   0 0 0 0 0.933333333   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <rect id="path-14" x="14" y="22" width="8" height="8"></rect>
    <filter x="-75.0%" y="-75.0%" width="250.0%" height="250.0%" filterUnits="objectBoundingBox" id="filter-15">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.62745098   0 0 0 0 0.933333333   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <rect id="path-16" x="6" y="34" width="4" height="4"></rect>
    <filter x="-150.0%" y="-150.0%" width="400.0%" height="400.0%" filterUnits="objectBoundingBox" id="filter-17">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.62745098   0 0 0 0 0.933333333   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <rect id="path-18" x="18" y="26" width="8" height="8"></rect>
    <filter x="-75.0%" y="-75.0%" width="250.0%" height="250.0%" filterUnits="objectBoundingBox" id="filter-19">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.62745098   0 0 0 0 0.933333333   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
    <rect id="path-20" x="107" y="4" width="8" height="10"></rect>
    <filter x="-75.0%" y="-60.0%" width="250.0%" height="220.0%" filterUnits="objectBoundingBox" id="filter-21">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0.62745098   0 0 0 0 0.933333333   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
    </filter>
  </defs>
  <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="备课-准备材料" transform="translate(0.000000, -190.000000)">
      <g id="编组-3" transform="translate(0.000000, 194.000000)">
        <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="9" width="124" height="49"></rect>
        <g id="编组-2">
          <g id="编组" transform="translate(0.000000, 9.000000)">
            <mask id="mask-3" fill="white">
              <use xlink:href="#path-2"></use>
            </mask>
            <g id="蒙版"></g>
            <g mask="url(#mask-3)" id="椭圆形">
              <g transform="translate(95.000000, -1.000000)">
                <ellipse fill="#5BB9FF" filter="url(#filter-4)" cx="29.5" cy="26.5" rx="29.5" ry="26.5"></ellipse>
                <ellipse fill="#B3F1FF" filter="url(#filter-5)" cx="38.3679245" cy="26.5" rx="18.3679245" ry="16.5"></ellipse>
              </g>
            </g>
          </g>
          <g id="矩形">
            <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-6"></use>
          </g>
          <g id="矩形" opacity="0.650692894">
            <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
            <use fill="#A0EEFF" fill-rule="evenodd" xlink:href="#path-8"></use>
          </g>
          <g id="矩形备份-22" opacity="0.361607143">
            <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-10"></use>
            <use fill="#A0EEFF" fill-rule="evenodd" xlink:href="#path-10"></use>
          </g>
          <g id="矩形备份-24" opacity="0.361607143">
            <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
            <use fill="#A0EEFF" fill-rule="evenodd" xlink:href="#path-12"></use>
          </g>
          <g id="矩形备份" opacity="0.193033854">
            <use fill="black" fill-opacity="1" filter="url(#filter-15)" xlink:href="#path-14"></use>
            <use fill="#A0EEFF" fill-rule="evenodd" xlink:href="#path-14"></use>
          </g>
          <g id="矩形备份-23" opacity="0.193033854">
            <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
            <use fill="#A0EEFF" fill-rule="evenodd" xlink:href="#path-16"></use>
          </g>
          <g id="矩形备份-2" opacity="0.661830357">
            <use fill="black" fill-opacity="1" filter="url(#filter-19)" xlink:href="#path-18"></use>
            <use fill="#A0EEFF" fill-rule="evenodd" xlink:href="#path-18"></use>
          </g>
          <g id="矩形备份-21" opacity="0.522623698">
            <use fill="black" fill-opacity="1" filter="url(#filter-21)" xlink:href="#path-20"></use>
            <use fill="#A0EEFF" fill-rule="evenodd" xlink:href="#path-20"></use>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
