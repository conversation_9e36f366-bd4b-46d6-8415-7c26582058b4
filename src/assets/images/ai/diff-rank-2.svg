<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 101 93" class="design-iconfont">
  <defs>
    <filter x="-3.1%" y="-2.7%" width="106.3%" height="105.4%" filterUnits="objectBoundingBox" id="ek8yiuzh6b">
      <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner1"/>
      <feOffset dx="-1" dy="-1" in="shadowBlurInner1" result="shadowOffsetInner1"/>
      <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"/>
      <feColorMatrix values="0 0 0 0 0.356862745 0 0 0 0 0.850980392 0 0 0 0 0.964705882 0 0 0 1 0" in="shadowInnerInner1"/>
    </filter>
    <filter x="-4.1%" y="-4.5%" width="108.3%" height="109%" filterUnits="objectBoundingBox" id="8unhd1ji4f">
      <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner1"/>
      <feOffset dx="3" dy="-4" in="shadowBlurInner1" result="shadowOffsetInner1"/>
      <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"/>
      <feColorMatrix values="0 0 0 0 0.262745098 0 0 0 0 0.737254902 0 0 0 0 0.882352941 0 0 0 1 0" in="shadowInnerInner1" result="shadowMatrixInner1"/>
      <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner2"/>
      <feOffset dx="3" dy="4" in="shadowBlurInner2" result="shadowOffsetInner2"/>
      <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"/>
      <feColorMatrix values="0 0 0 0 0.282352941 0 0 0 0 0.654901961 0 0 0 0 0.964705882 0 0 0 1 0" in="shadowInnerInner2" result="shadowMatrixInner2"/>
      <feMerge>
        <feMergeNode in="shadowMatrixInner1"/>
        <feMergeNode in="shadowMatrixInner2"/>
      </feMerge>
    </filter>
    <filter x="-37.5%" y="-34.6%" width="175.1%" height="171.5%" filterUnits="objectBoundingBox" id="gi93oknn2h">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.62745098 0 0 0 0 0.933333333 0 0 0 0 1 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-37.5%" y="-34.6%" width="175.1%" height="171.5%" filterUnits="objectBoundingBox" id="r3jydsae3k">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.62745098 0 0 0 0 0.933333333 0 0 0 0 1 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
    <path id="ueq8sygmqa" d="M0 88.4569735L3.63944749 92.2753806 51.1893563 92.2753806 79.8438432 45.7867667 58.6882913 1.99485835 53.6165571 -2.66135062e-16z"/>
    <path id="67monvs75e" d="M29.1544421 1.19447088L7.88222652e-16 45.4816114 20.6985072 88.4569735 67.7012449 88.4569735 96.5246461 44.2284867 74.3150643 -9.58356124e-14z"/>
    <path d="M32.1278854,48.6189495 L27.8856371,50.8495382 C27.3779102,51.1165028 26.7498986,50.9213263 26.4829341,50.4135994 C26.3766218,50.2114092 26.3399307,49.9798105 26.3785416,49.7546608 L27.1887758,45.0299827 L27.1887758,45.0299827 L23.756437,41.6838189 C23.3456926,41.283387 23.3373326,40.6257986 23.7377646,40.2150542 C23.8971968,40.0515161 24.1060873,39.9450876 24.3321058,39.9122406 L29.0753478,39.2229124 L29.0753478,39.2229124 L31.1964587,34.924481 C31.4503017,34.4100683 32.073096,34.1988347 32.5875087,34.4526777 C32.7923854,34.5537764 32.9582133,34.7196043 33.0593121,34.924481 L35.180423,39.2229124 L35.180423,39.2229124 L39.923665,39.9122406 C40.4913363,39.9947394 40.8846463,40.5218064 40.8021474,41.0894777 C40.7693005,41.3154963 40.6628719,41.5243868 40.4993339,41.6838189 L37.066995,45.0299827 L37.066995,45.0299827 L37.8772293,49.7546608 C37.9741864,50.3200421 37.5944536,50.8569736 37.0290723,50.9539307 C36.8039227,50.9925416 36.5723239,50.9558505 36.3701337,50.8495382 L32.1278854,48.6189495 L32.1278854,48.6189495 Z" id="irv5mqvboi"/>
    <path d="M63.2876083,48.6189495 L59.04536,50.8495382 C58.5376331,51.1165028 57.9096215,50.9213263 57.642657,50.4135994 C57.5363447,50.2114092 57.4996536,49.9798105 57.5382645,49.7546608 L58.3484987,45.0299827 L58.3484987,45.0299827 L54.9161599,41.6838189 C54.5054155,41.283387 54.4970555,40.6257986 54.8974875,40.2150542 C55.0569197,40.0515161 55.2658102,39.9450876 55.4918287,39.9122406 L60.2350707,39.2229124 L60.2350707,39.2229124 L62.3561816,34.924481 C62.6100246,34.4100683 63.2328189,34.1988347 63.7472316,34.4526777 C63.9521083,34.5537764 64.1179362,34.7196043 64.219035,34.924481 L66.3401459,39.2229124 L66.3401459,39.2229124 L71.0833879,39.9122406 C71.6510592,39.9947394 72.0443692,40.5218064 71.9618703,41.0894777 C71.9290234,41.3154963 71.8225948,41.5243868 71.6590568,41.6838189 L68.2267179,45.0299827 L68.2267179,45.0299827 L69.0369522,49.7546608 C69.1339093,50.3200421 68.7541765,50.8569736 68.1887952,50.9539307 C67.9636456,50.9925416 67.7320468,50.9558505 67.5298566,50.8495382 L63.2876083,48.6189495 L63.2876083,48.6189495 Z" id="m3qucg45vl"/>
    <linearGradient x1="73.3601112%" y1="0%" x2="26.6398888%" y2="96.6485416%" id="85iext066c">
      <stop stop-color="#2D57AC" offset="0%"/>
      <stop stop-color="#142E70" offset="50.6993488%"/>
      <stop stop-color="#63B1F6" offset="100%"/>
    </linearGradient>
    <linearGradient x1="20.5950292%" y1="95.3001645%" x2="72.4419597%" y2="12.1073127%" id="fuxff37fmg">
      <stop stop-color="#6DEEFA" offset="0%"/>
      <stop stop-color="#76B1EB" offset="39.4912818%"/>
      <stop stop-color="#5A99E3" offset="100%"/>
    </linearGradient>
    <linearGradient x1="37.0665052%" y1="17.7555865%" x2="50%" y2="100%" id="ph9xv2ljzj">
      <stop stop-color="#A8F3FC" offset="0%"/>
      <stop stop-color="#6FE2F7" offset="100%"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <g transform="translate(20.730925 .574873)">
      <mask id="yu7snmykwd" fill="#fff">
        <use xlink:href="#ueq8sygmqa"/>
      </mask>
      <use fill="#2951A0" xlink:href="#ueq8sygmqa"/>
      <use fill="#000" filter="url(#ek8yiuzh6b)" xlink:href="#ueq8sygmqa"/>
      <path fill="url(#85iext066c)" mask="url(#yu7snmykwd)" d="M47.0027377 88.4569735L51.1893563 92.2753806 79.8438432 45.7867667 75.8261389 44.2284867z"/>
    </g>
    <g transform="translate(.032418 .574873)">
      <use fill="#233663" xlink:href="#67monvs75e"/>
      <use fill="#000" filter="url(#8unhd1ji4f)" xlink:href="#67monvs75e"/>
      <path stroke="url(#fuxff37fmg)" stroke-width="3.11597229" d="M73.3668967,1.58360946 L94.7306718,44.1277445 L66.8569465,86.8989873 L21.6774001,86.8989873 L1.78673546,45.6009117 L30.0085973,2.73041 L73.3668967,1.58360946 Z" stroke-linejoin="square"/>
    </g>
    <g transform="translate(.032418 .574873)">
      <use fill="#000" filter="url(#gi93oknn2h)" xlink:href="#irv5mqvboi"/>
      <use fill="url(#ph9xv2ljzj)" xlink:href="#irv5mqvboi"/>
    </g>
    <g transform="translate(.032418 .574873)">
      <use fill="#000" filter="url(#r3jydsae3k)" xlink:href="#m3qucg45vl"/>
      <use fill="url(#ph9xv2ljzj)" xlink:href="#m3qucg45vl"/>
    </g>
  </g>
</svg>
