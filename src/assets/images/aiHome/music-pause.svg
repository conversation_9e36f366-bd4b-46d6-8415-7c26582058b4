<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 174 98" class="design-iconfont">
  <defs>
    <filter x="-27.6%" y="-40.7%" width="155.8%" height="182.1%" filterUnits="objectBoundingBox" id="16wajnntec">
      <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"/>
      <feOffset in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="12" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.152941176 0 0 0 0 0.529411765 0 0 0 0 1 0 0 0 0.184986888 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-25.3%" y="-37.3%" width="151.3%" height="175.3%" filterUnits="objectBoundingBox" id="8rrelp2f7e">
      <feMorphology radius="8" in="SourceAlpha" result="shadowSpreadInner1"/>
      <feGaussianBlur stdDeviation="8" in="shadowSpreadInner1" result="shadowBlurInner1"/>
      <feOffset in="shadowBlurInner1" result="shadowOffsetInner1"/>
      <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"/>
      <feColorMatrix values="0 0 0 0 0.584546494 0 0 0 0 0.769192497 0 0 0 0 1 0 0 0 1 0" in="shadowInnerInner1"/>
    </filter>
    <filter x="-77.4%" y="-53.4%" width="254.8%" height="206.8%" filterUnits="objectBoundingBox" id="egiu4o3i5f">
      <feGaussianBlur in="SourceGraphic"/>
    </filter>
    <filter x="-116.1%" y="-80.1%" width="332.2%" height="260.2%" filterUnits="objectBoundingBox" id="shqm2dxpug">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.670588235 0 0 0 0 0.925490196 0 0 0 0 0.956862745 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-7.3%" y="-75.8%" width="114.7%" height="251.6%" filterUnits="objectBoundingBox" id="t5gc6gunsi">
      <feMorphology radius="1.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"/>
      <feOffset in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
      <feMorphology radius="2" in="SourceAlpha" result="shadowInner"/>
      <feOffset in="shadowInner" result="shadowInner"/>
      <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.68627451 0 0 0 0 0.968627451 0 0 0 0 1 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-19.9%" y="-24%" width="139.8%" height="148.1%" filterUnits="objectBoundingBox" id="iuhgrn3k4k">
      <feGaussianBlur in="SourceGraphic"/>
    </filter>
    <filter x="-28.9%" y="-34.9%" width="157.8%" height="169.9%" filterUnits="objectBoundingBox" id="056rje8wzl">
      <feMorphology radius="2" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"/>
      <feOffset in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
      <feMorphology radius="2" in="SourceAlpha" result="shadowInner"/>
      <feOffset in="shadowInner" result="shadowInner"/>
      <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.68627451 0 0 0 0 0.968627451 0 0 0 0 1 0 0 0 0.629835009 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-21.7%" y="-24%" width="146.1%" height="148%" filterUnits="objectBoundingBox" id="x4vo8uicfn">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.152941176 0 0 0 0 0.529411765 0 0 0 0 1 0 0 0 0.296519886 0" in="shadowBlurOuter1" result="shadowMatrixOuter1"/>
      <feMerge>
        <feMergeNode in="shadowMatrixOuter1"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter x="-2.2%" y="-2%" width="104.3%" height="104%" filterUnits="objectBoundingBox" id="6ne80lf06q">
      <feGaussianBlur stdDeviation=".5" in="SourceAlpha" result="shadowBlurInner1"/>
      <feOffset dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"/>
      <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"/>
      <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.246203016 0" in="shadowInnerInner1"/>
    </filter>
    <path id="6evj5pbuda" d="M0 0H174V98H0z"/>
    <path id="1p7rp68wmd" d="M5.9920812e-13 117.900283L168.656589 101.454126 174.125534 83.9589561 71.7834943 3.29715417e-15 0 0z"/>
    <path id="tz9ow5sixh" d="M174.125534 86.1404751L166.628691 86.1404751 165.080603 97.4331836 170.091844 99.2528454z"/>
    <path d="M10.0362854,116.808398 L168.656589,101.454126" id="9a0q498w5j"/>
    <path d="M129.789675,47.2669415 L174.125534,83.9589561" id="24asu3qrim"/>
    <path d="M18.547,27.568 L18.5471774,41.1211315 C18.5328297,42.9914409 17.6433011,44.9476634 15.9944585,46.5810184 C13.7124157,48.8416259 10.32258,50.1403306 7.10186981,49.9879229 C3.8811596,49.8355152 1.31887733,48.2551495 0.380209965,45.8421344 C-0.55845752,43.4291192 0.269095804,40.5500489 2.5511386,38.2894415 C4.8331814,36.028834 8.22301707,34.7301293 11.4437273,34.882537 C12.5460537,34.9347004 13.5712498,35.1541393 14.4806176,35.5169857 L14.48,29.017 L18.547,27.568 Z M45.999,17.783 L46,35.3695389 C46.0111196,37.2625902 45.1199792,39.2501498 43.4485333,40.9059321 C41.1664627,43.1666166 37.7765298,44.4653585 34.5557468,44.3129123 C31.3349638,44.160466 28.7726705,42.579993 27.8341015,40.1668689 C26.8952875,37.7538387 27.7227763,34.8746816 30.0048469,32.613997 C32.2869175,30.3533124 35.6768504,29.0545705 38.8976334,29.2070168 C39.9991859,29.2591555 41.0237126,29.4783356 41.9325977,29.840738 L41.932,19.232 L45.999,17.783 Z M45.9960006,4.8933524 L46,5.09799851 L45.999,12.474 L41.932,13.924 L41.9329152,10.6477809 C41.9329152,9.99189752 41.373691,9.56305071 40.8398861,9.56305071 C40.7721014,9.56305071 40.7043167,9.57426238 40.6365319,9.58921128 L40.5348548,9.61350326 L19.3351751,16.3236947 C18.892456,16.4393152 18.5992517,16.8093007 18.5534587,17.2393446 L18.5471774,17.3579724 L18.547,22.26 L14.48,23.71 L14.4800915,11.8081899 C14.4786408,9.63182973 15.8651712,7.702934 17.9180056,6.9863814 L18.1404689,6.91429093 L39.3147293,0.229325748 C42.5780565,-0.759544532 45.8657964,1.57072834 45.9960006,4.8933524 Z" id="jtbuts5vtp"/>
    <linearGradient x1="78.8888311%" y1="100%" x2="25.1004254%" y2="30.44663%" id="sx8ju6t50o">
      <stop stop-color="#A4F7ED" offset="0%"/>
      <stop stop-color="#41B8F2" offset="100%"/>
    </linearGradient>
    <linearGradient x1="52.3598771%" y1="72.409432%" x2="14.8135693%" y2="43.6470839%" id="70qly7jrvr">
      <stop stop-color="#A4F7ED" offset="0%"/>
      <stop stop-color="#41B8F2" offset="100%"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <g>
      <mask id="2svf2lzvpb" fill="#fff">
        <use xlink:href="#6evj5pbuda"/>
      </mask>
      <g mask="url(#2svf2lzvpb)">
        <g fill="#000" transform="matrix(-1 0 0 1 187.089249 -27.764921)">
          <use filter="url(#16wajnntec)" xlink:href="#1p7rp68wmd"/>
          <use filter="url(#8rrelp2f7e)" xlink:href="#1p7rp68wmd"/>
        </g>
        <g filter="url(#egiu4o3i5f)" transform="matrix(-1 0 0 1 187.089249 -27.764921)">
          <use fill="#000" filter="url(#shqm2dxpug)" xlink:href="#tz9ow5sixh"/>
          <use fill="#AFF7FF" xlink:href="#tz9ow5sixh"/>
        </g>
        <g transform="matrix(-1 0 0 1 187.089249 -27.764921)">
          <use fill="#000" filter="url(#t5gc6gunsi)" xlink:href="#9a0q498w5j"/>
          <use stroke="#ABECF4" xlink:href="#9a0q498w5j"/>
        </g>
        <g filter="url(#iuhgrn3k4k)" transform="matrix(-1 0 0 1 187.089249 -27.764921)">
          <use fill="#000" filter="url(#056rje8wzl)" xlink:href="#24asu3qrim"/>
          <use stroke="#A0EEFF" stroke-width="2" xlink:href="#24asu3qrim"/>
        </g>
      </g>
    </g>
    <g filter="url(#x4vo8uicfn)" transform="translate(83 15)">
      <g opacity=".99" fill-rule="nonzero">
        <use fill="url(#sx8ju6t50o)" xlink:href="#jtbuts5vtp"/>
        <use fill="#000" filter="url(#6ne80lf06q)" xlink:href="#jtbuts5vtp"/>
      </g>
      <path stroke="url(#70qly7jrvr)" stroke-width="3" stroke-linecap="round" d="M4.28025478 30L55.3370597 11.8006787"/>
    </g>
  </g>
</svg>
