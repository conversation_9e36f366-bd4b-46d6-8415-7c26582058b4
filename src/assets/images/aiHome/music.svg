<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 174 98" class="design-iconfont">
  <defs>
    <filter x="-27.6%" y="-40.7%" width="155.8%" height="182.1%" filterUnits="objectBoundingBox" id="7wcqv3ypic">
      <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"/>
      <feOffset in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="12" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.152941176 0 0 0 0 0.529411765 0 0 0 0 1 0 0 0 0.184986888 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-25.3%" y="-37.3%" width="151.3%" height="175.3%" filterUnits="objectBoundingBox" id="7se2ftilse">
      <feMorphology radius="8" in="SourceAlpha" result="shadowSpreadInner1"/>
      <feGaussianBlur stdDeviation="8" in="shadowSpreadInner1" result="shadowBlurInner1"/>
      <feOffset in="shadowBlurInner1" result="shadowOffsetInner1"/>
      <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"/>
      <feColorMatrix values="0 0 0 0 0.584546494 0 0 0 0 0.769192497 0 0 0 0 1 0 0 0 1 0" in="shadowInnerInner1"/>
    </filter>
    <filter x="-77.4%" y="-53.4%" width="254.8%" height="206.8%" filterUnits="objectBoundingBox" id="xzk1c1bp2f">
      <feGaussianBlur in="SourceGraphic"/>
    </filter>
    <filter x="-116.1%" y="-80.1%" width="332.2%" height="260.2%" filterUnits="objectBoundingBox" id="l2y61j1qug">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.670588235 0 0 0 0 0.925490196 0 0 0 0 0.956862745 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-7.3%" y="-75.8%" width="114.7%" height="251.6%" filterUnits="objectBoundingBox" id="h6uytfb7yi">
      <feMorphology radius="1.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"/>
      <feOffset in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
      <feMorphology radius="2" in="SourceAlpha" result="shadowInner"/>
      <feOffset in="shadowInner" result="shadowInner"/>
      <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.68627451 0 0 0 0 0.968627451 0 0 0 0 1 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-19.9%" y="-24%" width="139.8%" height="148.1%" filterUnits="objectBoundingBox" id="qf59tb20sk">
      <feGaussianBlur in="SourceGraphic"/>
    </filter>
    <filter x="-28.9%" y="-34.9%" width="157.8%" height="169.9%" filterUnits="objectBoundingBox" id="4hlvnf2sjl">
      <feMorphology radius="2" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"/>
      <feOffset in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
      <feMorphology radius="2" in="SourceAlpha" result="shadowInner"/>
      <feOffset in="shadowInner" result="shadowInner"/>
      <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.68627451 0 0 0 0 0.968627451 0 0 0 0 1 0 0 0 0.629835009 0" in="shadowBlurOuter1"/>
    </filter>
    <filter x="-26.1%" y="-24%" width="152.2%" height="148%" filterUnits="objectBoundingBox" id="7adzmpfqsn">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.152941176 0 0 0 0 0.529411765 0 0 0 0 1 0 0 0 0.296519886 0" in="shadowBlurOuter1" result="shadowMatrixOuter1"/>
      <feMerge>
        <feMergeNode in="shadowMatrixOuter1"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <path id="7mtq9ey19a" d="M0 0H174V98H0z"/>
    <path id="w5onkqo1pd" d="M5.9920812e-13 117.900283L168.656589 101.454126 174.125534 83.9589561 71.7834943 3.29715417e-15 0 0z"/>
    <path id="vn0btpzyzh" d="M174.125534 86.1404751L166.628691 86.1404751 165.080603 97.4331836 170.091844 99.2528454z"/>
    <path d="M10.0362854,116.808398 L168.656589,101.454126" id="q8qe62gm5j"/>
    <path d="M129.789675,47.2669415 L174.125534,83.9589561" id="de6t0xapgm"/>
    <linearGradient x1="78.8888311%" y1="100%" x2="25.1004254%" y2="30.44663%" id="rdzrzomngo">
      <stop stop-color="#A4F7ED" offset="0%"/>
      <stop stop-color="#41B8F2" offset="100%"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <g>
      <mask id="3p6u9uy27b" fill="#fff">
        <use xlink:href="#7mtq9ey19a"/>
      </mask>
      <g mask="url(#3p6u9uy27b)">
        <g fill="#000" transform="matrix(-1 0 0 1 187.089249 -27.764921)">
          <use filter="url(#7wcqv3ypic)" xlink:href="#w5onkqo1pd"/>
          <use filter="url(#7se2ftilse)" xlink:href="#w5onkqo1pd"/>
        </g>
        <g filter="url(#xzk1c1bp2f)" transform="matrix(-1 0 0 1 187.089249 -27.764921)">
          <use fill="#000" filter="url(#l2y61j1qug)" xlink:href="#vn0btpzyzh"/>
          <use fill="#AFF7FF" xlink:href="#vn0btpzyzh"/>
        </g>
        <g transform="matrix(-1 0 0 1 187.089249 -27.764921)">
          <use fill="#000" filter="url(#h6uytfb7yi)" xlink:href="#q8qe62gm5j"/>
          <use stroke="#ABECF4" xlink:href="#q8qe62gm5j"/>
        </g>
        <g filter="url(#qf59tb20sk)" transform="matrix(-1 0 0 1 187.089249 -27.764921)">
          <use fill="#000" filter="url(#4hlvnf2sjl)" xlink:href="#de6t0xapgm"/>
          <use stroke="#A0EEFF" stroke-width="2" xlink:href="#de6t0xapgm"/>
        </g>
      </g>
    </g>
    <g filter="url(#7adzmpfqsn)" transform="translate(83 15)" fill="url(#rdzrzomngo)" fill-rule="nonzero" opacity=".99">
      <path d="M39.3147293,0.229325748 C42.644655,-0.779725558 46,1.66722392 46,5.09799851 L46,5.09799851 L46,35.3695389 C46.0111196,37.2625902 45.1199792,39.2501498 43.4485333,40.9059321 C41.1664627,43.1666166 37.7765298,44.4653585 34.5557468,44.3129123 C31.3349638,44.160466 28.7726705,42.579993 27.8341015,40.1668689 C26.8952875,37.7538387 27.7227763,34.8746816 30.0048469,32.613997 C32.2869175,30.3533124 35.6768504,29.0545705 38.8976334,29.2070168 C39.9991859,29.2591555 41.0237126,29.4783356 41.9325977,29.840738 L41.9329152,10.6477809 C41.9329152,9.99189752 41.373691,9.56305071 40.8398861,9.56305071 C40.738209,9.56305071 40.6365319,9.58827696 40.5348548,9.61350326 L40.5348548,9.61350326 L19.3351751,16.3236947 C18.8522088,16.4498261 18.5471774,16.878673 18.5471774,17.3579724 L18.5471774,17.3579724 L18.5471774,41.1211315 C18.5328297,42.9914409 17.6433011,44.9476634 15.9944585,46.5810184 C13.7124157,48.8416259 10.32258,50.1403306 7.10186981,49.9879229 C3.8811596,49.8355152 1.31887733,48.2551495 0.380209965,45.8421344 C-0.55845752,43.4291192 0.269095804,40.5500489 2.5511386,38.2894415 C4.8331814,36.028834 8.22301707,34.7301293 11.4437273,34.882537 C12.5460537,34.9347004 13.5712498,35.1541393 14.4806176,35.5169857 L14.4800915,11.8081899 C14.478589,9.55410258 15.9659794,7.56547137 18.1404689,6.91429093 L18.1404689,6.91429093 Z"/>
    </g>
  </g>
</svg>
