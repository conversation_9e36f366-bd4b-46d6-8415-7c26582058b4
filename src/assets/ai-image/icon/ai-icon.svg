<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="140px" height="155px" viewBox="0 0 140 155" version="1.1">
  <title>编组 3</title>
  <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="loding" transform="translate(-650.000000, -314.000000)">
      <g id="编组-3" transform="translate(650.081877, 314.576151)">
        <rect id="矩形" fill="#E45C56" x="0" y="14.0114525" width="139.836245" height="139.836245" rx="28"></rect>
        <g id="编组-2" transform="translate(46.000000, 48.093330)">
          <circle id="椭圆形" fill="#FFFFFF" cx="23.9181226" cy="23.9181226" r="23.9181226"></circle>
          <circle id="椭圆形" fill="#134079" cx="33.7696412" cy="27.75" r="12.25"></circle>
        </g>
        <g id="编组" transform="translate(52.019641, -0.000000)" fill="#FFFFFF">
          <rect id="矩形" transform="translate(22.426739, 14.011453) rotate(49.000000) translate(-22.426739, -14.011453) " x="16.4267389" y="1.51145251" width="12" height="25" rx="6"></rect>
          <rect id="矩形备份" transform="translate(12.728995, 13.370224) scale(-1, 1) rotate(41.000000) translate(-12.728995, -13.370224) " x="6.72899534" y="0.870223927" width="12" height="25" rx="6"></rect>
        </g>
        <rect id="矩形" fill="#FFFFFF" x="46" y="113.011453" width="47.8362451" height="16" rx="8"></rect>
      </g>
    </g>
  </g>
</svg>
