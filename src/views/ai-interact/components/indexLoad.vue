<template>
  <div class="ai-loading">
    <div class="ai-load-icon">
      <img class="w h" src="@/assets/ai-image/icon/ai-icon.svg" />
    </div>
    <div class="ai-load-text">
      正在加载资源。。。
    </div>
    <div class="ai-load-progress">
      <div class="ai-load-progress_inside" :style="{width: styleWidth + '%'}"></div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      styleWidth: 10,
      time: null
    }
  },
  created () {
    this.time = setInterval(() => {
      this.styleWidth += 10
      if (this.styleWidth >= 90) {
        clearInterval(this.time)
      }
    }, 300)
  },
  beforeDestroy () {
    this.styleWidth = 100
  }
}
</script>

<style lang="scss" scoped>
.ai-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background: #0C4475;
    background: linear-gradient(109.39deg, #FCECFF -23.25%, #ABF6FF 97.11%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 99;
    .ai-load-icon {
      width: vh2(60);
      height: vh2(74);
    }

    .ai-load-text {
      margin: vh2(20) 0;
      font-size: vh2(14);
      color: #000;
    }
    .ai-load-progress {
      width: vh2(300);
      height: vh2(10);
      background: #F1F1F1;
      border-radius: vh2(7);
      .ai-load-progress_inside {
        height: vh2(10);
        background: #E45C56;
        border-radius: vh2(7);
      }
    }
}
</style>
