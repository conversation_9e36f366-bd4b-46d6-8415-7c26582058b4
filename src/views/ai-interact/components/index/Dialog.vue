<template>
  <div class="dialog-container-bg flex-col align-center justify-center">
    <div class="dialog-container">
      <div class="close-icon" @click="close">
        <i class="el-icon-error"></i>
      </div>
      <div class="w h">
        <div ref="bill" class="q-box">
          <div class="q-name">{{ info && info.toUser.displayName }}</div>
          <div id="qrcode" ref="qrcode" class="scan-qr"></div>
          <div class="f16 flex justify-center" style="font-weight: 300;">
            班级二维码，保存或截图分享
          </div>
        </div>
        <div class="mt20 flex justify-center">
          <el-button type="text" class="f16" @click="saveImg">保存</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import baseUrl from '@/config'
export default {
  props: {
    info: {
      type: Object,
      require: true,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {
    }
  },
  mounted () {
    this.qrcodeScan()
  },
  methods: {
    close () {
      this.$emit('close')
    },
    qrcodeScan () { // 生成二维码
      this.$nextTick(() => {
        const html = document.getElementById('qrcode')
        html.innerHTML = ''
        const url = `${baseUrl.default.VUE_APP_PARENT}/#/parent/hassClassInfo?isScan=1&classId=${this.info.toUser.id}&className=${this.info.toUser.displayName}&schoolId=${this.info.toUser.school.id}&schoolName=${this.info.toUser.school.name}`
        new QRCode(html, {
          width: 200, // 二维码宽度
          height: 200, // 二维码高度
          text: url, // 浏览器地址url
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        })
        html.removeAttribute('title')
      })
    },
    htmlToCanvas () {
      html2canvas(this.$refs.bill, {})
        .then((canvas) => {
          const imageUrl = canvas.toDataURL('image/png') // 将canvas转成base64图片格式
          this.canvasImageUrl = imageUrl
          this.isDom = false
        })
    },
    dataURLToBlob (dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    saveImage () {
      const canvasID = this.$refs.bill
      const that = this
      const a = document.createElement('a')
      html2canvas(canvasID).then((canvas) => {
        const dom = document.body.appendChild(canvas)
        dom.style.display = 'none'
        a.style.display = 'none'
        document.body.removeChild(dom)
        const blob = that.dataURLToBlob(dom.toDataURL('image/png'))
        a.setAttribute('href', URL.createObjectURL(blob))
        // 这块是保存图片操作  可以设置保存的图片的信息
        a.setAttribute('download', that.info.toUser.displayName + '.png')
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(blob)
        document.body.removeChild(a)
      })
    },
    saveImg () {
      this.saveImage()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';

.f16 {
  font-size: vh2(16);
}
.mt20 {
  margin-top: vh2(10);
}
.dialog-container-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(9, 8, 8, 0.7);
  z-index: 3001;
}
.dialog-container {
  width: vh2(421);
  min-height: vh2(320);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #BDBDBD;
  border-radius: vh2(10);
  padding: vh2(20);
  box-sizing: border-box;
  position: relative;
}
.q-name {
  display: flex;
  justify-content: center;
  font-weight: 600;
  font-size: vh2(25);
  margin-bottom: vh2(10);
}
.q-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
#qrcode {
  display: flex;
  justify-content: center;
  align-items: center;
  width: vh2(150);
  height: vh2(150);
  margin-bottom: vh2(10);

}
.close-icon {
  position: absolute;
  right: vh2(10);
  top: vh2(10);
  cursor: pointer;
  i {
    color: #BDBDBD;
    font-size: vh2(18);
  }
}
</style>
<style lang="scss">
.scan-qr {
  img {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
