<template>
  <div v-if="dialogVisible">
    <el-dialog
      :title="'添加课程'"
      :visible.sync="dialogVisible"
      class="bingo-normal-dialog normal-center"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="closeDialog"
      :width="vh2(480) + 'px'"
      :append-to-body="true"
    >
      <div class="dialog-box">
        <div class="content">
          <div class="w flex-col">
            <div class="flex align-center mb10">
              <div>选择班级：</div>
              <el-select
                v-model="selectClassUserId"
                style="flex: 1"
                class="w"
                :disabled="disableSelectClassUserId"
                placeholder="请选择"
                @change="getCourseList"
              >
                <el-option
                  v-for="item in tableData"
                  :key="item.id"
                  :label="item.displayName"
                  :value="item.id"
                />
              </el-select>
            </div>
            <div class="flex align-center">
              <div>选择课包：</div>
              <el-select
                v-model="selectCrousUserId"
                style="flex: 1"
                class="w"
                :disabled="!selectClassUserId"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in crouseList"
                  :key="item.aiCourse && item.aiCourse.id"
                  :label="item.aiCourse && item.aiCourse.title"
                  :value="item.aiCourse && item.aiCourse.id"
                  :disabled="item.has"
                >
                  <div class="flex w300">
                    <div class="option-width">
                      <div class="item-scope w" style="display: inline-block">
                        {{ item.aiCourse && item.aiCourse.title }}
                      </div>
                    </div>
                    <div
                      class="tr w60"
                      :style="item.has ? 'color: #636363;' : 'color: #3479FF;'"
                    >
                      {{ item.has ? '已添加' : '添加' }}
                    </div>
                  </div>
                </el-option>
              </el-select>
            </div>
            <div class="flex items-start pt10">
              <div>计划授课：</div>
              <div class="flex flex-col mh40" style="flex: 1">
                <el-switch
                  v-model="hasWeeks"
                  active-color="#3479FF"
                  inactive-color="#e9eef5"
                />
                <div v-if="hasWeeks" class="pt5">
                  <el-checkbox-group v-model="weeksList">
                    <el-checkbox label="每周一" />
                    <el-checkbox label="每周二" />
                    <el-checkbox label="每周三" />
                    <el-checkbox label="每周四" />
                    <el-checkbox label="每周五" />
                  </el-checkbox-group>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <div class="edu-btn" @click="addCourse">确定</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAssistantId } from '@/utils/auth'
import { debounce } from '@/utils/index'
import { addCourseToClass, getSchoolCourseList } from '@/api/aicourse'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    },
    gradeList: {
      type: [Object, Array],
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      disableSelectClassUserId: false,
      hasWeeks: false,
      tableData: [],
      tableDataMap: null,
      nowEditRow: null,
      weeksList: [],
      crouseList: [],
      selectClassUserId: '',
      selectCrousUserId: ''
    }
  },
  async mounted () {
    await this._getUserClassList()
  },
  methods: {
    closeDialog () {
      this.$emit('closeDialog')
    },
    vh2 (px) {
      return px * document.body.clientHeight / 965
    },
    async _getUserClassList () {
      const arr = []
      const map = new Map()
      for (let i = 0; i < this.gradeList.length; i++) {
        map.set(this.gradeList[i].toUserId, this.gradeList[i].toUser)
        arr.push(this.gradeList[i].toUser)
      }
      this.tableData = arr
      this.tableDataMap = map
      this.selectClassUserId = this.$store.getters.userInfo.id
      this.disableSelectClassUserId = true
      this.getCourseList(+this.selectClassUserId)
    },
    async getCourseList (val) {
      this.nowEditRow = this.tableDataMap.get(val)
      console.log(this.nowEditRow)
      const { data: { data }} = await getSchoolCourseList({
        studentId: val
      })
      const map = new Map()
      data.forEach(element => {
        map.set(element.courseId, element.courseType)
      })
      this.nowCourseList = map
      this.crouseList = data
    },
    addCourse: debounce(async function () {
      if (!(this.nowEditRow && this.nowEditRow.id && this.selectCrousUserId)) {
        this.$message.error('有内容未填写')
        return
      }
      const assistantId = await getAssistantId()
      const obj = {
        studentId: this.nowEditRow.id,
        courseId: this.selectCrousUserId,
        courseType: this.nowCourseList.get(this.selectCrousUserId),
        assistantUserId: assistantId
        // weekdays: ''
      }
      if (this.hasWeeks) {
        if (this.weeksList.length === 0) {
          this.$message.error('有内容未填写')
          return
        }
        const srtObj = {
          '每周一': 1,
          '每周二': 2,
          '每周三': 3,
          '每周四': 4,
          '每周五': 5,
          '每周六': 6,
          '每周日': 7
        }
        const arr = []
        for (let i = 0; i < this.weeksList.length; i++) {
          arr.push(srtObj[this.weeksList[i]])
        }
        obj.weekdays = arr.join(',')
      }
      await addCourseToClass(obj)
      this.$emit('submit')
    }, 3000, true)
  }
}
</script>

<style lang="scss" scoped>
.normal-center {
  margin:0 !important;
  position:absolute !important;
  top:50% !important;
  left:50% !important;
  transform:translate(-50%,-50%) !important;
  overflow: hidden;

  ::v-deep .el-dialog {
    overflow: hidden;
    border-radius: vh2(24) !important;
    margin: 0 !important;
  }
}
.bingo-normal-dialog {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: vh2(24) !important;

  .dialog-box {
    background: white;
    padding: 0 vh2(20);
    position: relative;
  }

  .el-dialog__body {
    flex:1;
    overflow: auto;
    padding: 0;
  }

  ::v-deep .el-dialog__header {
    border-bottom: vh2(1) solid #DADFEA;
    padding: vh2(10) vh2(20);
    .el-dialog__title {
      font-weight: 500;
      font-size: vh2(18) !important;
    }

    .el-dialog__headerbtn {
      top: vh2(12);
      right: vh2(20);
      font-size: vh2(16) !important;
    }
  }

  .el-dialog__footer {
    width: 100%;
    color: #fff;
    font-size: vh2(12);
    padding: vh2(20);
  }

  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    min-height: vh2(85);
  }
}
.edu-btn {
  background: linear-gradient(90deg, #FBED96 0%, #ABECD6 100%);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: vh2(25);
  cursor: pointer;
  font-size: vh2(14);
  color: #000;
  width: vh2(80);
  height: vh2(30);
  line-height: vh2(30);
  text-align: center;
  margin-top: vh2(5);
}

.mb10 {
  margin-bottom: vh2(10);
}

.w300 {
  width: vh2(150);
}

.w60 {
  width: vh2(30);
}

.pt10 {
  padding-top: vh2(20);
}
.pt5 {
  padding-top: vh2(5);
}

.mh40 {
  min-height: vh2(40);
}

.option-width {
  width: calc(100% - #{vh2(30)})
}

.item-scope {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.tr {
  text-align: right;
}
</style>
