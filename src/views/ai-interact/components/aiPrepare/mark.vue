<template>
  <div class="home-t-mark">
    <div class="mark-head">
      <div class="m-title">评价</div>
      <div class="flex">
        <div>批阅前请查看评分标准</div>
        <div class="m-tips" @click="handleMark">
          评分标准
          <img class="m-img" :src="exclamatory" />
        </div>
      </div>
    </div>
    <div class="mark-content flex justify-between">
      <div class="fb-600 flex align-center">
        <el-input v-model.number="score" />
        分
      </div>
      <div class="m-btn" @click="_markAiCourseWork">提交</div>
    </div>
    <div v-if="showStandard" class="dialog-box">
      <div class="dialog">
        <div class="dialog-title">评分标准</div>
        <img class="close" :src="close" alt="" @click="showStandard = false" />
        <img class="dialog-img" :src="standradImg" />
      </div>
    </div>
  </div>
</template>

<script>
import { markAiCourseWork } from '@/api/aicourse'
import exclamatory from '@/assets/ai-image/icon/exclamatory.svg'
import close from '@/assets/ai-image/icon/close.svg'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return null
      }
    },
    aiWorkScore: {
      type: [Object, Array],
      default: () => {
        return null
      }
    },
    homeworkInfoItem: {
      type: [Object, Array],
      default: () => {
        return null
      }
    },
    workId: {
      type: [Number, String],
      default: ''
    }
  },
  data () {
    return {
      close,
      exclamatory,
      assistantId: this.$route.params.assistantId,
      score: 0,
      showStandard: false,
      standradImg: ''
    }
  },
  watch: {
    homeworkInfoItem: {
      handler (val) {
        if (val) {
          this.score = val.score
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleMark () {
      // 显示评分标准弹框
      this.showStandard = true
      if (this.aiWorkScore && this.aiWorkScore.length > 0) {
        this.standradImg = this.aiWorkScore[0].mediaFile.url
      } else {
        this.standradImg = ''
      }
    },
    async _markAiCourseWork () {
      if (this.score === '' || (+this.score > 100 || +this.score < 0)) {
        this.$message.error('请输入0-100的数字')
        return
      }
      const { data } = await markAiCourseWork({
        assistantUserId: this.assistantId,
        workId: this.workId,
        score: this.score
      })
      if (data.code !== 200) {
        this.$message.error(data.message)
        this.score = ''
      } else {
        this.$emit('markSuccess', this.workId)
        this.$message.success('成功')
      }
      // 评分

      // this.score = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.home-t-mark {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .mark-head {
    height: vh2(30);
    color: #4F4F4F;
    font-size: vh2(12);

    .m-title {
      font-weight: 500;
      font-size: vh2(16);
      color: #000000;
      margin-bottom: vh2(10);
    }
    .m-tips {
      color: #F2994A;
      margin-left: vh2(10);
      display: flex;
      align-items: center;
      cursor: pointer;
      text-decoration: underline;
      font-size: vh2(10);
    }

    .m-img {
      width: vh2(13);
      height: vh2(13);
    }

  }
  .mark-content {
    height: 100%;
    align-items: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: vh2(30);

    ::v-deep .el-input {
      width: vh2(90);
      height: vh2(40);
      border-radius: 5px;
      margin-right: vh2(5);
      .el-input__inner {
        border-color: #2D9CDB !important;
        font-size: vh2(30) !important;
        text-align: center;
        height: 100% !important;
        color: #333 !important;
      }
    }

    .m-btn {
      background: #F2C94C;
      width: vh2(60);
      height: vh2(34);
      color: #000000;
      font-size: vh2(12);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: vh2(10);
      cursor: pointer;
      margin-left: vh2(15);
    }

    .fb-600 {
      font-weight: 600;
    }
  }
}
.dialog-box {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 9999;
  left: 0;
  top: 0;
  background: rgba($color: #000000, $alpha: 0.7);
}
.dialog {
  width: vh2(502);
  height: vh2(300);
  background: #FFFFFF;
  border-radius: 5px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 999999;

  .dialog-title {
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 600;
      font-size: vh2(24);
      line-height: vh2(35);
      color: #000000;
      text-align: center;
      padding: vh2(18) 0 vh2(2);
  }

  .close {
      width: vh2(20);
      height: vh2(20);
      object-fit: contain;
      position: absolute;
      top: vh2(15);
      right: vh2(18);
      cursor: pointer;
  }

  img {
      height: vh2(225);
      object-fit: contain;
  }
}
</style>
