<template>
  <div class="w h flex a-main">
    <div class="tip-view">
      <div v-if="currInfo.time > 0">
        建议授课时长：{{ currInfo.time | timeFormat }}
      </div>
      <div v-else>
        建议授课时长：--
      </div>
      <div class="preview-btn" @click="handlePreview">
        预览
        <i class="el-icon-caret-right" style='margin-left: 10px;font-size: 26px'></i>
      </div>
    </div>
    <div class="a-card a-items">
      <div
        v-for="(dItem, key, index) in info"
        :key="key"
        class="li pointer"
        :class="nameIndex === key ? 'li-active' : ''"
        @click="handleName(key)"
      >
        <div class="w tc article-singer-container z-10">
          {{ index + 1 }}.{{ dItem | formateList }}
        </div>
      </div>
    </div>
    <div class="home-work-card">
      <Questions v-if="!load" :info="currInfo" />
    </div>
    <div class='preview-view' v-if='showPreview'>
      <iframe
        id="scratch-iframe"
        ref="scratchFrame"
        :src="iframeUrl"
        style="border: none"
        width="100%"
        height="100%"
        allowfullscreen
        allow="microphone *; camera *"
        sandbox="allow-same-origin allow-scripts allow-popups allow-modals allow-downloads"
      ></iframe>

    </div>
  </div>
</template>

<script>
import Questions from '@/views/ai-interact/components/aiPrepare/questions.vue'
import moment from 'moment/moment'
import { mapGetters } from 'vuex'
export default {
  components: {
    Questions
  },
  filters: {
    formateList (item) {
      // let str = ''
      // if (item.stepType === 'CHALLENGE') {
      //   switch (item.aiSectionType) {
      //     case 'VIDEO':
      //       str = '探索新知'
      //       break
      //     case 'THINK':
      //       str = '奇思妙想'
      //       break

      //     default:
      //       str = '挑战题目'
      //       break
      //   }
      // } else {
      //   switch (item.aiSectionType) {
      //     case 'VIDEO':
      //       str = '探索新知'
      //       break
      //     case 'THINK':
      //       str = '奇思妙想'
      //       break
      //     case 'SCREEN_INTERACT':
      //       str = '趣味竞赛'
      //       break

      //     default:
      //       str = '勤学勤练'
      //       break
      //   }
      // }
      return item.tagName
    },
    timeFormat (val) {
      if (!val) {
        return 0
      }
      const time = moment.duration(val, 'seconds') // 得到一个对象，里面有对应的时分秒等时间对象值
      const hours = time.hours()
      const minutes = time.minutes()
      const seconds = time.seconds()
      if (hours) {
        return moment({ h: hours, m: minutes, s: seconds }).format('HH时mm分ss秒')
      } else if (minutes) {
        return moment({ m: minutes, s: seconds }).format('mm分ss秒')
      } else {
        return moment({ s: seconds }).format('ss秒')
      }
    }
  },
  props: {
    info: {
      type: Object,
      default: () => {
        return null
      }
    },
    unitList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      viewOption: {
        ready: () => {
          // 自定义预览样式
          this.$viewer = this.$el.querySelector('.viewer').$viewer
          console.dir(this.$viewer)
          const div3 = document.createElement('div')
          div3.className = 'view-length'
          div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          const div = document.createElement('div')
          div.className = 'view-pre'
          div.innerHTML = `<i class="el-icon-arrow-left"></i>`
          div.onclick = () => {
            this.$viewer.prev(true)
            div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          }
          const div2 = document.createElement('div')
          div2.className = 'view-next'
          div2.innerHTML = `<i class="el-icon-arrow-right"></i>`
          div2.onclick = () => {
            this.$viewer.next(true)
            div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          }

          this.$viewer.canvas.parentNode.appendChild(div)
          this.$viewer.canvas.parentNode.appendChild(div2)
          this.$viewer.canvas.parentNode.appendChild(div3)
        },
        // 'inline': true,
        // 'button': false,
        'navbar': false,
        'title': false,
        'toolbar': false,
        'tooltip': false,
        'movable': false,
        'zoomable': false,
        'rotatable': false,
        'scalable': false,
        'transition': false
        // 'fullscreen': false
        // 'keyboard': false
      },
      studentCourseId: this.$route.params.studentCourseId,
      nameIndex: '',
      currInfo: null,
      load: false,
      showPreview: false,
      iframeUrl: ''
    }
  },
  watch: {
    info: {
      handler (val, oldVal) {
        if (val) {
          this.load = true
          this.nameIndex = Object.keys(val)[0]
          this.currInfo = val[this.nameIndex]
          this.load = false
        }
      },
      immediate: true
    }
  },
  mounted () {
    window.addEventListener('message', (e) => {
      if (e.data.type === 'prepareBack') {
        this.handleClose()
      }
    })
  },
  computed: {
    previewIndex () {
      let num = 0
      this.unitList.map((item, index) => {
        if (item.id === this.currInfo.id) {
          num = index
        }
      })
      return num
    },
    ...mapGetters(['userInfo'])
  },
  methods: {
    async homeWorkClick (type) {
      this.homeWorkIndex = type
    },
    handleName (type) {
      this.load = true
      this.nameIndex = type
      this.currInfo = this.info[this.nameIndex]
      this.load = false
    },
    handlePreview () {
      this.iframeUrl = `${window.location.origin}/ai/${this.$route.params.courseId}/0/${this.$route.params.unitId}?preview=1&previewIndex=${this.previewIndex}&source=prepare&userId=${this.userInfo.id}`
      this.showPreview = true
    },
    handleClose () {
      this.iframeUrl = ''
      this.showPreview = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';
.a-main {
  position: relative;
  .preview-view{
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 3;
    background: white;
  }
  .tip-view{
    position: absolute;
    right: 20px;
    top: -80px;
    display: flex;
    font-size: vh2(14);
    align-items: center;
    .preview-btn{
      width: 120px;
      height: 40px;
      background: linear-gradient(90deg, #FFB347 0%, #FFCC33 100%);
      border-radius: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 20px;
      padding-left: 10px;
      box-sizing: border-box;
      &:hover{
        opacity: 0.7;
      }
    }
  }
}
.a-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: vh2(10);
  height: vh2(450);
  border-radius: vh2(10);
  box-sizing: border-box;
  @include aiScrollBar;
}
.tc {
  text-align: center;
}

.a-items {
  width: vh2(110);
  margin-right: vh2(10);
  overflow-y: auto;
  .li {
    width: 100%;
    height: vh2(30);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #000;
    font-size: vh2(14);
    margin-bottom: vh2(30);
    padding: 0 vh2(5);
    box-sizing: border-box;
    position: relative;
    z-index: 2;
    &:last-child {
      margin-bottom: 0;
    }
    &:hover {
      &::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: linear-gradient(90deg, #FFB347 0%, #FFCC33 100%);
        filter: blur(#{vh2(5)});
        border-radius: vh2(6);
      }
    }
  }

  .li-active {
    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background: linear-gradient(90deg, #FFB347 0%, #FFCC33 100%);
      filter: blur(#{vh2(5)});
      border-radius: vh2(6);
    }
  }
}

.home-work-card {
  width: calc(100% - #{vh2(110)} - #{vh2(20)});
  height: vh2(450);
  padding: vh2(10);
  background: rgba(255, 255, 255, 0.3);
  border: vh2(1) solid #FFFFFF;
  border-radius: vh2(10);
  box-sizing: border-box;
  @include aiScrollBar;
}
</style>
<style lang="scss">
.view-pre {
  left: 10px;
}

.view-next {
  right: 10px;
}

.view-length {
  position: absolute;
  bottom: 8px;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  // background-color: rgba(0, 0, 0, 50%);
  overflow: hidden;
}

.view-next, .view-pre {
  position: absolute;
  top: 45%;
  color: #000;
  font-weight: 500 !important;
  font-size: 25px;
  width: 50px;
  height: 50px;
  background: #F2C94C;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
}
</style>
