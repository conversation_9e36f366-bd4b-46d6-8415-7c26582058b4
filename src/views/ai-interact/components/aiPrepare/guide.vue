<template>
  <div v-loading="iframeLoading" class="w h a-card">
    <iframe class="w h" :src="`${info.url}#toolbar=0`" frameborder="0" @load="iframeLoad"></iframe>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {
      iframeLoading: false
    }
  },
  created () {
    this.iframeLoading = true
  },
  methods: {
    iframeLoad () {
      this.iframeLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';
.a-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: vh2(10);
  background: rgba(255, 255, 255, 0.3);
  border: vh2(1) solid #FFFFFF;
  border-radius: vh2(10);
  box-sizing: border-box;
  @include aiScrollBar;
}
</style>
