<template>
  <div v-if="info" class="w h question-content">
    <template v-if="info.aiSectionType === 'VIDEO' && typeFrom === 'hudong'">
      <div class="video-box">
        <video-player
          v-if="videoShow"
          ref="videoPlayer"
          class="video-player object-fill vjs-big-play-centered"
          :options="playerOptions"
          x5-video-player-type="h5"
          :webkit-playsinline="true"
          :webkit-inline="true"
          :playsinline="true"
          custom-event-name="customstatechangedeventname"
          @ready="playerReadied"
          @canplay="onPlayerCanplay($event)"
          @play="onPlayerPlay($event)"
          @pause="onPlayerPause($event)"
          @ended="onPlayerEnd($event)"
          @contextmenu="onPlayerContextmenu($event)"
        />
      </div>
      <div class="detail v-detail">
        {{ typeFrom === 'hudong' ? '课前互动说明' : '教学内容说明' }}
      </div>
      <div class="detail2 v-detail2" v-html="description || '暂无说明'">
        <!-- {{description || '暂无说明'}} -->
      </div>
    </template>
    <template v-if="info.aiSectionType === 'VIDEO' && typeFrom !== 'hudong'">
      <div class="w h-60 mb-10 flex justify-between">
        <div class="video-box2">
          <video-player
            v-if="videoShow"
            ref="videoPlayer"
            class="video-player object-fill vjs-big-play-centered"
            :options="playerOptions"
            x5-video-player-type="h5"
            :webkit-playsinline="true"
            :webkit-inline="true"
            :playsinline="true"
            custom-event-name="customstatechangedeventname"
            @ready="playerReadied"
            @canplay="onPlayerCanplay($event)"
            @play="onPlayerPlay($event)"
            @pause="onPlayerPause($event)"
            @ended="onPlayerEnd($event)"
            @contextmenu="onPlayerContextmenu($event)"
          />
        </div>
        <!-- <div class="mark-box">
          <div class="mark-title">
            <div>课程备注</div>
          </div>
          <div class="mark">
            <el-input
              v-model="mark"
              type="textarea"
              placeholder="请输入备注内容，点击空白处将自动保存"
              maxlength="200"
              show-word-limit
              resize="none"
              @blur="autoSave"
            />
          </div>
        </div> -->
      </div>
      <div class="detail v-detail">
        {{ typeFrom === 'hudong' ? '课前互动说明' : '教学内容说明' }}
      </div>
      <div class="detail2 v-detail2" style="margin-bottom: 20px !important;" v-html="description || '暂无说明'">
        <!-- {{description || '暂无说明'}} -->
      </div>

      <div class="detail v-detail">
        线下带班建议
      </div>
      <div class="detail2 v-detail2" v-html="advice || '暂无'">
      </div>
    </template>
    <template v-if="info.aiSectionType === 'THINK'">
      <div class="w h-60 flex justify-between mb-10">
        <div class="video-box2 think-box">
          <img class="max-img" :src="info.mediaFile.url" />
        </div>
        <div v-show="studentCourseId !== 0" class="mark-box">
          <div class="mark-title">
            <div>课程备注</div>
            <!-- <div style="font-size: 10px;">限200字</div> -->
          </div>
          <div class="mark">
            <el-input
              v-model="mark"
              type="textarea"
              placeholder="请输入备注内容，点击空白处将自动保存"
              maxlength="200"
              resize="none"
              show-word-limit
              @blur="autoSave"
            />
          </div>
        </div>
      </div>
      <div class="detail v-detail">教学内容说明</div>
      <div class="detail2 v-detail2" style="margin-bottom: 20px !important;" v-html="description || '暂无说明'">
      </div>
      <div class="detail v-detail">
        线下带班建议
      </div>
      <div class="detail2 v-detail2" v-html="advice || '暂无'">
      </div>
    </template>
    <template v-if="info.aiSectionType === 'TRAINING'">
      <div class='training-view'>
        <div class='training-box' :style='boxStyle'>
          <Scratch ref="scratchRef" :section-id="info.id"/>
          <Common ref="commonRef" />
          <GameTraining ref='gameRef' :student-course-id="studentCourseId"/>
          <PythonView ref="pythonRef" />
        </div>
      </div>
    </template>
    <template v-if="info.aiSectionType === 'SCREEN_INTERACT'">
      <div v-loading="iframeLoading" class="w h">
        <iframe v-if="game && game.url" class="w h" :src="game.url" frameborder="0" @load="iframeLoad"></iframe>
      </div>
    </template>
    <template v-else-if="typeFrom === 'resource'">
      <div v-if="info.type === 'VIDEO'" class="video-box">
        <video-player
          v-if="videoShow"
          ref="videoPlayer"
          class="video-player object-fill vjs-big-play-centered"
          :options="playerOptions"
          x5-video-player-type="h5"
          :webkit-playsinline="true"
          :webkit-inline="true"
          :playsinline="true"
          custom-event-name="customstatechangedeventname"
          @ready="playerReadied"
          @canplay="onPlayerCanplay($event)"
          @play="onPlayerPlay($event)"
          @pause="onPlayerPause($event)"
          @ended="onPlayerEnd($event)"
          @contextmenu="onPlayerContextmenu($event)"
        />
      </div>
      <div v-else-if="info.type === 'IMAGE'" class="video-box mb-10">
        <img class="max-img" :src="info.url" />
      </div>
      <div class="detail v-detail">课程材料说明</div>
      <div class="detail2 v-detail2" v-html="description || '暂无说明'">
        <!-- {{description || '暂无说明'}} -->
      </div>
    </template>
    <template v-else-if="info.aiSectionType === 'QUESTION'">
      <div
        class="q-box"
        :class="{ 'overflow-y': question && question.questionType !== 'MATCH' }"
      >
        <template
          v-if="question && (question.questionType === 'CHOICE' || question.questionType === 'SIMPLE_CHOOSE')"
        >
          <template v-if="questionTypeObj[questionTypeIndex] === 'TEXT'">
            <div class="q-title" v-html="question.question">
            </div>
            <div
              v-for="(item, index) in question.answerOptionList"
              :key="item.id"
              class="q-item"
              :class="item.id + '' === answer ? 'correct' : ''"
            >
              <span style="padding-right: 10px">{{ index | answerName }}</span>
              <template v-if="item.answerType === 'TEXT'">
                {{ item.answer }}
              </template>
              <template v-else>
                <img class="img1" :src="item.answer" />
              </template>
            </div>
          </template>
          <template
            v-else-if="questionTypeObj[questionTypeIndex] === 'TITLE_IMAGE'"
          >
            <div class="q-title" v-html="question.question">
            </div>
            <div class="q-item2">
              <div class="left-item">
                <img class="max-img" :src="question.mediaUrl" />
              </div>
              <div class="right-item">
                <div
                  v-for="(item, index) in question.answerOptionList"
                  :key="item.id"
                  class="q-item"
                  :class="item.id + '' === answer ? 'correct' : ''"
                >
                  <span style="padding-right: 10px">{{
                    index | answerName
                  }}</span>
                  <template v-if="item.answerType === 'TEXT'">
                    {{ item.answer }}
                  </template>
                  <template v-else>
                    <img class="img1" :src="item.answer" />
                  </template>
                </div>
              </div>
            </div>
          </template>
          <template
            v-else-if="questionTypeObj[questionTypeIndex] === 'ANSWER_IMAGE'"
          >
            <div class="q-title" v-html="question.question">
            </div>
            <div class="flex">
              <div
                v-for="(item, index) in question.answerOptionList"
                :key="item.id"
                class="q-item q-item3"
                :class="item.id + '' === answer ? 'correct' : ''"
              >
                <div>
                  <template v-if="item.answerType === 'TEXT'">
                    {{ item.answer }}
                  </template>
                  <template v-else>
                    <img class="img1" :src="item.answer" />
                  </template>
                </div>
                <div style="padding-top: 10px">{{ index | answerName }}</div>
              </div>
            </div>
          </template>
        </template>
        <template v-else-if="question && question.questionType === 'MATCH'">
          <div
            v-if="question.question || question.mediaUrl"
            class="q-title2"
            :title="question.question"
            v-html="question.question"
          >
          </div>
          <div class="flex justify-between">
            <div style="width: 40%">
              <div
                v-for="item in matchLeft"
                :key="item.id"
                ref="leftList"
                class="q-item4"
                :title="item.answerType === 'TEXT' ? item.answer : ''"
              >
                <div :id="item.id + ''" class="img2-box">
                  <template v-if="item.answerType === 'TEXT'">
                    {{ item.answer }}
                  </template>
                  <template v-else>
                    <!-- <img class="img2" :src="item.answer" /> -->
                    <el-image class="img2" :fit="'cover'" :src="item.answer">
                      <div slot="placeholder" class="image-slot">
                        加载中<span class="dot">...</span>
                      </div>
                    </el-image>
                  </template>
                </div>
              </div>
            </div>
            <div style="width: 40%">
              <div
                v-for="item in matchRight"
                :key="item.id"
                ref="rightList"
                class="q-item4"
              >
                <div :id="item.id + ''" class="img2-box">
                  <template v-if="item.answerType === 'TEXT'">
                    {{ item.answer }}
                  </template>
                  <template v-else>
                    <!-- <img class="img2" :src="item.answer" /> -->
                    <el-image class="img2" :fit="'cover'" :src="item.answer">
                      <div slot="placeholder" class="image-slot">
                        加载中<span class="dot">...</span>
                      </div>
                    </el-image>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div class="answer-box">
        <div>
          <template
            v-if="question && (question.questionType === 'CHOICE' || question.questionType === 'SIMPLE_CHOOSE')"
          >正确答案 {{ answerItem }}</template>
        </div>
        <div>
          <el-rate
            v-if="question"
            v-model="question.level"
            disabled
            text-color="#F2994A"
            disabled-void-icon-class="el-icon-star-off"
            disabled-void-color="#F2994A"
          />
        </div>
      </div>
      <div class="mb-10 question-detail">
        <div class="question-title">【题目解析】</div>
        <div class="question-item">
          {{ (question && question.analysis) || '暂无解析' }}
        </div>
      </div>
      <div class="detail2 mb-10">
        【题目类型】{{ (question && question.questionType) | questionName }}
      </div>
      <div class="detail2 mb-10">
        【答题倒计时】{{ (question && question.time) | timeFormate }}
      </div>
    </template>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import PythonView from '@/views/ai/components/scratch/components/pythonView'
import GameTraining from '@/views/ai/components/scratch/components/gameTraining'
import Common from '@/views/ai/components/scratch/components/common'
import Scratch from '@/views/ai/components/scratch/components/scratch'
import { getAiCourseUnitSection, getTrainingPresetFile, saveCoursewareRemark } from '@/api/aicourse'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
import { videoPlayer } from 'vue-video-player'
window.videojs = require('video.js')
// require("video.js/dist/lang/zh-CN")
import { jsPlumb } from 'jsplumb'

import StreamToBlob from 'stream-to-blob'
import { Readable } from 'stream'
import moment from 'moment'
import { mapGetters } from 'vuex'
export default {
  components: {
    videoPlayer,
    Scratch,
    Common,
    GameTraining,
    PythonView
  },
  filters: {
    answerName (val) {
      return ['A', 'B', 'C', 'D', 'E', 'F'][val]
    },
    questionName (val) {
      const obj = {
        CHOICE: '选择题',
        SIMPLE_CHOOSE: '选择题',
        TEXT_IMAGE_CHOICE: '图文题',
        FILL_IN_THE_BLANK: '',
        MATCH: '连线题'
      }
      return obj[val]
    },
    timeFormate (val) {
      if (!val) {
        return 0
      }
      const time = moment.duration(val, 'seconds') // 得到一个对象，里面有对应的时分秒等时间对象值
      const hours = time.hours()
      const minutes = time.minutes()
      const seconds = time.seconds()
      if (hours) {
        return moment({ h: hours, m: minutes, s: seconds }).format('HH时mm分ss秒')
      } else if (minutes) {
        return moment({ m: minutes, s: seconds }).format('mm分ss秒')
      } else {
        return moment({ s: seconds }).format('ss秒')
      }
    }
  },
  props: {
    info: {
      type: Object
    },
    typeFrom: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      courseId: this.$route.params.courseId,
      studentCourseId: this.$route.params.studentCourseId,
      unitIndex: this.$route.params.index,
      unitId: this.$route.params.unitId,
      videoLoad: false,
      playerOptions: {
        language: 'zh-CN',
        // controls: true,
        autoplay: false,
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'progressControl' }, // 播放进度条
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'timeDivider' },
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            { // 倍数播放
              name: 'playbackRateMenuButton',
              'playbackRates': [0.5, 1, 1.5, 2, 2.5]
            },
            { name: 'FullscreenToggle' } // 全屏
          ]
        },
        sources: [
          {
            type: 'video/mp4',
            src: ''
          }
        ],
        hls: true,
        fluid: false,
        aspectRatio: '16:9',
        notSupportedMessage: '此视频暂无法播放,请稍后再试'
      },
      videoShow: false,
      description: '',
      advice: '',
      mark: '',
      question: null,
      answer: null,
      questionTypeObj: [
        'TEXT',
        'TITLE_IMAGE',
        'ANSWER_IMAGE',
        'TITLE_ANSWER_IMAGE'
      ],
      questionTypeIndex: 0,
      matchLeft: null,
      matchRight: null,
      answerItem: 0,
      jsPlumb: null,
      game: null,
      iframeLoading: false,
      trainingData: null,
      boxScale: 1
    }
  },
  computed: {
    player () {
      return this.$refs.videoPlayer.player
    },
    boxStyle () {
      return {
        transform: `scale(${this.boxScale})`
      }
    },
    ...mapGetters(['userInfo'])
  },
  watch: {
    info () {
      this.init()
    }
  },
  mounted () {
    this.init()
    this.jsPlumb = jsPlumb.getInstance({
      PaintStyle: {
        strokeWidth: 3,
        stroke: '#27AE60'
      },
      Anchors: ['Right', 'Left'],
      Connector: 'Straight',
      DragOptions: { cursor: 'crosshair' },
      Endpoints: ['Blank', 'Blank']
    })
    // this.jsPlumb = jsPlumb.importDefaults({
    //   PaintStyle : {
    //     strokeWidth:3,
    //     stroke: '#00E949'
    //   },
    //   Anchors: ["Right", "Left"],
    //   Connector : "Straight",
    //   DragOptions: { cursor: "crosshair" },
    //   Endpoints: [ 'Blank', 'Blank' ],
    // });
    document
      .querySelector('.question-content')
      .addEventListener('contextmenu', (event) => {
        event.preventDefault()
      })
  },
  beforeDestroy () {
    this.deleteEveryConnection()
  },

  methods: {
    init () {
      if (!this.typeFrom) {
        this.getList()
      } else if (this.typeFrom === 'hudong') {
        this.playVideo(this.info)
        this.description = this.info.description.split('\n').join('<br><br>')
      } else if (this.typeFrom === 'resource') {
        if (this.info.type === 'VIDEO') {
          this.playVideo(this.info)
        }
        this.description = this.info.description.split('\n').join('<br><br>')
      }
      if (this.info.aiSectionType === 'TRAINING'){
        this.$nextTick(() => {
          const trainingViewWidth = document.querySelector('.training-view').clientWidth
          const trainingViewHeight = document.querySelector('.training-view').clientHeight
          const trainingBoxWidth = document.querySelector('.training-box').clientWidth
          const trainingBoxHeight = document.querySelector('.training-box').clientHeight
          this.boxScale = Math.min(
            trainingViewWidth / trainingBoxWidth,
            trainingViewHeight / trainingBoxHeight
          )
        })
      }
    },
    async getTrainingPresetFile (trainingData) {
      const params = {
        trainingId: trainingData.trainingId,
        userId: Cookies.get('assistant-id')
      }
      await getTrainingPresetFile(params)
        .then(response => {
          if (response.data.code === 200) {
            window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
          } else {
            this.$message.error(response.data.message || '获取文件失败')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('获取文件失败')
        })
    },
    getList () {
      this.deleteEveryConnection()
      getAiCourseUnitSection({
        aicourseUnitSectionId: this.info.id,
        studentCourseId: this.$route.params.studentCourseId
      })
        .then((response) => {
          if (+response.data.code === 200) {
            const { data } = response.data
            this.videoShow = false
            this.question = null
            this.game = data.game
            if (data.aiSectionType === 'THINK') {
              this.advice = data.advice.split('\n').join('<br><br>')
              this.description = data.mediaFile.description.split('\n').join('<br><br>')
              this.mark = data.teacherRemark
            } else if (data.aiSectionType === 'VIDEO') {
              this.playVideo(data.mediaFile)
              this.advice = data.advice.split('\n').join('<br><br>')
              this.description = data.mediaFile.description.split('\n').join('<br><br>')
              this.mark = data.teacherRemark
            } else if (data.aiSectionType === 'TRAINING') {
              this.$refs.scratchRef.back()
              this.$refs.commonRef.back()
              this.$refs.pythonRef.back()
              this.$refs.gameRef.back()
              this.trainingData = data.training
              if (this.trainingData.trainingType === 'SCRATCH_PRACTICE') {
                this.$refs.scratchRef.open(this.trainingData, 'prepare')
              } else if (this.trainingData.trainingType === 'COMM_PRACTICE') {
                this.$refs.commonRef.open(this.trainingData)
              } else if (this.trainingData.trainingType === 'PYTHON_PRACTICE') {
                // this.$refs.pythonRef.open(this.trainingData)
                this.getTrainingPresetFile(this.trainingData)
              } else if (this.trainingData.trainingType === 'FINACE_PRACTICE') {
                this.$refs.commonRef.open(this.trainingData)
              } else {
                this.$refs.gameRef.open(data)
              }
            } else if (data.aiSectionType === 'QUESTION') {
              this.question = data.question
              this.question.question = this.question.question.split('\n').join('<br><br>')
              if (data.question.questionType === 'CHOICE' || data.question.questionType === 'SIMPLE_CHOOSE') {
                this.answer = data.question.answer
                data.question.answerOptionList.map((val, index) => {
                  if (val.id + '' === data.question.answer) {
                    this.answerItem = ['A', 'B', 'C', 'D', 'E', 'F'][index]
                  }
                })
                // const style = ['TEXT', 'TITLE_IMAGE', 'ANSWER_IMAGE', 'TITLE_ANSWER_IMAGE']
                if (data.question.mediaUrl) {
                  this.questionTypeIndex = 1
                } else if (
                  data.question.answerOptionList[0].answerType === 'TEXT'
                ) {
                  this.questionTypeIndex = 0
                } else if (
                  data.question.answerOptionList[0].answerType === 'IMAGE'
                ) {
                  this.questionTypeIndex = 2
                }
              } else if (data.question.questionType === 'MATCH') {
                if (data.question.answer) {
                  this.answer = data.question.answer.split(',').map((val) => {
                    return val.split('-')
                  })
                } else {
                  this.answer = []
                }
                // console.log(this.answer);
                this.matchLeft = []
                this.matchRight = []
                const leftIds = []
                const rightIds = []
                data.question.answerOptionList.map((val) => {
                  if (val.side === 0) {
                    this.matchLeft.push(val)
                    leftIds.push(val.id + '')
                  } else if (val.side === 1) {
                    this.matchRight.push(val)
                    rightIds.push(val.id + '')
                  }
                })
                // console.log(this.matchLeft)
                // console.log(this.matchRight)
                // console.log(leftIds)
                // console.log(rightIds)
                // jsPlumb.importDefaults({
                //   PaintStyle : {
                //     strokeWidth:3,
                //     stroke: '#00E949'
                //   },
                //   Anchors: ["Right", "Left"],
                //   Connector : "Straight",
                //   DragOptions: { cursor: "crosshair" },
                //   Endpoints: [ 'Blank', 'Blank' ],
                // });
                // this.$nextTick(() => {

                // })
                setTimeout(() => {
                  this.answer.map((val) => {
                    if (leftIds.indexOf(val[0]) !== -1) {
                      this.jsPlumb.connect({
                        source: val[0],
                        target: val[1]
                      })
                    } else {
                      this.jsPlumb.connect({
                        source: val[1],
                        target: val[0]
                      })
                    }
                  })
                }, 0)
              }
            }
          }
        })
        .catch((err) => {
          this.$toast(err, {
            position: 'center',
            duration: '2000'
          })
        })
    },
    deleteEveryConnection () {
      this.jsPlumb && this.jsPlumb.deleteEveryConnection()
      this.jsPlumb && this.jsPlumb.reset()
    },
    playerReadied () {
      this.$refs.videoPlayer.player.addClass('vjs-custom-waiting')
    },
    playVideo (videoObj) {
      this.videoInfo = {
        url: videoObj.url,
        cover: videoObj.coverUrl
      }
      this.playerOptions.sources[0].src = videoObj.url
      this.videoShow = true
      // if (window.ipc) {
      //   this.loadVideo(videoObj.url)
      // }
    },
    onPlayerCanplay (player) {
      console.log('onPlayerCanplay', player)
    },
    onPlayerPlay (player) {
      console.log('onPlayerPlay', player)
      this.$refs.videoPlayer.player.removeClass('vjs-custom-waiting')
    },
    onPlayerPause (player) {
      player.bigPlayButton.show()
      console.log('onPlayerPause', player)
    },
    onPlayerEnd (player) {
      console.log('onPlayerEnd', player)
    },
    onPlayerContextmenu (event) {
      console.log(event)
      event.preventDefault()
    },
    loadVideo (url) {
      const fileName = url.split('/').reverse()[0].split('.')[0]
      window.ipc.invoke('loadLocalVideo', fileName).then(buffer => {
        // console.log(buffer)
        const stream = this.bufferToStream(buffer)
        let fileUrl // blob对象
        StreamToBlob(stream)
          .then(res => {
            fileUrl = res
            // console.log(fileUrl);

            // 将blob对象转成blob链接
            this.sourceUrl = window.URL.createObjectURL(fileUrl)
            // console.log(filePath);
            if (this.player) {
              // 动态设置播放地址
              this.player.src([{
                src: this.sourceUrl,
                type: 'video/mp4'
              }])
            }
          })
          .catch(err => {
            console.log(err)
          })
      })
    },
    bufferToStream (binary) {
      const readableInstanceStream = new Readable({
        read () {
          this.push(binary)
          this.push(null)
        }
      })
      return readableInstanceStream
    },
    async autoSave () {
      await saveCoursewareRemark({
        aiCourseUnitId: this.info.aiCourseUnitId,
        unitSectionId: this.info.id,
        studentCourseId: this.studentCourseId,
        remark: this.mark
      })
      this.$message.success('已自动保存备注')
    },
    iframeLoad () {
      this.iframeLoading = false
    }
  }
}
</script>

<style scoped lang="scss">
@function UI_h($px) {
  @return $px * 100vh / 650;
}
@function UI_w($px) {
  @return $px * 100vw / 965;
}
@import '@/styles/mixin';

// .question-content {
//   overflow-y: auto;
//   @include aiScrollBar;
// }

// .think-box {
//   border: 2px solid #4fbcff;
//   border-image: linear-gradient(#4fbcff, #f0f9ff) 1 1;
//   background: #0b2442;
// }
.max-img {
  max-width: 100%;
  max-height: 100%;
  height: auto;
  width: auto;
}

.video-box {
  width: 100%;
  height: 70%;
  margin: auto;

  img {
    object-fit: contain;
  }
  // ::v-deep .video-js {
  //   .vjs-big-play-button {
  //     top: 45%;
  //     left: 45%;
  //   }
  // }

  ::v-deep video{
    max-width: 100% !important;
    max-height: 100% !important;
    // object-fit:fill;    //消除留白
  }
  ::v-deep .video-js.vjs-fluid {
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
    padding-top: 0 !important;
  }
}

.h-60 {
  height: 40%;
}

.mb-10 {
  margin-bottom: vh2(10);
}

.mark-box {
  width: 45%;
  height: 100%;
  .mark-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    height: vh2(25);
    font-size: vh2(12);
    color: #000000;
  }
  .mark {
    width: 100%;
    height: calc(100% - #{vh2(25)});
    border-radius: vh2(2);
    box-sizing: border-box;
    ::v-deep .el-textarea {
      height: 100% !important;
    }
    ::v-deep .el-textarea__inner {
      background: rgba(255, 255, 255, 0.5) !important;
      border: 1px solid #E0E0E0 !important;
      color: #828282 !important;
      height: 100% !important;
    }
    ::v-deep .el-input__count {
      background: transparent !important;
    }
  }
}

.video-box2 {
  width: 50%;
  height: 100%;
  // margin: auto;

  img {
    object-fit: contain;
  }

  ::v-deep video{
    max-width: 100% !important;
    max-height: 100% !important;
    // object-fit:fill;    //消除留白
  }
  ::v-deep .video-js.vjs-fluid {
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
    padding-top: 0 !important;
  }
}

.v-detail {
  // width: 800px !important;
  margin: auto !important;
}
.v-detail2 {
  // width: 800px !important;
  margin: vh2(10) auto !important;
  word-break: break-all;
}

.detail {
  color: #000;
  font-weight: 500;
  font-size: vh2(16);
  width: 100%;
  margin: vh2(10) 0;
}

.detail2 {
  width: 100%;
  font-size: vh2(12);
  font-weight: 300;
  color: #000;
  max-height: vh2(80);
  overflow-y: auto;
  @include aiScrollBar;
}

.question-detail {
  display: flex;
  width: 100%;
  font-size: vh(12);
  color: #000;
  .question-title{
    font-size: vh2(12);
    font-weight: 300;
    color: #000;
  }
  .question-item {
    flex: 1;
    font-weight: 300;
    overflow-y: auto;
    max-height: vh2(80);
    font-size: vh2(12);
    @include aiScrollBar;
  }
}

.video-player {
  height: 100%;
  width: 100%;
}
.overflow-y {
  overflow-y: auto;
}

.q-box {
  width: 100%;
  height: 55%;
  border: 1px solid #F2C94C;
  background: rgba(255, 255, 255, 0.3);
  color: #000000;
  padding: vh2(20) vh2(15);
  box-sizing: border-box;
  border-radius: vh2(10);

  .q-title {
    font-size: vh2(14);
    margin-bottom: vh2(20);
    font-weight: 600;
    // @include ellipsisMore(2);
    // max-height: 73px;
  }

  .q-title2 {
    font-size: vh2(14);
    margin-bottom: vh2(20);
    // @include ellipsisMore(2);
    // max-height: vh2(70);
  }

  .q-item {
    width: 100%;
    font-size: vh2(14);
    margin-bottom: vh2(10);
    // @include ellipsis;
  }

  .q-item4 {
    width: 100%;
    font-size: vh2(14);
    margin-bottom: vh2(15);
  }

  .q-item2 {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: flex-start;

    .left-item {
      width: vh2(170);
      height: vh2(170);
      margin-right: vh2(20);
    }
    .right-item {
      width: calc(100% - vh2(300));
    }
  }

  .q-item3 {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: vh2(30);
  }

  .correct {
    color: #27AE60;
    font-weight: 600;
  }

  .img1 {
    width: vh2(50);
    height: vh2(50);
    vertical-align: middle;
  }

  .img2 {
    width: vh2(40);
    height: vh2(40);
    vertical-align: middle;
  }

  .image-slot {
    font-size: vh2(12);
    color: #E0E0E0;
  }

  .img2-box {
    height: vh2(40);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.answer-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-weight: 500;
  font-size: vh2(16);
  color: #000;
  padding: vh2(10) 0;
  ::v-deep .el-icon-star-on {
    font-size: 22px !important;
  }
}
.mb-10 {
  margin-bottom: 10px;
}

.training-view{
  width: 100%;
  height: 100%;
  .training-box {
    width: 1223px;
    height: 844px;
    position: relative;
    transform-origin: left top;
  }
}

</style>

