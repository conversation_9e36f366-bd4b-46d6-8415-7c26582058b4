<template>
  <div class="w h a-card">
    <Questions :info="info.mediaFile" :type-from="'resource'" />
  </div>
</template>

<script>
import Questions from '@/views/ai-interact/components/aiPrepare/questions.vue'
export default {
  components: {
    Questions
  },
  props: {
    info: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {}
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';
.a-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: vh2(10);
  background: rgba(255, 255, 255, 0.3);
  border: vh2(1) solid #FFFFFF;
  border-radius: vh2(10);
  box-sizing: border-box;
  @include aiScrollBar;
}
</style>
