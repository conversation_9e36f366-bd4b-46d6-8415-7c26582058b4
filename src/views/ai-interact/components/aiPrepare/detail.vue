<template>
  <div class="w h a-card">
    <div class="w h course-sub">
      <div class="w flex align-center item-box">
        <div class="c-title-box">
          课程名称：
        </div>
        <div class="c-title-item-box">
          <div class="w article-singer-container">
            {{ (unitInfo && unitInfo.title) || '' }}
          </div>
        </div>
      </div>

      <div class="w flex align-center item-box">
        <div class="c-title-box">
          时<span class="mr40"></span>长：
        </div>
        <div class="c-title-item-box">
          <div v-if="unitInfo" class="w article-singer-container">
            <!-- {{ unitInfo && Math.floor(unitInfo.duration / 60) }}分钟 -->
            {{ unitInfo.duration | timeFormate }}
          </div>
        </div>
      </div>

      <div class="w flex align-center item-box">
        <div class="c-title-box">
          课程流程：
        </div>
        <div class="c-title-item-box">
          <div class="w article-singer-container">
            {{ unitInfo && unitInfo.unitStepStr }}
          </div>
        </div>
      </div>

      <div v-if="unitInfo && unitInfo.subTitle.length > 0" class="w flex flex-col">
        <div class="c-title-box item-box w flex align-center mb10">
          教学目标：
        </div>
        <div
          v-for="(item, index) in unitInfo.subTitle"
          :key="'s' + index"
          class="sub-title"
          :class="index === unitInfo.subTitle.length - 1 ? 'mb10' : 'mb5'"
        >
          {{ item }}
        </div>
      </div>

      <div v-if="unitInfo && unitInfo.difficultPoint.length > 0" class="w flex flex-col">
        <div class="c-title-box item-box w flex align-center ">
          教学重难点：
        </div>
        <div
          v-for="(item, index) in unitInfo.difficultPoint"
          :key="'d' + index"
          class="sub-title"
          :class="index === unitInfo.difficultPoint.length - 1 ? 'mb10' : 'mb5'"
        >
          {{ item }}
        </div>
      </div>

      <div class="w flex align-center item-box">
        <div class="c-title-box">
          题目平均难度：
        </div>
        <div class="c-title-item-box">
          <div class="w article-singer-container">
            <el-rate
              v-if="unitInfo"
              v-model="unitInfo.avgLv"
              disabled
              text-color="#F2994A"
              disabled-void-icon-class="el-icon-star-off"
              disabled-void-color="#F2994A"
            />
          </div>
        </div>
      </div>

      <div class="w flex align-center item-box">
        <div class="c-title-box">
          题目总数量：
        </div>
        <div class="c-title-item-box">
          <div class="w article-singer-container">
            {{
              (unitInfo &&
                unitInfo.totalChallengeQuestion +
                unitInfo.totalCommonQuestion) ||
                0
            }}道
          </div>
        </div>
      </div>

      <div class="w flex align-center item-box">
        <div class="c-title-box">
          课中互动题目数量：
        </div>
        <div class="c-title-item-box">
          <div class="w article-singer-container">
            {{ (unitInfo && unitInfo.thinkTimes) || 0 }}道
          </div>
        </div>
      </div>

      <div v-if="unitInfo && unitInfo.gain.length > 0" class="w flex flex-col">
        <div class="c-title-box item-box w flex align-center ">
          课堂收获：
        </div>
        <div
          v-for="(item, index) in unitInfo.gain"
          :key="'d' + index"
          class="sub-title"
          :class="index === unitInfo.gain.length - 1 ? 'mb10' : 'mb5'"
          :style="item.charAt(0) === '#'? 'font-weight: bold;' : ''"
        >
          {{ item.charAt(0) === '#' ? item.substring(1) :item }}
        </div>
      </div>
    <!-- <div class="w flex align-center item-box">
      <div class="c-title-box">
        终极挑战题目数量：
      </div>
      <div class="c-title-item-box">
        <div class="w article-singer-container">
          {{ (unitInfo && unitInfo.totalChallengeQuestion) || 0 }}道
        </div>
      </div>
    </div> -->
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  filters: {
    timeFormate (val) {
      if (!val) {
        return 0
      }
      const time = moment.duration(val, 'seconds') // 得到一个对象，里面有对应的时分秒等时间对象值
      const hours = time.hours()
      const minutes = time.minutes()
      const seconds = time.seconds()
      if (hours) {
        return moment({ h: hours, m: minutes, s: seconds }).format('HH时mm分ss秒')
      } else if (minutes) {
        return moment({ m: minutes, s: seconds }).format('mm分ss秒')
      } else {
        return moment({ s: seconds }).format('ss秒')
      }
    }
  },
  props: {
    pUnitInfo: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {
      unitInfo: null
    }
  },
  watch: {
    pUnitInfo: {
      handler (val) {
        if (val) {
          this.getList()
        }
      },
      immediate: true
    }
  },
  methods: {
    async getList () {
      const data = JSON.parse(JSON.stringify(this.pUnitInfo))
      if (data.data.subTitle) {
        data.data.subTitle = data.data.subTitle.split('\n')
      }
      if (data.data.difficultPoint) {
        data.data.difficultPoint = data.data.difficultPoint.split('\n')
      }
      if (data.data.gain) {
        data.data.gain = data.data.gain.split('\n')
      }
      const arr = []
      data.data.unitStepList.map(val => {
        if (val.title) {
          arr.push(val.title)
        } else {
          if (val.sectionStepType === 'CHALLENGE') {
            arr.push('终极挑战')
          } else {
            arr.push('环节' + val.indexNo)
          }
        }
      })
      data.data.unitStepStr = arr.join('-')
      this.unitInfo = data.data
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/mixin';
.a-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: vh2(10);
  background: rgba(255, 255, 255, 0.3);
  border: vh2(1) solid #FFFFFF;
  border-radius: vh2(10);
  box-sizing: border-box;
  @include aiScrollBar;
}

.course-sub {
  padding: vh2(10);
  box-sizing: border-box;
  overflow-y: auto;
  @include aiScrollBar;
  .mr40 {
    margin-right: vh2(35);
  }

  .mb10 {
    margin-bottom: vh2(10);
  }

  .mb5 {
    margin-bottom: vh2(5);
  }
  .item-box {
    height: vh2(50);
  }
  .c-title-box {
    min-width: vh2(80);
    font-family: 'PingFang SC';
    font-weight: 500;
    font-size: vh2(16);
    color: #000000;
  }

  .c-title-item-box {
    width: 0;
    flex: 1;
    font-family: 'PingFang SC';
    font-weight: 300;
    font-size: vh2(14);
    color: #000000;
    ::v-deep .el-rate {
      height: vh2(50);
      display: flex;
      align-items: center;
    }
  }

  .sub-title {
    font-family: 'PingFang SC';
    font-weight: 300;
    font-size: vh2(14);
    color: #000000;
  }
}
</style>

<style lang="scss">
.c-title-item-box {
  .el-icon-star-on {
    font-size: 22px !important;
  }
}
</style>
