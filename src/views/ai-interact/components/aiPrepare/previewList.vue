<template>
  <div class="w h flex">
    <div class="home-work-card" :class="homeworkList.length > 0 && homeWorkIndex === '学生作品' ? '' : 'home-work-card-no'">
      <div class="home-head">
        <div
          v-show="homeworkList.length > 0"
          class="home-btn"
          :class="{'home-btn-active': homeWorkIndex === '学生作品'}"
          @click="homeWorkClick('学生作品')"
        >学生作品</div>
        <div
          class="home-btn"
          :class="{'home-btn-active': homeWorkIndex === '内容要求'}"
          @click="homeWorkClick('内容要求')"
        >内容要求</div>
        <div
          v-show="['下节课准备', '前置课程'].indexOf(type) === -1"
          class="home-btn"
          :class="{'home-btn-active': homeWorkIndex === '评分标准'}"
          @click="homeWorkClick('评分标准')"
        >评分标准</div>
        <div
          v-show="['下节课准备', '前置课程'].indexOf(type) === -1"
          class="home-btn"
          :class="{'home-btn-active': homeWorkIndex === '排行'}"
          @click="homeWorkClick('排行')"
        >排行</div>
      </div>
      <div class="home-c-box">
        <div v-show="homeworkList.length > 0 && homeWorkIndex === '学生作品'" class="a-card a-items">
          <!-- 学生作业列表 -->
          <div
            v-for="workItem in homeworkList"
            :key="workItem.id"
            class="li pointer"
            :class="[nameIndex === workItem.id ? 'li-active' : '', workItem.reviewStatus === 'UNDER_REVIEW' ? 'under-reiew' : '']"
            @click="handleName(workItem.id)"
          >
            <div class="w tl article-singer-container z-10">
              {{ workItem.author.displayName }}
            </div>
          </div>
        </div>
        <div v-if="homeWorkIndex === '学生作品'" class="homework-stu-box">
          <div class="homework-stu" :class="{'homework-no-mark': ['下节课准备', '前置课程'].indexOf(type) !== -1}">
            <div class="content-head" v-html="homeworkInfoItem && homeworkInfoItem.introduce"></div>
            <template v-if="homeworkInfoItem && homeworkInfoItem.imgList && homeworkInfoItem.imgList.length > 0">
              <div class="tag">图片({{ homeworkInfoItem.imgList.length }})</div>
              <div v-viewer.rebuild="viewOption" class="w content-content viewer">
                <!-- 学生作品(图片) -->
                <template v-for="img in homeworkInfoItem.imgList">
                  <img :key="img.id" class="home-img pointer" :src="img.url" />
                </template>
              </div>
            </template>
            <template v-if="homeworkInfoItem && homeworkInfoItem.videoList && homeworkInfoItem.videoList.length > 0">
              <div class="tag">视频({{ homeworkInfoItem.videoList.length }})</div>
              <div class="w video-content">
                <div v-for="video in homeworkInfoItem.videoList" :key="video.id" class="v" @click="handleVideo(video)">
                  <div class="v-tag">视频</div>
                  <i class="el-icon-caret-right"></i>
                  <img class="home-img pointer" :src="`${video.url}?x-oss-process=video/snapshot,t_1000,m_fast`" />
                </div>
              </div>
            </template>
          </div>
          <div v-if="['下节课准备', '前置课程'].indexOf(type) === -1" class="homework-stu-mark">
            <Hmark
              :info="info"
              :work-id="nameIndex"
              :ai-work-score="aiWorkScore"
              :homework-info-item="homeworkInfoItem"
              @markSuccess="markSuccess"
            />
          </div>
        </div>
        <div v-else class="home-content">
          <template v-if="homeWorkIndex === '内容要求'">
            <div class="content-head" v-html="aiWorkImg && aiWorkImg.introduce"></div>
            <div class="w content-content viewer">
              <template v-if="aiWorkImg && aiWorkImg.length > 0">
                <div class="flex flex-col align-start w">
                  <template v-for="img in aiWorkImg">
                    <img v-if="img && img.mediaFile && img.mediaFile.type === 'IMAGE'" :key="img.id" class="max-w" :src="img.mediaFile.url" />
                  </template>
                </div>
              </template>
            </div>
          </template>
          <template v-else-if="homeWorkIndex === '评分标准'">
            <div class="w content-content viewer">
              <template v-if="aiWorkScore && aiWorkScore.length > 0">
                <div class="flex flex-col align-start w">
                  <template v-for="img in aiWorkScore">
                    <img v-if="img && img.mediaFile && img.mediaFile.type === 'IMAGE'" :key="img.id" class="max-w" :src="img.mediaFile.url" />
                  </template>
                </div>
              </template>
            </div>
          </template>
          <template v-else-if="homeWorkIndex === '排行'">
            <!-- 排行 -->
            <div v-if="rankList.length > 0" class="w">
              <!-- {{ rankList }} -->
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="rank-head">排名</div>
                </el-col>
                <el-col :span="8">
                  <div class="rank-head">姓名</div>
                </el-col>
                <el-col :span="8">
                  <div class="rank-head">分数</div>
                </el-col>
              </el-row>
              <el-row v-for="(item, index) in rankList" :key="`rank-${item.id}`" :gutter="20">
                <el-col :span="8">
                  <div class="rank-detail">
                    <template v-if="index + 1 === 1"><img :height="vh2(40)" :src="rank1" /></template>
                    <template v-else-if="index + 1 === 2"><img :height="vh2(40)" :src="rank2" /></template>
                    <template v-else-if="index + 1 === 3"><img :height="vh2(40)" :src="rank3" /></template>
                    <template v-else>
                      <div class="ranking-num f16">{{ index + 1 }}</div>
                    </template>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="rank-detail">{{ (item && item.author && item.author.displayName) || '-' }}</div>
                </el-col>
                <el-col :span="8">
                  <div class="rank-detail">{{ item.score }}</div>
                </el-col>
              </el-row>
            </div>
            <div v-show="rankList.length === 0" class="w h flex align-center justify-center f18">
              暂无数据
            </div>
          </template>
        </div>
      </div>
    </div>
    <div v-if="videoShow" class="video-pop-box" @click.self="videoShow = false">
      <div class="v-close" @click="videoShow = false">
        <i class="el-icon-close"></i>
      </div>
      <div class="video-box">
        <video-player
          ref="videoPlayer"
          class="video-player object-fill vjs-big-play-centered"
          :options="playerOptions"
          x5-video-player-type="h5"
          :webkit-playsinline="true"
          :webkit-inline="true"
          :playsinline="true"
          custom-event-name="customstatechangedeventname"
          @ready="playerReadied"
          @canplay="onPlayerCanplay($event)"
          @play="onPlayerPlay($event)"
          @pause="onPlayerPause($event)"
          @ended="onPlayerEnd($event)"
          @contextmenu="onPlayerContextmenu($event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
import { videoPlayer } from 'vue-video-player'
window.videojs = require('video.js')
import {
  getAiCourseWorkList,
  getAiCourseUnitSection,
  getAiCourseWorkInfo
} from '@/api/aicourse'
import rank1 from '@/assets/ai-image/tools-img/rank-1.png'
import rank2 from '@/assets/ai-image/tools-img/rank-2.png'
import rank3 from '@/assets/ai-image/tools-img/rank-3.png'
import Hmark from './mark.vue'
export default {
  components: {
    videoPlayer,
    Hmark
  },
  props: {
    info: {
      type: Object,
      default: () => {
        return null
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      playerOptions: {
        language: 'zh-CN',
        // controls: true,
        autoplay: false,
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'progressControl' }, // 播放进度条
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'timeDivider' },
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            { // 倍数播放
              name: 'playbackRateMenuButton',
              'playbackRates': [0.5, 1, 1.5, 2, 2.5]
            },
            { name: 'FullscreenToggle' } // 全屏
          ]
        },
        sources: [
          {
            type: 'video/mp4',
            src: ''
          }
        ],
        hls: true,
        fluid: false,
        aspectRatio: '16:9',
        notSupportedMessage: '此视频暂无法播放,请稍后再试'
      },
      videoShow: false,
      viewOption: {
        ready: () => {
          // 自定义预览样式
          this.$viewer = this.$el.querySelector('.viewer').$viewer
          console.dir(this.$viewer)
          const div3 = document.createElement('div')
          div3.className = 'view-length'
          div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          const div = document.createElement('div')
          div.className = 'view-pre'
          div.innerHTML = `<i class="el-icon-arrow-left"></i>`
          div.onclick = () => {
            this.$viewer.prev(true)
            div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          }
          const div2 = document.createElement('div')
          div2.className = 'view-next'
          div2.innerHTML = `<i class="el-icon-arrow-right"></i>`
          div2.onclick = () => {
            this.$viewer.next(true)
            div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          }

          this.$viewer.canvas.parentNode.appendChild(div)
          this.$viewer.canvas.parentNode.appendChild(div2)
          this.$viewer.canvas.parentNode.appendChild(div3)
        },
        // 'inline': true,
        // 'button': false,
        'navbar': false,
        'title': false,
        'toolbar': false,
        'tooltip': false,
        'movable': false,
        'zoomable': false,
        'rotatable': false,
        'scalable': false,
        'transition': false
        // 'fullscreen': false
        // 'keyboard': false
      },
      studentCourseId: this.$route.params.studentCourseId,
      homeworkList: [],
      homeWorkIndex: '',
      nameIndex: '',
      aiWorkImg: null,
      aiWorkScore: null,
      homeworkInfoItem: null,
      rankList: [],
      rank1,
      rank2,
      rank3
    }
  },
  watch: {
    info: {
      handler (val) {
        if (val) {
          this._getAiCourseUnitSection()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 作业提交列表
    async _getAiCourseWorkList () {
      const { data } = await getAiCourseWorkList({
        studentCourseId: this.studentCourseId,
        unitSectionId: this.info.id,
        isRank: 0
      })
      if (+data.code === 200) {
        this.homeworkList = []
        if (data.data) {
          this.homeworkList = data.data
        }
        if (this.homeworkList.length > 0) {
          this.homeWorkIndex = '学生作品'
          this.nameIndex = this.homeworkList[0].id
          this._getAiCourseWorkInfo(this.nameIndex)
        } else {
          if (this.aiWorkImg) {
            this.homeWorkIndex = '内容要求'
          } else if (this.aiWorkScore) {
            this.homeWorkIndex = '评分标准'
          }
        }
      }
    },
    markSuccess (workId) {
      for (let i = 0; i < this.homeworkList.length; i++) {
        if (+this.homeworkList[i].id === +workId) {
          this.homeworkList[i].reviewStatus = 'PASS'
          break
        }
      }
    },
    // 评分，内容要求
    async _getAiCourseUnitSection () {
      const { data } = await getAiCourseUnitSection({
        studentCourseId: this.studentCourseId,
        aicourseUnitSectionId: this.info.id
      })
      if (+data.code === 200) {
        this.aiWorkImg = data.data.resourceList.filter(val => {
          return val.linkType === 'AI_WORK_IMG'
        })
        this.aiWorkScore = data.data.resourceList.filter(val => {
          return val.linkType === 'AI_WORK_SCORE_STANDARD'
        })
        this.aiWorkImg.introduce = data.data.advice && data.data.advice.split('\n').join('<br><br>')
        this._getAiCourseWorkList()
      }
    },
    // 对应学生作品详情
    async _getAiCourseWorkInfo (workId) {
      const { data } = await getAiCourseWorkInfo({ workId })
      data.data.imgList = []
      data.data.videoList = []
      data.data.introduce = data.data.introduce && data.data.introduce.split('\n').join('<br><br>')
      const arrImg = []
      const arrVideo = []
      data.data.resourceList.map(val => {
        if (val.type === 'IMAGE') {
          arrImg.push(val)
        } else if (val.type === 'VIDEO') {
          arrVideo.push(val)
        }
      })
      data.data.imgList = arrImg
      data.data.videoList = arrVideo
      this.homeworkInfoItem = data.data
    },
    async homeWorkClick (type) {
      this.homeWorkIndex = type
      if (type === '排行') {
        const { data } = await getAiCourseWorkList({
          studentCourseId: this.studentCourseId,
          unitSectionId: this.info.id,
          isRank: 1
        })
        if (+data.code === 200) {
          this.rankList = []
          if (data.data) {
            this.rankList = data.data
          }
        }
      }
    },
    handleName (type) {
      this.nameIndex = type
      this._getAiCourseWorkInfo(type)
    },
    vh2 (px) {
      return px * document.body.clientHeight / 965
    },
    handleVideo (videoObj) {
      this.playerOptions.sources[0].src = videoObj.url
      this.videoShow = true
    },
    playerReadied () {
      this.$refs.videoPlayer.player.addClass('vjs-custom-waiting')
    },
    onPlayerCanplay (player) {
      console.log('onPlayerCanplay', player)
    },
    onPlayerPlay (player) {
      console.log('onPlayerPlay', player)
      this.$refs.videoPlayer.player.removeClass('vjs-custom-waiting')
    },
    onPlayerPause (player) {
      player.bigPlayButton.show()
      console.log('onPlayerPause', player)
    },
    onPlayerEnd (player) {
      console.log('onPlayerEnd', player)
    },
    onPlayerContextmenu (event) {
      console.log(event)
      event.preventDefault()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';

.a-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: vh2(10);
  height: vh2(450);
  border-radius: vh2(10);
  box-sizing: border-box;
  @include aiScrollBar;
}

.a-items {
  width: vh2(110);
  margin-right: vh2(10);
  overflow-y: auto;
  .li {
    width: 100%;
    height: vh2(30);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #000;
    font-size: vh2(14);
    margin-bottom: vh2(30);
    padding: 0 vh2(5);
    box-sizing: border-box;
    position: relative;
    z-index: 2;
    text-align: center;
    &:last-child {
      margin-bottom: 0;
    }
    &:hover {
      &::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: linear-gradient(90deg, #FFB347 0%, #FFCC33 100%);
        filter: blur(#{vh2(5)});
        border-radius: vh2(6);
      }
    }
  }

  .li-active {
    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background: linear-gradient(90deg, #FFB347 0%, #FFCC33 100%);
      filter: blur(#{vh2(5)});
      border-radius: vh2(6);
    }
  }
}

.homework-stu-box {
  width: calc(100% - #{vh2(110)} - #{vh2(10)});
  height: 100%;

  .homework-stu-mark {
    width: 100%;
    margin-top: vh2(10);
    height: vh2(80);
    padding: vh2(10);
    box-sizing: border-box;
    border-radius: vh2(10);
    background: rgba(255, 255, 255, 0.3);
    border: vh2(1) solid #FFFFFF;
  }

  .homework-no-mark {
    height: calc(100% - #{vh2(30)}) !important;
  }

  .homework-stu {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding: vh2(10);
    height: calc(100% - #{vh2(30)} - #{vh2(90)});
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.3);
    border: vh2(1) solid #FFFFFF;
    border-radius: vh2(10);
    box-sizing: border-box;
    @include aiScrollBar;

    .tag {
      padding: vh2(5) 0;
    }

    .content-head {
      font-weight: 500;
      font-size: vh2(14);
      color: #4F4F4F;
      margin-bottom: vh2(20);
    }

    .content-content {
      .home-img {
        width: vh2(100);
        height: vh2(70);
        margin: 0 vh2(10) vh2(20) 0;
        object-fit: cover;
      }

      .max-w {
        max-width: 100%;
        margin-bottom: vh2(5);
      }
    }

    .video-content {
      margin: vh2(20) 0;
      display: flex;
      .v {
        width: vh2(100);
        height: vh2(70);
        margin: 0 vh2(10) vh2(20) 0;
        position: relative;
      }
      .home-img {
        width: vh2(100);
        height: vh2(70);
        object-fit: cover;
      }
      .v-tag {
        position: absolute;
        right: vh2(2);
        top: vh2(2);
        font-size: vh2(12);
        content: '视频';
        color: #fff;
        z-index: 9;
        padding: vh(3);
        border-radius: vh2(3);
        background: rgba(0, 0, 0, 0.3);
      }
      .el-icon-caret-right {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: vh2(20);
      }
    }

    .rank-head {
      width: 100%;
      height: vh2(40);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #000000;
      font-weight: 600;
      font-size: vh2(16);
    }

    .rank-detail {
      width: 100%;
      height: vh2(40);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #4F4F4F;
      font-weight: 500;
      font-size: vh2(14);
    }
  }

  .home-text {
    color: #000;
    font-weight: 500;
    font-size: vh2(14);
  }
  .home-img {
    max-width: 100%;
  }
}

.home-c-box {
  width: 100%;
  height: 100%;
  display: flex;
}

.home-work-card-no {
  width: calc(100% - #{vh2(10)}) !important;
}

.home-work-card {
  width: 100%;
  height: vh2(450);

  .home-head {
    height: vh2(30);
    // display: flex;
    padding: vh2(0) vh2(10);
    box-sizing: border-box;

    .home-btn {
      font-weight: 500;
      font-size: vh2(12);
      color: #000;
      margin-right: vh2(30);
      cursor: pointer;
      position: relative;
      display: inline-block;
    }
    .home-btn-active {
      &::after {
        content: "";
        position: absolute;
        bottom: - vh2(3);
        left: 0;
        width: 100%;
        height: vh2(2);
        background: linear-gradient(90deg, #22C1C3 0%, #FDBB2D 100%);
        border-radius: vh2(9);
      }
    }
  }

  .home-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding: vh2(10);
    height: calc(100% - #{vh2(30)});
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.3);
    border: vh2(1) solid #FFFFFF;
    border-radius: vh2(10);
    box-sizing: border-box;
    @include aiScrollBar;

    .tag {
      padding: vh2(5) 0;
    }

    .content-head {
      font-weight: 500;
      font-size: vh2(14);
      color: #4F4F4F;
      margin-bottom: vh2(20);
    }

    .content-content {
      .home-img {
        width: vh2(100);
        height: vh2(70);
        margin: 0 vh2(10) vh2(20) 0;
        object-fit: cover;
      }

      .max-w {
        max-width: 100%;
        margin-bottom: vh2(5);
      }
    }

    .video-content {
      margin: vh2(20) 0;
      display: flex;
      .v {
        width: vh2(100);
        height: vh2(70);
        margin: 0 vh2(10) vh2(20) 0;
        position: relative;
      }
      .home-img {
        width: vh2(100);
        height: vh2(70);
        object-fit: cover;
      }
      .v-tag {
        position: absolute;
        right: vh2(2);
        top: vh2(2);
        font-size: vh2(12);
        content: '视频';
        color: #fff;
        z-index: 9;
        padding: vh(3);
        border-radius: vh2(3);
        background: rgba(0, 0, 0, 0.3);
      }
      .el-icon-caret-right {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: vh2(20);
      }
    }

    .rank-head {
      width: 100%;
      height: vh2(40);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #000000;
      font-weight: 600;
      font-size: vh2(16);
    }

    .rank-detail {
      width: 100%;
      height: vh2(40);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #4F4F4F;
      font-weight: 500;
      font-size: vh2(14);
    }
  }

  .home-text {
    color: #000;
    font-weight: 500;
    font-size: vh2(14);
  }
  .home-img {
    max-width: 100%;
  }
}
.under-reiew {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 45%;
    bottom: 0;
    left: - vh2(3);
    width: vh2(4);
    height: vh2(4);
    border-radius: 50%;
    background: #F2994A;
  }
}

.video-pop-box {
  position: fixed;
  background: rgba(0, 0, 0, .5);
  z-index: 99;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .v-close {
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
    cursor: pointer;
    height: 80px;
    overflow: hidden;
    position: absolute;
    right: -40px;
    top: -40px;
    transition: background-color 0.15s;
    width: 80px;

    .el-icon-close {
      bottom: 15px;
      left: 15px;
      position: absolute;
      color: #fff;
    }
  }
  .video-box {
    width: 80%;
    height: 80%;
  }
  ::v-deep video {
    max-width: 100% !important;
    max-height: 100% !important;
    // object-fit:fill;    //消除留白
  }
  ::v-deep .video-js.vjs-fluid {
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
    padding-top: 0 !important;
  }

  .video-player {
    height: 100%;
    width: 100%;
  }
}
</style>
<style lang="scss">
.view-pre {
  left: 10px;
}

.view-next {
  right: 10px;
}

.view-length {
  position: absolute;
  bottom: 8px;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  // background-color: rgba(0, 0, 0, 50%);
  overflow: hidden;
}

.view-next, .view-pre {
  position: absolute;
  top: 45%;
  color: #000;
  font-weight: 500 !important;
  font-size: 25px;
  width: 50px;
  height: 50px;
  background: #F2C94C;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
}
</style>
