<template>
  <div class="w h flex a-main">
    <div class="tip-view">
      <div v-if="currInfo.time > 0">
        建议授课时长：{{ currInfo.time | timeFormat }}
      </div>
      <div v-else>
        建议授课时长：--
      </div>
      <div class="preview-btn" @click="handlePreview">
        预览
        <i class="el-icon-caret-right" style='margin-left: 10px;font-size: 26px'></i>
      </div>
    </div>
    <div class="a-card a-items">
      <div
        v-if="info && info.previewList"
        class="li pointer"
        :class="[nameIndex === '前置课程' ? 'li-active' : '']"
        @click="handleName('前置课程', info.previewList)"
      >
        <div class="w tc article-singer-container">
          前置课程
        </div>
      </div>
      <div
        v-if="info && info.learnAchievedList"
        class="li pointer"
        :class="[nameIndex === '课中成果' ? 'li-active' : '']"
        @click="handleName('课中成果', info.learnAchievedList)"
      >
        <div class="w tc article-singer-container">
          课中成果
        </div>
      </div>
      <div
        v-if="info && info.afterAchievedList"
        class="li pointer"
        :class="[nameIndex === '课后成果' ? 'li-active' : '']"
        @click="handleName('课后成果', info.afterAchievedList)"
      >
        <div class="w tc article-singer-container">
          课后成果
        </div>
      </div>
      <div
        v-if="info && info.nextPreviewList"
        class="li pointer"
        :class="[nameIndex === '下节课准备' ? 'li-active' : '']"
        @click="handleName('下节课准备', info.nextPreviewList)"
      >
        <div class="w tc article-singer-container">
          下节课准备
        </div>
      </div>
    </div>
    <div class="home-work-card">
      <PreviewList :info="currInfo" :type="nameIndex" />
    </div>
    <div class='preview-view' v-if='showPreview'>
      <iframe
        id="scratch-iframe"
        ref="scratchFrame"
        :src="iframeUrl"
        style="border: none"
        width="100%"
        height="100%"
        allowfullscreen
        allow="microphone *; camera *"
        sandbox="allow-same-origin allow-scripts allow-popups allow-modals allow-downloads"
      ></iframe>
    </div>
  </div>
</template>

<script>
import PreviewList from '@/views/ai-interact/components/aiPrepare/previewList.vue'
import moment from 'moment'
export default {
  components: {
    PreviewList
  },
  props: {
    info: {
      type: Object,
      default: () => {
        return null
      }
    },
    unitList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  filters: {
    timeFormat (val) {
      if (!val) {
        return 0
      }
      const time = moment.duration(val, 'seconds') // 得到一个对象，里面有对应的时分秒等时间对象值
      const hours = time.hours()
      const minutes = time.minutes()
      const seconds = time.seconds()
      if (hours) {
        return moment({ h: hours, m: minutes, s: seconds }).format('HH时mm分ss秒')
      } else if (minutes) {
        return moment({ m: minutes, s: seconds }).format('mm分ss秒')
      } else {
        return moment({ s: seconds }).format('ss秒')
      }
    }
  },
  data () {
    return {
      viewOption: {
        ready: () => {
          // 自定义预览样式
          this.$viewer = this.$el.querySelector('.viewer').$viewer
          console.dir(this.$viewer)
          const div3 = document.createElement('div')
          div3.className = 'view-length'
          div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          const div = document.createElement('div')
          div.className = 'view-pre'
          div.innerHTML = `<i class="el-icon-arrow-left"></i>`
          div.onclick = () => {
            this.$viewer.prev(true)
            div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          }
          const div2 = document.createElement('div')
          div2.className = 'view-next'
          div2.innerHTML = `<i class="el-icon-arrow-right"></i>`
          div2.onclick = () => {
            this.$viewer.next(true)
            div3.innerHTML = `${this.$viewer.index + 1}/${this.$viewer.length}`
          }

          this.$viewer.canvas.parentNode.appendChild(div)
          this.$viewer.canvas.parentNode.appendChild(div2)
          this.$viewer.canvas.parentNode.appendChild(div3)
        },
        // 'inline': true,
        // 'button': false,
        'navbar': false,
        'title': false,
        'toolbar': false,
        'tooltip': false,
        'movable': false,
        'zoomable': false,
        'rotatable': false,
        'scalable': false,
        'transition': false
        // 'fullscreen': false
        // 'keyboard': false
      },
      studentCourseId: this.$route.params.studentCourseId,
      homeworkList: [],
      homeWorkIndex: '',
      nameIndex: '',
      aiWorkImg: null,
      aiWorkScore: null,
      homeworkInfoItem: null,
      currInfo: null,
      load: false,
      showPreview: false,
      iframeUrl: ''
    }
  },
  watch: {
    info: {
      handler (val) {
        if (val) {
          if (val.previewList) {
            this.currInfo = val.previewList
            this.nameIndex = '前置课程'
          } else if (val.learnAchievedList) {
            this.currInfo = val.learnAchievedList
            this.nameIndex = '课中成果'
          } else if (val.afterAchievedList) {
            this.currInfo = val.afterAchievedList
            this.nameIndex = '课后成果'
          } else if (val.nextPreviewList) {
            this.currInfo = val.nextPreviewList
            this.nameIndex = '下节课准备'
          }
          this.homeWorkIndex = '内容要求'
        }
      },
      immediate: true
    }
  },
  mounted () {
    window.addEventListener('message', (e) => {
      if (e.data.type === 'prepareBack') {
        this.handleClose()
      }
    })
  },
  computed: {
    previewIndex () {
      let num = 0
      this.unitList.map((item, index) => {
        if (item.id === this.currInfo.id) {
          num = index
        }
      })
      return num
    }
  },
  methods: {
    handleName (type, info) {
      this.nameIndex = type
      this.currInfo = info
      this.homeWorkIndex = '内容要求'
    },
    handlePreview () {
      this.iframeUrl = `${window.location.origin}/ai/${this.$route.params.courseId}/0/${this.$route.params.unitId}?preview=1&previewIndex=${this.previewIndex}&source=prepare`
      this.showPreview = true
    },
    handleClose () {
      this.iframeUrl = ''
      this.showPreview = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';
.a-main {
  position: relative;
  .preview-view{
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 3;
    background: white;
  }
  .tip-view{
    position: absolute;
    right: 20px;
    top: -60px;
    display: flex;
    font-size: vh2(14);
    align-items: center;
    .preview-btn{
      width: 120px;
      height: 40px;
      background: linear-gradient(90deg, #FFB347 0%, #FFCC33 100%);
      border-radius: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 20px;
      padding-left: 10px;
      box-sizing: border-box;
      &:hover{
        opacity: 0.7;
      }
    }
  }
}
.a-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: vh2(10);
  height: vh2(450);
  background: rgba(255, 255, 255, 0.3);
  border: vh2(1) solid #FFFFFF;
  border-radius: vh2(10);
  box-sizing: border-box;
  @include aiScrollBar;

  .tc {
    text-align: center;
  }
}

.a-items {
  width: vh2(110);
  margin-right: vh2(10);
  overflow-y: auto;
  .li {
    width: 100%;
    height: vh2(30);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #4F4F4F;
    font-weight: 500;
    font-size: vh2(14);
    margin-bottom: vh2(20);
    padding: vh2(10) vh2(5);
    box-sizing: border-box;
    &:last-child {
      margin-bottom: 0;
    }
    &:hover {
      background: linear-gradient(90deg, #22C1C3 0%, #FDBB2D 100%);
      border-radius: vh2(10);
      color:#fff !important;
    }
  }

  .li-active {
    background: linear-gradient(90deg, #22C1C3 0%, #FDBB2D 100%);
    border-radius: vh2(10);
    color:#fff !important;
  }
}

.home-work-card {
  width: calc(100% - #{vh2(110)} - #{vh2(20)});
  height: vh2(450);
}
</style>
