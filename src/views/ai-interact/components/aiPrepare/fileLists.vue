<template>
  <div class="w h a-card">
    <div v-for="item in info" :key="item.id" class="mb10">
      <template v-if="item.mediaFile && item.mediaFile.url">
        <div>
          <span class="download-text">{{ item.mediaFile.fileName }}.{{ item.mediaFile.expendType }}</span>
          <span class="download-btn" @click="download(item.mediaFile)">下载</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { downloadFile } from '@/utils/index'
import { debounce } from '@/utils/index'

export default {
  props: {
    info: {
      type: [Object, Array],
      default: () => {
        return null
      }
    }
  },
  data () {
    return {}
  },
  methods: {
    download: debounce(function (mediaFile) {
      downloadFile(mediaFile.url, `${mediaFile.fileName}.${mediaFile.expendType}`)
    }, 3000, true)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';
.a-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: vh2(10);
  background: rgba(255, 255, 255, 0.3);
  border: vh2(1) solid #FFFFFF;
  border-radius: vh2(10);
  box-sizing: border-box;
  @include aiScrollBar;
}

.download-text {
  color: #000000;
  font-weight: 500;
  font-size: vh2(14);
  margin-right: vh2(15);
}
.download-btn {
  color: #F2994A;
  font-weight: 500;
  font-size: vh2(14);
  cursor: pointer;
}
.mb10 {
  margin-bottom: vh2(10);
}
</style>
