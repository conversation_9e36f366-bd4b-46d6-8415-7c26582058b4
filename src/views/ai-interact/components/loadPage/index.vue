<template>
  <div class="ai-load-page">
    <div class="ai-load-head">
      <div class="pointer" @click="goTo">跳过下载，直接上课</div>
    </div>
    <div class="gif">
      <!-- <lottie
        v-if="isLoading"
        :width="'100%'"
        :height="'100%'"
        :options="loadingAniOptions"
        class="video-animation"
      /> -->
    </div>
    <div class="ai-load-footer">
      <div>课程正在下载中</div>
      <el-progress
        class="w"
        :stroke-width="10"
        :percentage="process"
      />
    </div>
  </div>
</template>

<script>
import loadingAni from '@/assets/animate/loading.json'
// import lottie from 'vue-lottie'

export default {
  // components: { lottie },
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    toPath: {
      type: String,
      default: ''
    },
    videoList: {
      type: Object,
      default: () => {
        return null
      }
    },
    process: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      loadingAniOptions: {
        animationData: loadingAni,
        loop: true,
        autoplay: true
      }
    }
  },
  methods: {
    goTo () {
      window.ipc.send('pauseDownload')
      if (this.toPath) {
        this.$router.push({
          path: this.toPath
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-load-page {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #000000;
  // background: linear-gradient(109.39deg, #FCECFF -23.25%, #ABF6FF 97.11%);
  z-index: 50;

  .ai-load-head {
    width: 100%;
    height: 30px;
    font-size: 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #fff;
    padding: 0 15px;
    box-sizing: border-box;
    z-index: 51;
  }

  .gif {
    width: 100%;
    height: calc(100% - 80px);
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .ai-load-footer {
    width: 100%;
    height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 51;
    padding: 0 15px;
    box-sizing: border-box;
    color: #fff;
    z-index: 51;
    ::v-deep .el-progress__text {
      color: #fff;
    }
  }
}
</style>
