<template>
  <div class="dialog-container-bg flex-col align-center justify-center">
    <div class="dialog-container">
      <div class="d-tips">{{ isEmpty? '请学习完前面课程，可解锁新课程' : '正式上课会留有答题记录，确定上课吗？' }}</div>
      <div class="row">
        <template v-if="isEmpty">
          <div class="btn-middle" @click="$emit('leftCallback')">
            <span>确定</span>
          </div>
        </template>
        <template v-else>
          <div class="btn-middle" @click="$emit('leftCallback')">
            <span>取消</span>
          </div>
          <div class="btn-middle" @click="$emit('rightCallback')">
            <span>确定</span>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import bg from '@/assets/images/ai/ai-bg.png'
export default {
  props: {
    isEmpty: {
      type: Boolean,
      require: true,
      default: true
    }
  },
  data () {
    return {
      bg
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';
.dialog-container-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(9, 8, 8, 0.7);
  z-index: 3001;
}
.dialog-container {
  width: vh2(421);
  height: vh2(280);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #BDBDBD;
  border-radius: vh2(10);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.d-tips {
  font-size: vh2(20);
  font-weight: 500;
  color: #333333;
  line-height: vh2(28);
  padding: vh2(20);
  line-height: vh2(40);
  box-sizing: border-box;
  margin-bottom: vh2(20);
}

.row {
  display: flex;
  justify-content: center;
  width: vh2(315);
}

.btn-middle {
  height: vh2(50);
  width: vh2(150);
  background: url('../../../../assets/ai-image/ai/bg-btn.png') center center no-repeat;
  background-size: contain;
  text-align: center;
  font-size: vh2(20);
  font-weight: 500;
  line-height: vh2(50);
  margin-right: vh2(20);
  &:last-child {
    margin-right: 0;
  }
}
</style>
