<template>
  <div class="readerinfo">
    <div class="info-box">
      <div class="info-head">
        <div class="t-left">图书信息</div>
        <div class="t-right" @click="close"><i class="el-icon-close"></i></div>
      </div>

      <div v-if="info" class="info-content">
        <img class="i-img" :src="info.cover || defaultCourse" />
        <div class="i-c">
          <div class="i-c-title w">
            <div>
              {{ info.name }}
            </div>
          </div>
          <div class="flex mb10">
            <div class="d-title">出版：</div>
            <div class="d-content">{{ info.publisher && info.publisher.name || '-' }}</div>
          </div>
          <div class="flex mb10">
            <div class="d-title">发行：</div>
            <div class="d-content">{{ info.issuer && info.issuer.name || '-' }}</div>
          </div>
          <div class="flex mb10">
            <div class="d-title">ISBN：</div>
            <div class="d-content">{{ info.isbn || '-' }}</div>
          </div>
          <div class="flex mb10">
            <div class="d-title">CIP核字号：</div>
            <div class="d-content">{{ info.cip || '-' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import defaultCourse from '@/assets/ai-image/default/default-course.jpg'
import { getAiCourseBook } from '@/api/aicourse'
export default {
  props: {
    readId: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      defaultCourse,
      info: null
    }
  },
  watch: {
    readId: {
      handler (val) {
        if (val) {
          this._getList()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    close () {
      this.$emit('close')
    },
    async _getList () {
      const { data } = await getAiCourseBook({ aiCourseId: this.readId })
      this.info = data.data
    }
  }
}
</script>

<style lang="scss" scoped>
.readerinfo {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, .3);
  display: flex;
  justify-content: center;
  align-items: center;

  .info-box {
    width: vh2(460);
    min-height: vh2(280);
    border-radius: vh2(10);
    background: #FFF;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    padding: vh2(18);
    box-sizing: border-box;
    .info-head {
      height: vh2(30);
      display: flex;
      justify-content: space-between;

      .t-left {
        color: #000;
        font-size: vh2(16);
        font-weight: 500;
      }

      .t-right {
        .el-icon-close {
          cursor: pointer;
          font-size: vh2(18);
          color: #575B66;
        }
      }
    }

    .info-content {
      display: flex;
      .i-img {
        width: vh2(150);
        height: vh2(200);
        object-fit: cover;
        flex-shrink: 0;
      }

      .i-c {
        width: calc(100% - #{vh2(150)});
        padding: 0 0 0 vh2(15);

        .i-c-title {
          color: #000;
          font-size: vh2(16);
          font-weight: 500;
          margin-bottom: vh2(15);
        }

        .mb10 {
          margin-bottom: vh2(10);
        }

        .d-title {
          color: #828282;
          font-size: vh2(14);
          font-weight: 500;
          line-height: vh2(20);
          flex-shrink: 0;
        }
        .d-content {
          color: #000;
          font-size: vh2(14);
          font-weight: 500;
          line-height: vh2(20);
        }
      }
    }
  }

}
</style>
