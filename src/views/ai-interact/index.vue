<template>
  <div class="home">
    <div class="bg-circle1"></div>
    <div class="bg-circle2"></div>
    <img class="bingo" :src="bingo" />

    <div class="content flex">
      <div class="left flex flex-col">

        <!-- 班级名称 -->
        <div class="header flex align-start">
          <img class="arrow-left" :src="arrowLeft" alt="返回按钮" @click="closeWindow" />
          <div class="class-box">
            <div v-if="userInfo" class="flex align-center">
              <div class="class-name-box" @click.stop="showCode(userInfo, 'store')">
                <div class="class-name">{{ `${userInfo.displayName || ''}` }}</div>
              </div>
              <div class="code-box-icon">
                <img src="../../assets/ai-image/icon/assistant-code.svg" @click.stop="showCode(userInfo, 'store')" />
              </div>
              <div class="chang-btn" @click.stop="showGrade = !showGrade">
                <img class="triangle-down" :src="triangleDown" />
                切换班级
              </div>
            </div>
            <div v-if="userInfo" class="welcome">欢迎来到缤果双师AI课堂！</div>
            <ul v-if="showGrade" class="grade_ul">
              <li
                v-for="grade in gradeList"
                :key="grade.id"
                :class="{'blue': +userInfo.id === +grade.toUser.id}"
                @click="_switchAccount(grade)"
              >
                <div class="name">
                  {{ grade.toUser.displayName || '' }}
                </div>

                <img src="../../assets/ai-image/icon/assistant-code.svg" @click.stop="showCode(grade)" />
              </li>
              <li @click="handleAddCrouse">
                <div class="name">
                  + 添加班级
                </div>
              </li>
            </ul>
          </div>
        </div>

        <!-- 人物 -->
        <div class="people-box">
          <!-- 滨果币 -->
          <div class="coin-box flex align-center">
            <img class="coin" :src="coin" alt="滨果币" />
            <div class="x">x </div>
            <div class="coin-number">{{ wallet && wallet.userLevel && wallet.userLevel.aiScore || 0 }}</div>
          </div>
          <!-- 人物选择框 -->
          <div class="select-group">
            <img :class="{'select-img': showPeopleState === 0}" :src="headerZHIYA" alt="智雅" @click="changePeopleState(0)" />
            <img :class="{'select-img': showPeopleState === 1}" :src="headerSHUZHIXING" alt="苏智兴" @click="changePeopleState(1)" />
            <img :class="{'select-img': showPeopleState === 2}" :src="headerXIAOCHUAN" alt="小川" @click="changePeopleState(2)" />
            <img :class="{'select-img': showPeopleState === 3}" :src="headerXIAOYA" alt="小雅" @click="changePeopleState(3)" />
          </div>
          <!-- 人物展示（必须用v-if重新渲染gif图的动作） -->
          <div class="people">
            <div v-if="showPeopleLoading" class="people-loading">
              <i class="el-icon-loading"></i>
              <span>加载中...</span>
            </div>
            <template v-else-if="showActionState === 0">
              <img v-if="showPeopleState === 0" class="zhiya" :src="ZhiYa" alt="智雅" />
              <img v-if="showPeopleState === 1" class="suzhixing" :src="ShuZhiXing" alt="苏智兴" />
              <img v-if="showPeopleState === 2" class="xiaochuan" :src="XiaoChuan" alt="小川" />
              <img v-if="showPeopleState === 3" class="xiaoya" :src="XiaoYa" alt="小雅" />
            </template>
            <!-- 动作展示 -->
            <!-- 智雅动作展示 -->
            <template v-else-if="showPeopleState === 0">
              <img v-if="showActionState === 1" class="zhiya" :src="zhiyaAction1" alt="" />
              <img v-if="showActionState === 2" class="zhiya" :src="zhiyaAction2" alt="" />
              <img v-if="showActionState === 3" class="zhiya" :src="zhiyaAction3" alt="" />
            </template>
            <!-- 苏智星动作展示 -->
            <template v-else-if="showPeopleState === 1">
              <img v-if="showActionState === 1" class="suzhixing" :src="doctorAction1" alt="" />
              <img v-if="showActionState === 2" class="suzhixing" :src="doctorAction2" alt="" />
              <img v-if="showActionState === 3" class="suzhixing" :src="doctorAction3" alt="" />
              <img v-if="showActionState === 4" class="suzhixing" :src="doctorAction4" alt="" />
              <img v-if="showActionState === 5" class="suzhixing" :src="doctorAction5" alt="" />
              <img v-if="showActionState === 6" class="suzhixing" :src="doctorAction6" alt="" />
              <img v-if="showActionState === 7" class="suzhixing" :src="doctorAction7" alt="" />
            </template>
            <!-- 小川动作展示 -->
            <template v-else-if="showPeopleState === 2">
              <img v-if="showActionState === 1" class="xiaochuan" :src="xiaochuanAction1" alt="" />
              <img v-if="showActionState === 2" class="xiaochuan" :src="xiaochuanAction2" alt="" />
              <img v-if="showActionState === 3" class="xiaochuan" :src="xiaochuanAction3" alt="" />
              <img v-if="showActionState === 4" class="xiaochuan" :src="xiaochuanAction4" alt="" />
              <img v-if="showActionState === 5" class="xiaochuan" :src="xiaochuanAction5" alt="" />
              <img v-if="showActionState === 6" class="xiaochuan" :src="xiaochuanAction6" alt="" />
              <img v-if="showActionState === 7" class="xiaochuan" :src="xiaochuanAction7" alt="" />
            </template>
            <!-- 小雅动作展示 -->
            <template v-else-if="showPeopleState === 3">
              <img v-if="showActionState === 1" class="xiaoya xiaoya-gif" :src="xiaoyaAction1" alt="" />
              <img v-if="showActionState === 2" class="xiaoya xiaoya-gif" :src="xiaoyaAction2" alt="" />
              <img v-if="showActionState === 3" class="xiaoya xiaoya-gif" :src="xiaoyaAction3" alt="" />
              <img v-if="showActionState === 4" class="xiaoya xiaoya-gif" :src="xiaoyaAction4" alt="" />
              <img v-if="showActionState === 5" class="xiaoya xiaoya-gif" :src="xiaoyaAction5" alt="" />
              <img v-if="showActionState === 6" class="xiaoya xiaoya-gif" :src="xiaoyaAction6" alt="" />
              <img v-if="showActionState === 7" class="xiaoya xiaoya-gif" :src="xiaoyaAction7" alt="" />
            </template>
            <div class="people-container">
              <div class="people-name" @click="openActionList">
                <div class="text">{{ peopleName[showPeopleState] }}</div>
                <img v-show="actionList[showPeopleState].length > 0" class="arrow" :src="arrowDown" alt="" />
              </div>
              <div v-if="showActionList" class="action-box">
                <div class="action-list">
                  <div
                    v-for="(actionItem, index) in actionList[showPeopleState]"
                    :key="actionItem + index"
                    class="action-item"
                    @click="actionPeople(index)"
                  >
                    <div class="action-image"><img :src="actionLabel" alt="" /></div>
                    <span>{{ actionItem }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <img class="platform" :src="platform" alt="站台" />
        </div>
      </div>

      <div class="right flex flex-col">
        <!-- 我的课程 -->
        <div class="flex align-center ml40">
          <img class="star" :src="star" alt="星星" />
          <div class="my-course">我的课程</div>
          <img class="star" :src="star" alt="星星" />
        </div>
        <div class="course-list">
          <template v-if="aiCourseList.length > 0 || (showOutdate && aiOutdateCourseList.length > 0)">
            <div v-for="item in aiCourseList" :key="item.courseId" class="course-item" @click="goPackage(item)">
              <div class="seq">{{ item.finishedProgress }}/{{ item.total }}</div>
              <div class="cover">
                <img :src="item.aicourse.coverUrl || dfCover" alt="" />
                <div v-if="+item.finishedProgress === +item.total" class="shadow">已完课</div>
              </div>
              <div v-if="item.id === 0" class="label-tiyan">体验课</div>
              <div class="course-name">{{ item.aicourse && item.aicourse.title || '' }}</div>
            </div>
            <template v-if="showOutdate">
              <div v-for="item in aiOutdateCourseList" :key="item.id" class="course-item" @click="goOutdatePackage()">
                <div class="seq">{{ item.finishedProgress }}/{{ item.total }}</div>
                <div class="cover">
                  <img :src="item.aicourse.coverUrl || dfCover" alt="" />
                  <div class="shadow">已过期</div>
                </div>
                <div class="course-name">{{ item.aicourse && item.aicourse.title || '' }}</div>
              </div>
            </template>
            <div v-else-if="!showOutdate && aiOutdateCourseList.length > 0" class="history-course" @click="showHistoryCourse">历史课程></div>
            <div class="course-add" @click="handleAddCrouse">
              <div>+ 添加课程</div>
            </div>
          </template>
          <template v-else>
            <div class="empty-box">
              <img class="empty" :src="empty" alt="" />
              <span>暂无课程</span>
              <p @click="handleAddCrouse">添加课程</p>
            </div>
          </template>
        </div>
      </div>
    </div>
    <el-drawer
      v-if="showAddCrouse"
      :with-header="false"
      :size="'95%'"
      :visible.sync="showAddCrouse"
      :direction="'btt'"
      :show-close="false"
      :destroy-on-close="true"
      :before-close="handleClose"
    >
      <div class="drawer-title">
        <i class="el-icon-error" @click="closeAddCrouse"></i>
      </div>
      <div v-loading="addLoading" class="iframe">
        <iframe class="w h" :src="iframeUrl" frameborder="0" @load="addLoading=false"></iframe>
      </div>
    </el-drawer>
    <!-- <addCrouse
      v-if="showAddCrouse"
      :dialog-visible="showAddCrouse"
      :grade-list="gradeList"
      @closeDialog="showAddCrouse = false"
      @submit="showAddCrouse = false; _getStudentCourseList()"
    /> -->
    <aiDialog
      v-if="aiDialogShow"
      :info="classNameinfo"
      @close="aiDialogShow = false"
    />
  </div>
</template>

<script>
import arrowLeft from '@/assets/ai-image/ai-interact/arrow-left.svg'
import arrowDown from '@/assets/ai-image/ai-interact/arrow-down.svg'
import triangleDown from '@/assets/ai-image/icon/change.svg'
import coin from '@/assets/ai-image/ai-interact/coin.svg'
import star from '@/assets/ai-image/ai-interact/star.svg'
import bingo from '@/assets/ai-image/ai-interact/bingo.png'
import headerZHIYA from '@/assets/ai-image/ai-interact/header-ZHIYA.png'
import headerXIAOCHUAN from '@/assets/ai-image/ai-interact/header-XIAOCHUAN.png'
import headerSHUZHIXING from '@/assets/ai-image/ai-interact/header-SHUZHIXING.png'
import headerXIAOYA from '@/assets/ai-image/ai-interact/header-XIAOYA.png'
import platform from '@/assets/ai-image/ai-interact/platform.png'
import bgAction from '@/assets/ai-image/ai-interact/bg-action.png'
import actionLabel from '@/assets/ai-image/ai-interact/action-label.png'
import dfCover from '@/assets/ai-image/default/default-cover.jpg'
import empty from '@/assets/ai-image/ai/empty-3.png'
import ZhiYa from '@/assets/ai-image/ai-interact/ZhiYa2.png'
import XiaoChuan from '@/assets/ai-image/ai-interact/XiaoChuan2.png'
import ShuZhiXing from '@/assets/ai-image/ai-interact/ShuZhiXing2.png'
import XiaoYa from '@/assets/ai-image/ai-interact/XiaoYa2.png'
import { setAssistantId, getAssistantId } from '@/utils/auth'
import { getWallet, getUserInfo } from '@/api/aicourse'
import { getStudentCourseList } from '@/api/lesson'
import { getUserRelationListWithSameMainUser, switchAccount } from '@/api/user'
import { getToken } from '@/utils/auth'
import aiDialog from './components/index/Dialog.vue'
// import addCrouse from './components/addCrouse/index.vue'
import baseUrl from '@/config'
export default {
  components: {
    aiDialog
    // addCrouse
  },
  data () {
    return {
      aiDialogShow: false,
      classNameinfo: null,
      arrowLeft,
      arrowDown,
      triangleDown,
      coin,
      star,
      bingo,
      headerZHIYA,
      headerXIAOCHUAN,
      headerSHUZHIXING,
      headerXIAOYA,
      ZhiYa: require('@/assets/ai-image/ai-interact/ZhiYa2.png'),
      XiaoChuan: require('@/assets/ai-image/ai-interact/XiaoChuan2.png'),
      ShuZhiXing: require('@/assets/ai-image/ai-interact/ShuZhiXing2.png'),
      XiaoYa: require('@/assets/ai-image/ai-interact/XiaoYa2.png'),
      peopleArray: [ZhiYa, ShuZhiXing, XiaoChuan, XiaoYa],
      platform,
      bgAction,
      actionLabel,
      empty,
      zhiyaAction1: require('@/assets/ai-image/ai-interact/zhiya-action-1.gif'),
      zhiyaAction2: require('@/assets/ai-image/ai-interact/zhiya-action-2.gif'),
      zhiyaAction3: require('@/assets/ai-image/ai-interact/zhiya-action-3.gif'),
      doctorAction1: require('@/assets/ai-image/ai-interact/doctor-action-1.gif'),
      doctorAction2: require('@/assets/ai-image/ai-interact/doctor-action-2.gif'),
      doctorAction3: require('@/assets/ai-image/ai-interact/doctor-action-3.gif'),
      doctorAction4: require('@/assets/ai-image/ai-interact/doctor-action-4.gif'),
      doctorAction5: require('@/assets/ai-image/ai-interact/doctor-action-5.gif'),
      doctorAction6: require('@/assets/ai-image/ai-interact/doctor-action-6.gif'),
      doctorAction7: require('@/assets/ai-image/ai-interact/doctor-action-7.gif'),
      xiaochuanAction1: require('@/assets/ai-image/ai-interact/xiaochuan-action-1.gif'),
      xiaochuanAction2: require('@/assets/ai-image/ai-interact/xiaochuan-action-2.gif'),
      xiaochuanAction3: require('@/assets/ai-image/ai-interact/xiaochuan-action-3.gif'),
      xiaochuanAction4: require('@/assets/ai-image/ai-interact/xiaochuan-action-4.gif'),
      xiaochuanAction5: require('@/assets/ai-image/ai-interact/xiaochuan-action-5.gif'),
      xiaochuanAction6: require('@/assets/ai-image/ai-interact/xiaochuan-action-6.gif'),
      xiaochuanAction7: require('@/assets/ai-image/ai-interact/xiaochuan-action-7.gif'),
      xiaoyaAction1: require('@/assets/ai-image/ai-interact/xiaoya-action-1.gif'),
      xiaoyaAction2: require('@/assets/ai-image/ai-interact/xiaoya-action-2.gif'),
      xiaoyaAction3: require('@/assets/ai-image/ai-interact/xiaoya-action-3.gif'),
      xiaoyaAction4: require('@/assets/ai-image/ai-interact/xiaoya-action-4.gif'),
      xiaoyaAction5: require('@/assets/ai-image/ai-interact/xiaoya-action-5.gif'),
      xiaoyaAction6: require('@/assets/ai-image/ai-interact/xiaoya-action-6.gif'),
      xiaoyaAction7: require('@/assets/ai-image/ai-interact/xiaoya-action-7.gif'),
      dfCover,
      shareToken: this.$route.query.token,
      assistantId: this.$route.query.assistantId,
      comeUrl: this.$route.query.url,
      wallet: undefined,
      aiCourseList: [],
      aiOutdateCourseList: [],
      showOutdate: false,
      gradeList: [],
      userInfo: undefined,
      showPeopleState: 0,
      showActionState: 0,
      showGrade: false,
      showActionList: false,
      peopleName: ['智雅', '苏智星', '小川', '小雅'],
      actionList: [
        ['打招呼', '感谢', '您好'],
        ['抱拳', '悲伤', '打招呼', '开心', '疑惑', 'OK', '点赞'],
        ['抱拳', '悲伤', '打招呼', '开心', '疑惑', 'OK', '点赞'],
        ['抱拳', '悲伤', '打招呼', '开心', '疑惑', 'OK', '点赞']
      ],
      timer: undefined,
      autoActionTimer: undefined,
      autoActionState: 0,
      showPeopleLoading: true,
      actionListZhiYa: [],
      actionListShuZhiXing: [],
      actionListXiaoChuan: [],
      actionListXiaoYa: [],
      showAddCrouse: false,
      iframeUrl: '',
      addLoading: false
    }
  },
  mounted () {
    this.initData()
  },
  destroyed () {
    if (this.autoActionTimer) clearInterval(this.autoActionTimer)
  },
  methods: {
    closeAddCrouse () {
      this.showAddCrouse = false
      this._getUserRelationListWithSameMainUser()
    },
    async handleAddCrouse () {
      this.showGrade = false
      this.addLoading = true
      this.iframeUrl = baseUrl.default.BASE_CLASSPRO_URL
      const assistantId = await getAssistantId()
      const userId = this.userInfo.id
      if (userId + '' !== assistantId + '') {
        // 当前用户和助教ID不一致的时候
        // 为了获取助教token 传入到ifram里使用 本地存的token 都是当前用户 需要从后台获取助教token
        const { data } = await switchAccount({
          touserId: assistantId,
          parentUserId: assistantId
        })
        this.iframeUrl += `?token=${data.data.access_token}`
      } else {
        const token = getToken()
        this.iframeUrl += `?token=${token}`
      }
      this.showAddCrouse = true
    },
    async initData () {
      if (this.$route && this.$route.query) {
        if (this.$route.query.token) {
          await this.$store.dispatch('saveToken', this.shareToken)
        }
        if (this.$route.query.assistantId) {
          await setAssistantId(this.assistantId)
        }
        if (this.$route.query.comeUrl) {
          localStorage.setItem('comeUrl', this.comeUrl)
        }
      }
      await this._getUserInfo()
      this.actionListZhiYa = [
        this.zhiyaAction1,
        this.zhiyaAction2,
        this.zhiyaAction3
      ]
      this.actionListShuZhiXing = [
        this.doctorAction1,
        this.doctorAction2,
        this.doctorAction3,
        this.doctorAction4,
        this.doctorAction5,
        this.doctorAction6,
        this.doctorAction7
      ]
      this.actionListXiaoChuan = [
        this.xiaochuanAction1,
        this.xiaochuanAction2,
        this.xiaochuanAction3,
        this.xiaochuanAction4,
        this.xiaochuanAction5,
        this.xiaochuanAction6,
        this.xiaochuanAction7
      ]
      this.actionListXiaoYa = [
        this.xiaoyaAction1,
        this.xiaoyaAction2,
        this.xiaoyaAction3,
        this.xiaoyaAction4,
        this.xiaoyaAction5,
        this.xiaoyaAction6,
        this.xiaoyaAction7
      ]
      this._getWallet()
      this._getStudentCourseList()
      this._getAiOutdateCourseList()
      this.autoChangeGrade()
      this.changePeopleState(0)
    },
    // 获取用户信息
    async _getUserInfo () {
      const params = {
        'userType': 'STUDENT'
      }
      var response = await getUserInfo(params)
      if (+response.data.code === 200) {
        this.userInfo = response.data.data
      }
    },
    // 获取钱包
    async _getWallet () {
      var response = await getWallet()
      if (+response.data.code === 200) {
        this.wallet = response.data.data
      }
    },
    // 获取ai课程列表
    async _getStudentCourseList () {
      const assistantId = await getAssistantId()
      const params = {
        'studentCourseListType': 'ASSISTANT',
        'assistantUserId': assistantId
      }
      var response = await getStudentCourseList(params)
      if (+response.data.code === 200) {
        this.aiCourseList = response.data.data.filter(item => item.courseType === 'AI_COURSE')
        if (this.aiCourseList.length === 0) this.showOutdate = true
      }
    },
    // 获取ai课程失效列表
    async _getAiOutdateCourseList () {
      const assistantId = await getAssistantId()
      const params = {
        'studentCourseListType': 'HISTORY',
        'assistantUserId': assistantId
      }
      var response = await getStudentCourseList(params)
      if (+response.data.code === 200) {
        this.aiOutdateCourseList = response.data.data.filter(item => item.courseType === 'AI_COURSE')
      }
    },
    //  获取班级
    async _getUserRelationListWithSameMainUser () {
      const assistantId = await getAssistantId()
      if (!assistantId) return
      const params = {
        'parentUserId': assistantId
      }
      await getUserRelationListWithSameMainUser(params).then(
        response => {
          if (response.data.data) this.gradeList = response.data.data
        }
      )
    },
    //  自动切换班级
    async autoChangeGrade () {
      await this._getUserRelationListWithSameMainUser()
      const assistantId = await getAssistantId()
      if (this.userInfo && +this.userInfo.id === +assistantId && this.gradeList.length > 0) {
        this._switchAccount(this.gradeList[0])
      }
    },
    async _switchAccount (item) {
      const assistantId = await getAssistantId()
      const params = {
        'touserId': item.toUserId,
        'parentUserId': assistantId
      }
      const response = await switchAccount(params)

      this.showGrade = false
      this.$store.dispatch('saveToken', 'Bearer ' + response.data.data.access_token)
      const res = await this.$store.dispatch('GetUserInfo')
      this.userInfo = res.data.data
      this._getWallet()
      this._getStudentCourseList()
      this.showOutdate = false
      this._getAiOutdateCourseList()
    },
    openActionList () {
      if (this.showActionList) {
        this.showActionList = false
      } else {
        if (this.actionList[this.showPeopleState].length === 0) return
        this.showActionList = true
      }
    },
    autoActionPeople () {
      if (this.autoActionTimer) clearInterval(this.autoActionTimer)
      if (this.actionList[this.showPeopleState].length === 0) return
      this.autoActionTimer = setInterval(() => {
        if (this.autoActionState > this.actionList[this.showPeopleState].length - 1) {
          this.autoActionState = 0
        }
        this.actionPeople(this.autoActionState)
        this.autoActionState += 1
      }, 10000)
    },
    actionPeople (index) {
      this.showActionState = index + 1
      this.showPeopleLoading = true
      if (this.autoActionTimer) clearInterval(this.autoActionTimer)
      if (this.timer) clearTimeout(this.timer)
      let imgs = []
      if (this.showPeopleState === 0) {
        imgs = this.actionListZhiYa
      }
      if (this.showPeopleState === 1) {
        imgs = this.actionListShuZhiXing
      }
      if (this.showPeopleState === 2) {
        imgs = this.actionListXiaoChuan
      }
      if (this.showPeopleState === 3) {
        imgs = this.actionListXiaoYa
      }
      const peopleImage = new Image()
      peopleImage.src = imgs[index]
      if (peopleImage.complete) { // 如果图片已经存在于浏览器缓存，直接调用回调函数
        this.showPeopleLoading = false
        this.stopAction()
        return
      }
      peopleImage.onload = () => {
        console.log(`${peopleImage}加载完成`)
        this.showPeopleLoading = false
        this.stopAction()
      }
      peopleImage.onerror = () => {
        console.log(`${peopleImage}加载失败`)
        if (this.autoActionTimer) clearInterval(this.autoActionTimer)
        if (this.timer) clearTimeout(this.timer)
      }
    },
    stopAction () {
      this.$nextTick(() => {
        this.timer = setTimeout(() => {
          this.showActionState = 0
          this.autoActionPeople()
        }, 2000)
      })
    },
    remakePeople () {
      this.showActionState = 0
    },
    changePeopleState (index) {
      this.showPeopleLoading = true
      this.showActionList = false
      this.showActionState = 0
      this.showPeopleState = index
      const peopleImgs = [
        this.ZhiYa,
        this.ShuZhiXing,
        this.XiaoChuan,
        this.XiaoYa
      ]
      const peopleImage = new Image()
      peopleImage.onload = () => {
        console.log(`${peopleImage}加载完成`)
        this.showPeopleLoading = false
      }
      peopleImage.src = peopleImgs[index]
      this.autoActionPeople()
    },
    // 切换路由
    async goPackage (item) {
      const assistantId = await getAssistantId()
      this.$router.push(`/aiPackage/${item.courseId}/${item.id}/${assistantId}${this.$route.query && this.$route.query.token ? `?token=${this.$route.query.token}` : ''}`)
    },
    closeWindow () {
      window.location.href = 'about:blank'
      window.close()
    },
    handleClose () {
      this._getUserRelationListWithSameMainUser()
      this._getStudentCourseList()
      this._getAiOutdateCourseList()
    },
    showHistoryCourse () {
      this.showOutdate = true
    },
    goOutdatePackage () {
      this.$message.error('课程已过期，请联系学校管理人员')
    },
    showCode (info, type) {
      if (type === 'store') {
        this.classNameinfo = {
          toUser: info
        }
      } else {
        this.classNameinfo = info
      }
      this.aiDialogShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.home {
    width: 100vw;
    height: 100vh;
    min-width: 965px;
    min-height: 650px;
    background: rgba(217, 246, 255, 1);
    position: relative;
    @include aiScrollBar;

    .bg-circle1 {
        width: vh(210);
        height: vh(210);
        min-width: 210px;
        min-height: 210px;
        background: rgba(172, 123, 240, 0.33);
        filter: blur(62.5px);
        position: absolute;
        left: vh(32);
        bottom: vh(63);
        z-index: 9;
    }

    .bg-circle2 {
        width: vh(400);
        height: vh(330);
        min-width: 400px;
        min-height: 330px;
        background: rgba(172, 123, 240, 0.33);
        filter: blur(62.5px);
        position: absolute;
        right: vh(160);
        top: vh(82);
        z-index: 9;
    }

    .bingo {
        position: absolute;
        object-fit: contain;
        left: vw2(65);
        bottom: vh2(68);
        width: vh2(482);
        height: vh2(210);
        z-index: 9;

        @media screen and (min-width: 1080px) {
          left: vw2(120);
        }
    }

    .content {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 10;
    }
}

.left {
    flex: 1;

    .header {
        padding:vh2(36) 0 0 vw2(36);
    }

    .arrow-left {
        width: vh2(58);
        height: vh2(58);
        margin-right: vh2(38);
        cursor: pointer;
    }

    .class-box {
        margin-top: vh2(5);
        height: vh2(85);
        position: relative;
    }

    .class-name {
        font-family: 'PingFang SC';
        font-size: vh2(30);
        width: 100%;
        line-height: vh2(42);
        cursor: pointer;
        @include ellipsisMore(2);
    }

    .class-name-box {
      flex: 1;
      // width: calc(100% - #{vh2(120)} - #{vh2(10)} - #{vh2(30)});
    }

    .code-box-icon {
      width: vh2(30);
      display: flex;
      justify-content: center;
      img {
        width: vh2(20);
      }
    }

    .chang-btn {
      background: #F2C94C;
      width: vh2(100);
      height: vh2(30);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-left: vh2(10);
      margin-top: vh2(8);
      cursor: pointer;
      align-self: flex-start;
      border-radius: vh2(10);
      img {
        margin-right: vh2(3);
        width: vh2(15);
        height: vh2(15);
      }
    }

    .triangle-down {
        width: vh2(17);
        height: vh2(17);
        cursor: pointer;
    }

    .welcome {
        font-family: 'PingFang SC';
        font-weight: 300;
        font-size: vh2(18);
        line-height: vh2(25);
        color: #333333;
        margin-top: 10px;
    }

    .grade_ul {
        position: absolute;
        left: 0;
        top: vh2(50);
        max-width: vh2(250);
        max-height: vh2(340);
        background: #FFFFFF;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        border-radius: 16px;
        padding: vh2(30);
        z-index: 12;
        display: flex;
        flex-direction: column;
        gap: vh2(23);
        overflow-x: hidden;
        @include aiScrollBar;

        li {
            text-align: center;
            font-family: 'PingFang SC';
            font-weight: 500;
            font-size: vh2(20);
            display: flex;
            align-items: center;
            cursor: pointer;
            img {
              margin-left: vh2(10);
              width: vh2(25);
            }

            .name {
              width: calc(100% - #{vh2(27)});
              @include ellipsisMore(2);
            }
        }

        .blue {
            color: #2D9CDB;
        }
    }

    .people-box {
        position: relative;
        flex: 1;

        @media screen and (min-width: 1080px) {
          margin-left: vh2(80);
        }

        .coin-box {
            padding: vh2(30) 0 0 vw2(45);
        }

        .coin {
            width: vh2(30);
            height: vh2(30);
        }

        .x {
            font-family: 'PingFang SC';
            font-weight: 400;
            font-size: vh2(18);
            color: #000000;
            margin: 0 vh2(5);
        }

        .coin-number {
            font-family: 'PingFang SC';
            font-weight: 600;
            font-size: vh2(28);
            color: #000000;
        }

        .select-group {
            position: absolute;
            bottom: vh2(40);
            left: vw2(45);
            display: flex;
            flex-direction: column;
            gap: vh2(8);
            align-items: center;

            img {
                width: vh2(50);
                height: vh2(50);
                min-width: 50px;
                min-height: 50px;
                object-fit: cover;
                cursor: pointer;
                border-radius: 50%;
                filter: drop-shadow(0px 2.75145px 2.75145px rgba(0, 0, 0, 0.25));
            }

            .select-img {
              border: 2px solid #FFFFFF;
            }
        }

        .people {
            position: absolute;
            width: vh2(275);
            height: vh2(470);
            min-width: 275px;
            min-height: 475px;
            left: vw2(120);
            bottom: vh2(44);

            .people-loading {
              position: absolute;
              bottom: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              z-index: 11;
              background: transparent;
              font-size: vh2(15);
              color: #000000;

              i {
                margin-right: vh2(6);
              }
            }

            .zhiya {
              // width: vh2(250);
              height: vh2(490);
              min-width: 250px;
              min-height: 490px;
              object-fit: contain;
              position: absolute;
              bottom: 0;
              left: vw2(5);
              z-index: 11;
            }

            .xiaochuan {
              width: vh2(282);
              height: vh2(480);
              min-width: 282px;
              min-height: 480px;
              object-fit: contain;
              position: absolute;
              bottom: 0;
              left: vw2(10);
              z-index: 11;
            }

            .xiaoya {
              // width: vh2(270);
              height: vh2(450);
              min-height: 450px;
              object-fit: contain;
              position: absolute;
              bottom: 0;
              left: vw2(20);
              z-index: 11;
            }

            .xiaoya-gif {
              transform: rotateY(180deg);
              left: vw2(8);
            }

            .suzhixing {
              width: vh2(275);
              height: vh2(435);
              min-width: 275px;
              min-height: 435px;
              object-fit: contain;
              position: absolute;
              bottom: vh2(10);
              left: vw2(5);
              z-index: 11;
            }

            .people-container {
              position: absolute;
              left: vh2(272);
              top: vh2(40);
              width: vh2(104);
              height: vh2(46);
              background: linear-gradient(90deg, #FBED96 0%, #ABECD6 100%);
              border-radius: 23px 0px;
              filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
              cursor: pointer;
              z-index: 12;
            }

            .people-name {
              width: 100%;
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;

              .text {
                font-size: vh2(20);
                font-weight: 600;
                line-height: vh2(28);
              }

              .arrow {
                width: vh2(9);
                height: vh2(5);
                object-fit: contain;
                position: relative;
              }
            }

            .action-box {
                position: absolute;
                left: vh2(-2);
                top: vh2(52);
                width: vh2(110);
                height: vh2(290);
                background: url('~assets/ai-image/ai-interact/bg-action.png') center center no-repeat;
                background-size: contain;
                z-index: 12;
            }

            .action-list {
                cursor: default;
                display: flex;
                flex-wrap: wrap;
                overflow: scroll;
                overflow-x: hidden;
                align-content: flex-start;
                column-gap: vh2(10);
                // row-gap: vh2(12);
                padding: vh2(21) 0 0 vh2(10);
            }

            .action-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: vh2(39);
                height: vh2(70);
                cursor: pointer;

                .action-image {
                    width: vh2(39);
                    height: vh2(39);
                    background: #FFFFFF;
                    border-radius: 4px;
                    position: relative;
                    margin-bottom: vh2(2);

                    img {
                        width: vh2(26);
                        height: vh2(26);
                        object-fit: contain;
                        position: absolute;
                        transform: translate(-50%, -50%);
                        left: 50%;
                        top: 50%;
                    }
                }

                span {
                    font-family: 'PingFang SC';
                    font-style: normal;
                    font-weight: 300;
                    font-size: vh2(12);
                    line-height: vh2(16);
                    color: #333333;
                }
            }
        }

        .platform {
            position: absolute;
            bottom: vh2(22);
            left: vw2(133);
            width: vh2(248);
            height: vh2(55);
            min-width: 248px;
            min-height: 55px;
            z-index: 10;
        }

        .action-container {
            position: absolute;
            left: vw2(514);
            top: vh2(110);
            z-index: 11;

            .action-btn {
                width: vh2(66);
                height: vh2(30);
                text-align: center;
                line-height: vh2(30);
                font-size: vh2(12);
                font-family: 'Inter';
                cursor: pointer;
                background: linear-gradient(90deg, #D9AFD9 0%, #97D9E1 100%);
                border: 1px solid #FFFFFF;
                box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.12);
                border-radius: 49px;
                color: #333333;
            }
        }
    }
}

.right {
    width: vh2(320);
    padding: vh2(50) vw2(20) 0;

    .ml40 {
        margin-left: vh2(45);
    }

    .my-course {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: vh2(26);
        color: #000000;
        margin: 0 15px;
    }

    .course-list {
        flex: 1;
        margin-top: vh2(20);
        overflow: scroll;
        overflow-x: hidden;
        &::-webkit-scrollbar-track-piece {
            background-color: transparent;
        }
        &::-webkit-scrollbar {
            width: 8px;
        }
        &::-webkit-scrollbar-thumb {
            background: #FFFFFF;
            opacity: 0.5;
            border-radius: 13px;
        }

        .course-item {
            width: vh2(270);
            height: vh2(196);
            border-radius: 14px;
            background: white;
            filter: drop-shadow(0px 2.76364px 2.76364px rgba(0, 0, 0, 0.05));
            margin-bottom: 10px;
            position: relative;
            cursor: pointer;
            overflow: hidden;

            @media screen and (min-width: 1080px) {
              margin-bottom: vh2(10);
            }
        }

        .course-add {
          width: vh2(270);
          padding: vh2(10) 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: white;
          border-radius: 14px;
          font-size: vh2(16);
          cursor: pointer;
        }

        .seq {
            width: vh2(54);
            height: vh2(38);
            background: #F2C94C;
            border-radius: 14px 0px;
            font-family: 'PingFang SC';
            font-weight: 600;
            font-size: vh2(20);
            line-height: vh2(38);
            text-align: center;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 11;
        }
        .label-tiyan {
          position: absolute;
          right: -53px;
          top: -55px;
          background: #D8C08E;
          color: #fff;
          font-weight: 500;
          width: 100px;
          height: 100px;
          transform: rotate(45deg);
          display: flex;
          align-items: flex-end;
          justify-content: center;
          padding-bottom: 6px;
          font-size: 14px;
        }

        .cover {
            width: 100%;
            height: vh2(150);
            border-radius: 14px;
            overflow: hidden;
            position: relative;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .shadow {
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.31);
                position: absolute;
                left: 0;
                top: 0;
                color: #FFFFFF;
                font-weight: 500;
                font-size: vh2(14);
                text-align: right;
                padding: vh2(10);
                box-sizing: border-box;
            }
        }

        .course-name {
            padding: vh2(12) vh2(10);
            word-break: break-all;
            white-space: pre-line;
            font-size: vh2(16);
            @include ellipsisMore(1)
        }

        .history-course {
          width: vh2(270);
          text-align: center;
          margin: 20px 0;
          cursor: pointer;
          font-weight: 300;
          font-size: vh2(14);
          line-height: 20px;
        }

        .empty-box {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-right: vh2(45);
        }

        .empty {
          width: vh2(200);
          height: vh2(192);
          object-fit: contain;
          padding-top: vh2(70);
        }

        span {
          font-family: 'PingFang SC';
          font-style: normal;
          font-weight: 500;
          font-size: vh2(24);
          color: #000000;
          margin: vh2(30) 0 vh2(10);
        }

        p {
          font-family: 'PingFang SC';
          font-style: normal;
          font-weight: 500;
          font-size: vh2(16);
          text-decoration-line: underline;
          color: #F2994A;
          cursor: pointer;
        }
    }
}

.drawer-title {
  height: vh2(40);
  box-sizing: border-box;
  text-align: right;
  padding: vh2(5) vh(15);

  i {
    color: #BDBDBD;
    font-size: vh2(20);
    cursor: pointer;
  }
}
.iframe {
  width: 100%;
  height: calc(100% - #{vh2(50)});
  padding: 0 vh2(10);
}
</style>
