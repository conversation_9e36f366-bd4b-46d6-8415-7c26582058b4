<template>
  <div class="home">
    <div class="content">
      <div class="a-head">
        <img class="back pointer" :src="arrowLeft" alt="返回按钮" @click="pre" />
        <div class="relative w">
          <div class="ai-name-box flex align-center" @click="!close && courseList && courseList.length > 1 ? showCourseList = !showCourseList : ''">
            <div :title="(courseList[unitIndex] && courseList[unitIndex].title) || ''" class="ai-name pointer article-singer-container">
              第{{
                courseList[unitIndex] && courseList[unitIndex].unitNo
              }}课时
              {{
                (courseList[unitIndex] && courseList[unitIndex].title) || ''
              }}
            </div>
            <img v-if="!close && courseList && courseList.length > 1" class="more pointer" :src="triangleDown" alt="筛选" />
          </div>
          <ul v-if="showCourseList" class="grade_ul">
            <li
              v-for="(item, index) in courseList"
              :key="item.id"
              :class="{'blue': +studentCourseId === +item.id}"
              :title="`第${item.unitNo}课时 ${item.title}`"
              @click="handleCourse(item, index)"
            >
              <div class="article-singer-container">第{{ item.unitNo }}课时 {{ item.title }}</div>
            </li>
          </ul>
        </div>
      </div>

      <div
        v-loading="refresh"
        element-loading-background="rgba(171, 246, 255, 0.4)"
        class="a-content flex"
      >
        <!-- refresh：切换课程时重绘页面 -->
        <template v-if="!refresh">
          <div class="a-card a-items">
            <div
              class="li pointer"
              :class="selectType === '课程简介' ? 'li-active' : ''"
              @click="handleSelect('课程简介')"
            >
              <div class="w tc article-singer-container">课程简介</div>
            </div>

            <!-- <div
              v-if="previewList"
              class="li pointer"
              :class="selectType === '前置课程' ? 'li-active' : ''"
              @click="handleSelect('前置课程')"
            >
              <div class="w tc article-singer-container">前置课程</div>
            </div> -->

            <div
              v-if="guideResource"
              class="li pointer"
              :class="selectType === '指导手册' ? 'li-active' : ''"
              @click="handleSelect('指导手册')"
            >
              <div class="w tc article-singer-container">指导手册</div>
            </div>

            <div
              v-if="expendResource"
              class="li pointer"
              :class="selectType === '材料准备' ? 'li-active' : ''"
              @click="handleSelect('材料准备')"
            >
              <div class="w tc article-singer-container">材料准备</div>
            </div>

            <div
              v-if="fileListResource && fileListResource.length > 0"
              class="li pointer"
              :class="selectType === '课程附件' ? 'li-active' : ''"
              @click="handleSelect('课程附件')"
            >
              <div class="w tc article-singer-container">课程附件</div>
            </div>
            <!-- 环节（动态获取） -->
            <template v-for="(item, key) in stepList">
              <div
                v-if="item.count && item.count > 0"
                :key="key"
                class="li pointer"
                :class="selectType === 'commn,' + key ? 'li-active' : ''"
                :title="item.name"
                @click="handleSelect('commn,' + key)"
              >
                <div class="w tc article-singer-container">{{ item.name }}</div>
              </div>
            </template>

            <div
              v-if="hasHomeworkList"
              class="li pointer"
              :class="selectType === '成果' ? 'li-active' : ''"
              @click="handleSelect('成果')"
            >
              <div class="w tc article-singer-container">成果</div>
            </div>
          </div>
          <!-- 右侧内容组件 -->
          <div class="a-detail">
            <Detail v-if="selectType === '课程简介'" :p-unit-info="currAiCourseUnitInfo" />
            <Resource v-else-if="selectType === '材料准备'" :info="expendResource" />
            <Guide v-else-if="selectType === '指导手册'" :info="guideResource.mediaFile" />
            <FileLists v-else-if="selectType === '课程附件'" :info="fileListResource" />
            <!-- <PreviewList v-else-if="selectType === '前置课程'" :info="previewList" /> -->
            <HomeworkList v-else-if="selectType === '成果'" :info="homeworkList" :unit-list="unitList"/>
            <!-- 环节 -->
            <CommnList v-else :info="currStepList" :unit-list="unitList"/>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import arrowLeft from '@/assets/ai-image/ai-interact/arrow-left.svg'
import triangleDown from '@/assets/ai-image/ai-interact/triangle-down.svg'
import Detail from '@/views/ai-interact/components/aiPrepare/detail.vue'
import Resource from '@/views/ai-interact/components/aiPrepare/resource.vue'
import FileLists from '@/views/ai-interact/components/aiPrepare/fileLists.vue'
import Guide from '@/views/ai-interact/components/aiPrepare/guide.vue'
import CommnList from '@/views/ai-interact/components/aiPrepare/commnList.vue'
// import PreviewList from '@/views/ai-interact/components/aiPrepare/previewList.vue'
import HomeworkList from '@/views/ai-interact/components/aiPrepare/homeworkList.vue'
import {
  getAiCourseUnitList,
  getAiCourseUnitInfo,
  getAiCourseUnitSectionList
} from '@/api/aicourse'
import { setAssistantId } from '@/utils/auth'
export default {
  components: {
    Detail,
    Resource,
    Guide,
    CommnList,
    FileLists,
    // PreviewList,
    HomeworkList
  },
  data () {
    return {
      courseId: this.$route.params.courseId,
      studentCourseId: this.$route.params.studentCourseId,
      unitIndex: this.$route.params.index,
      unitId: this.$route.params.unitId,
      shareToken: this.$route.query.token,
      assistantId: this.$route.params.assistantId,
      needBack: this.$route.query.needBack,
      close: this.$route.query.close || false,
      showLoading: true,
      arrowLeft,
      triangleDown,
      showCourseList: false,
      refresh: false,
      selectType: '课程简介',
      currStepList: null,
      currAiCourseUnitInfo: null,
      courseList: [],
      expendResource: null,
      guideResource: null,
      fileListResource: null,
      stepList: null,
      previewList: null,
      homeworkList: null,
      hasHomeworkList: false,
      commnList: [],
      unitList: []
    }
  },
  async created () {
    if (this.shareToken) {
      await this.$store.dispatch('saveToken', this.shareToken)
      await this.$store.dispatch('GetUserInfo')
    }
    if (this.assistantId) {
      await setAssistantId(this.assistantId)
    }
    if (this.$route.query.result) {
      this.handleSelect('成果')
    }
    this._getAiCourseUnitList()
    this._getAiCourseUnitInfo()
    this._getAiCourseUnitSectionList()
  },
  methods: {
    // 返回前面的页面
    async pre () {
      if (this.$route.query.close) {
        window.close()
        return
      }
      this.$router.push({
        path: `/aiPackage/${this.courseId}/${this.studentCourseId}/${this.assistantId}${this.$route.query && this.$route.query.token ? `?token=${this.$route.query.token}` : ''}`
      })
    },
    async handleCourse (item, index) {
      this.refresh = true
      setTimeout(async () => {
        this.unitIndex = index
        this.unitId = item.id
        this.showCourseList = false
        this.$router.push({
          path: `/aiPrepare/${this.courseId}/${this.studentCourseId}/${index}/${item.id}/${this.assistantId}${this.$route.query && this.$route.query.token ? `?token=${this.$route.query.token}` : ''}`
        })
        this.handleSelect('课程简介')
        await this._getAiCourseUnitList()
        await this._getAiCourseUnitInfo()
        await this._getAiCourseUnitSectionList()
        this.refresh = false
      }, 0)
    },
    // 课程信息
    async _getAiCourseUnitList () {
      const { data } = await getAiCourseUnitList({
        aicourseId: this.courseId,
        studentCourseId: this.studentCourseId
      })
      if (+data.code === 200) {
        this.courseList = data.data
      }
    },
    // 获取当前单元信息
    async _getAiCourseUnitInfo () {
      const { data } = await getAiCourseUnitInfo({
        aicourseUnitId: this.unitId,
        studentCourseId: this.studentCourseId
      })
      if (+data.code === 200) {
        if (data.data.resourceList && data.data.resourceList.length > 0) {
          const resourceList = data.data.resourceList
          // 材料准备
          this.expendResource = resourceList.find(
            item => item.resourceType === 'AI_PREPARE'
          )
          // 指导手册
          this.guideResource = resourceList.find(
            item => item.resourceType === 'GUIDE_MANUAL'
          )
          // 课程附件
          this.fileListResource = resourceList.filter(
            item => item.resourceType === 'AI_ATTACHMENT'
          )
        }
        this.currAiCourseUnitInfo = data
      }
    },
    // 环节，终极挑战数据
    async _getAiCourseUnitSectionList () {
      const { data } = await getAiCourseUnitSectionList({
        aicourseUnitId: this.unitId,
        studentCourseId: this.studentCourseId
      })
      if (+data.code === 200) {
        const list = data.data
        this.unitList = data.data
        const commnList = {}
        const stepList = {}
        let previewList = null
        let learnAchievedList = null
        let afterAchievedList = null
        let nextPreviewList = null
        list.map((val) => {
          if (['PREVIEW', 'LEARNING_ACHIEVED', 'AFTER_ACHIEVED', 'NEXT_PREVIEW'].indexOf(val.aiSectionType) > -1) {
            switch (val.aiSectionType) {
              case 'PREVIEW':
                previewList = val
                break
              case 'LEARNING_ACHIEVED':
                learnAchievedList = val
                break
              case 'AFTER_ACHIEVED':
                afterAchievedList = val
                break
              case 'NEXT_PREVIEW':
                nextPreviewList = val
                break
            }
            return
          }
          if (!commnList[val.step]) {
            // 环节名-环节内容
            commnList[val.step] = {}
            stepList[val.step] = {} // UI显示用
            stepList[val.step].stepType = val.stepType
            stepList[val.step].count = 0
            if (val.stepName) {
              stepList[val.step].name = val.stepName
            } else {
              if (val.stepType === 'CHALLENGE') {
                stepList[val.step].name = '终极挑战'
              } else {
                stepList[val.step].name = '环节' + val.step
              }
            }
          }
          stepList[val.step].count++
          // 环节[环节index][环节内题目index]
          commnList[val.step][val.indexNo] = val
        })
        this.commnList = commnList
        this.stepList = stepList
        this.previewList = previewList
        this.hasHomeworkList = previewList || learnAchievedList || afterAchievedList || nextPreviewList
        this.homeworkList = {
          previewList,
          nextPreviewList,
          learnAchievedList,
          afterAchievedList
        }
      }
    },
    handleSelect (type) {
      this.selectType = type
      if (type.indexOf('commn') > -1) {
        const key = type.split(',')[1]
        this.currStepList = this.commnList[Number(key)]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.home {
  width: 100vw;
  height: 100vh;
  min-width: 965px;
  min-height: 650px;
  background: linear-gradient(109.39deg, #FCECFF -23.25%, #ABF6FF 97.11%);
  position: relative;

  .content {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;

    .a-head {
      width: 100%;
      display: flex;
      align-items: center;
      padding: vh2(30) vh2(30) 0 vh2(30);
      box-sizing: border-box;

      .back {
        width: vh2(60);
        height: vh2(60);
        margin-right: vh2(14);
      }

      .relative {
        width: calc(100% - #{vh2(60)});
      }

      .more {
        width: vh2(15);
        height: vh2(8);
        margin-left: vh2(15);
      }

      .ai-name-box {
        width: 100%;
      }

      .ai-name {
        font-size: vh2(30);
        max-width: calc(100% - #{vh2(30)});
        color: #000;
      }
      .grade_ul {
        position: absolute;
        left: 0;
        top: vh2(50);
        min-width: vh2(150);
        max-width: vh2(400);
        max-height: vh2(340);
        background: #FFFFFF;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        border-radius: 16px;
        padding: vh2(30);
        z-index: 12;
        display: flex;
        flex-direction: column;
        gap: vh2(23);
        overflow-x: hidden;
        @include aiScrollBar;

        li {
          text-align: left;
          font-family: 'PingFang SC';
          font-weight: 500;
          font-size: vh2(20);
          line-height: vh2(28);
          cursor: pointer;
        }

        .blue {
          color: #2D9CDB;
        }
      }
    }

    .a-content {
      padding: vh2(50) vh2(30);
      box-sizing: border-box;
      height: calc(100% - #{vh2(30)} - #{vh2(60)});

      .tc {
        text-align: center;
      }

      .a-card {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: vh2(10);
        height: vh2(450);
        background: rgba(255, 255, 255, 0.3);
        border: vh2(1) solid #FFFFFF;
        border-radius: vh2(10);
        box-sizing: border-box;
        @include aiScrollBar;

      }

      .a-items {
        width: vh2(180);
        margin-right: vh2(10);
        overflow-y: auto;

        .li {
          width: 100%;
          height: vh2(30);
          display: flex;
          justify-content: center;
          align-items: center;
          color: #4F4F4F;
          font-weight: 500;
          font-size: vh2(14);
          margin-bottom: vh2(20);
          padding: vh2(10) vh2(5);
          box-sizing: border-box;
          &:last-child {
            margin-bottom: 0;
          }
          &:hover {
            background: linear-gradient(90deg, #22C1C3 0%, #FDBB2D 100%);
            border-radius: vh2(10);
            color:#fff !important;
          }
        }

        .li-active {
          background: linear-gradient(90deg, #22C1C3 0%, #FDBB2D 100%);
          border-radius: vh2(10);
          color:#fff !important;
        }
      }

      .a-detail {
        width: calc(100% - #{vh2(180)} - #{vh2(10)});
        height: vh2(450);
      }
    }
  }
}
</style>
