<template>
  <div class="home">
    <div class="bg-circle1"></div>
    <div class="bg-circle2"></div>
    <div class="content">
      <div class="a-head">
        <div class="flex align-center">
          <img class="back pointer" :src="arrowLeft" alt="返回按钮" @click="pre" />
          <div class="relative">
            <div class="ai-name-box flex align-center" @click="courseList && courseList.length > 1 ? showCourseList = !showCourseList : ''">
              <div class="ai-name pointer">{{ course.title || '' }}</div>
              <img v-if="courseList && courseList.length > 1" class="more pointer" :src="triangleDown" alt="筛选" />
            </div>
            <ul v-if="showCourseList" class="grade_ul">
              <li
                v-for="item in courseList"
                :key="item.id"
                :class="{'blue': +studentCourseId === +item.id}"

                @click="handleCourse(item)"
              >
                <div class="article-singer-container">{{ item.aicourse.title }}</div>
              </li>
            </ul>
          </div>
        </div>
        <div class="flex align-center">
          <div v-if="course && course.book" class="book-btn">
            <img :src="bookinfo" alt="" @click="showReadInfo" />
          </div>
          <div v-if="sources[0].src" class="guide-btn">
            <img :src="btnGuide" alt="" @click="openVideo" />
          </div>

        </div>
      </div>
      <div class="a-content">
        <swiper
          ref="lineSwiper"
          class="swiper"
          :options="swiperOption"
          @slideChange="onSlideChange"
        >
          <swiper-slide v-for="(item, index) in unitList" :key="item.id">
            <div class="course-card">
              <div class="c-img">
                <img class="w h object-cover" :src="item.coverUrl || defaultCourse" />
                <div class="c-tag">{{ item.unitNo }}</div>
                <div v-show="unitFinished(item)" class="c-done">
                  <div class="text1">已完成</div>
                </div>
                <div v-if="item.hasWork" class="c-result">
                  <div class="text2 pointer" @click="gotoPrepare(index, item, 'result')">学习成果</div>
                </div>
              </div>
              <div class="c-title">
                {{ item.title || '' }}
              </div>
              <div class="c-coin">
                <template v-if="unitFinished(item)">
                  <img class="coin" src="../../assets/ai-image/icon/coin.png" />
                  x
                  <span class="num">{{ (item.aicourseUnitUser && item.aicourseUnitUser.score) || 0 }}</span>
                </template>
              </div>
              <div class="c-btns swiper-no-swiping">
                <div class="btn1" @click.stop="gotoPrepare(index, item)">备课</div>
                <div class="btn2" @click.stop="beforeGotoPlay(index, item)">上课</div>
              </div>
            </div>
            <div v-if="!item.externalLessonUrl && currentProgress === item.unitNo" class="a-here">
              <img class="triangle" src="../../assets/ai-image/icon/triangle.svg" />
              上到这里
            </div>
          </swiper-slide>
          <div slot="button-prev" class="swiper-button-prev swiper-button-white"></div>
          <div slot="button-next" class="swiper-button-next swiper-button-white"></div>
        </swiper>
      </div>
    </div>
    <aiDialog
      v-if="aiDialogShow"
      :is-empty="isEmpty"
      @leftCallback="leftBtnCallback"
      @rightCallback="rightBtnCallback"
    />
    <loadPage
      v-if="loadPageShow"
      :is-loading="loadPageShow"
      :to-path="loadPageToPath"
      :video-list="loadPageVideoList"
      :process="loadProcess"
    />
    <indexLoad v-if="showLoading" />
    <div v-if="showVideo" class="navbar-video">
      <div class="shadow"></div>
      <div class="video-container">
        <my-video :sources="sources" />
        <div class="close" @click="closeVideo">
          <img :src="close" alt="" />
        </div>
      </div>
    </div>
    <readInfo v-if="showRead" :read-id="showReadId" @close="showRead = false" />
  </div>
</template>

<script>
import arrowLeft from '@/assets/ai-image/ai-interact/arrow-left.svg'
import triangleDown from '@/assets/ai-image/ai-interact/triangle-down.svg'
import defaultCourse from '@/assets/ai-image/default/default-course.jpg'
import btnGuide from '@/assets/ai-image/ai-interact/btn-guide.png'
import bookinfo from '@/assets/ai-image/ai-interact/bookinfo.svg'
import close from '@/assets/ai-image/icon/video-close.svg'
import {
  getAiCourse,
  getAiCourseUnitList,
  getStudentCourseList,
  getAiCourseUnitSectionList,
  getUserAicourseVideoList
} from '@/api/aicourse'
import { setAssistantId, getAssistantId } from '@/utils/auth'
import aiDialog from './components/aiPackage/Dialog.vue'
import loadPage from './components/loadPage/index.vue'
import indexLoad from '@/views/ai-interact/components/indexLoad.vue'
import MyVideo from '@/components/video/bingoVideo.vue'
import readInfo from './components/readInfo/index.vue'

export default {
  components: {
    aiDialog,
    loadPage,
    indexLoad,
    MyVideo,
    readInfo
  },
  data () {
    return {
      showRead: false,
      showReadId: '',
      bookinfo,
      arrowLeft,
      triangleDown,
      defaultCourse,
      btnGuide,
      close,
      aiDialogShow: false,
      clickIndex: null,
      lineList: [],
      unitList: [],
      showLoading: false,
      showVideo: false,
      course: {
        aicourse: {}
      },
      courseId: this.$route.params.courseId,
      studentCourseId: this.$route.params.studentCourseId,
      shareToken: this.$route.query.token,
      assistantId: this.$route.params.assistantId,
      grabCursor: true,
      shortSwipes: false,
      swiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
        slidesPerGroup: 3,
        // allowTouchMove: false,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
        // slidesPerView: 3,
        // spaceBetween: 30,
        // touchRatio: 1,
        // longSwipesMs: 500,
        // observer: true,
        // touchStartPreventDefault: true,
        // observeParents: true,
        // breakpoints: {
        //   1280: {
        //     slidesPerView: 3
        //   },
        //   1366: {
        //     slidesPerView: 4
        //   },
        //   1920: {
        //     slidesPerView: 5
        //   }
        // }
        // mousewheel: {
        //   forceToAxis: true,
        //   releaseOnEdges: true
        // }
      },
      activeIndex: 0,
      courseList: [],
      showCourseList: false,
      comeUrl: this.$route.query.url,
      isCacheAll: null,
      loadPageShow: false,
      loadPageToPath: '',
      loadPageVideoList: null,
      loadProcess: 0,
      isEmpty: true,
      sources: [
        {
          type: '',
          src: '',
          duration: 0
        }
      ]
    }
  },
  computed: {
    swiper () {
      return this.$refs.lineSwiper.$swiper
    },
    // 当前学习进度
    currentProgress () {
      for (var item of this.unitList) {
        if (!this.unitFinished(item)) {
          return item.unitNo
        }
      }
      return this.unitList.length + 1
    }
  },
  async created () {
    this._getAiCourse()
    this._getCourseList()
    if (this.$route.query.token) {
      await this.$store.dispatch('saveToken', this.$route.query.token)
      await this.$store.dispatch('GetUserInfo')
    }
    if (this.$route.params.assistantId) {
      await setAssistantId(this.$route.params.assistantId)
    }
  },
  async mounted () {
    setTimeout(() => {
      const img = new Image()
      img.src = require('@/assets/ai-image/default/bg-ai-package.jpeg')
      img.onload = () => {
        this.showLoading = false
      }
    }, 200)
  },
  methods: {
    // 返回前面的页面
    async pre () {
      this.$router.push({
        path: `/aiInteract${this.$route.query && this.$route.query.token ? `?token=${this.$route.query.token}` : ''}`
      })
    },
    // 挑战等级
    challengeStatus (item) {
      if (
        item.aicourseUnitUser === null ||
        item.aicourseUnitUser.challengeResult === null
      ) { return '未完成' }
      if (item.aicourseUnitUser.challengeStatus === 'NONE') return '未挑战'
      if (item.aicourseUnitUser.challengeResult > 80) return '卓越'
      if (
        item.aicourseUnitUser.challengeResult > 50 &&
        item.aicourseUnitUser.challengeResult < 81
      ) { return '优秀' }
      if (item.aicourseUnitUser.challengeResult < 51) return '良好'
      return '未完成'
    },
    _getAiCourse () {
      const param1 = {
        aicourseId: this.courseId
      }
      const param2 = {
        aicourseId: this.courseId,
        studentCourseId: this.studentCourseId

      }
      if (this.$route.query.token) {
        param2.token = this.$route.query.token
      }
      getAiCourse(param1)
        .then((response) => {
          if (+response.data.code === 200) {
            if (response.data.data.length > 0) {
              this.course = response.data.data[0]
              if (this.course.guideResource) {
                const data = this.course.guideResource
                const videoUrl = data.url
                const videoType = this.checkFileType(videoUrl)
                if (videoType === 'm3u8') {
                  this.sources[0].type = 'application/x-mpegURL'
                } else {
                  this.sources[0].type = ''
                }
                this.sources[0].src = videoUrl
              } else {
                this.sources = [{
                  type: '',
                  src: '',
                  duration: 0
                }]
              }
            }
          }
        })
        .catch((err) => {
          // this.$toast(err, {
          //   position: 'center',
          //   duration: '2000'
          // })
          console.log(err)
        })
      getAiCourseUnitList(param2)
        .then((response) => {
          if (+response.data.code === 200) {
            this.unitList = response.data.data
            // 跳当前进度
            this.$nextTick(() => {
              for (var item of this.unitList) {
                if (!this.unitFinished(item)) {
                  this.swiper.slideTo(item.unitNo - 1)
                  return
                }
              }
              return this.swiper.slideTo(this.unitList.length - 1)
            })
          }
        })
        .catch((err) => {
          // this.$toast(err, {
          //   position: 'center',
          //   duration: '2000'
          // })
          console.log(err)
        })
    },
    async _getCourseList () {
      const assistantId = await getAssistantId()
      const params = {
        'studentCourseListType': 'ASSISTANT',
        'assistantUserId': assistantId
      }
      getStudentCourseList(params).then(
        (res) => {
          if (res.data.data) {
            this.courseList = res.data.data.filter(item => item.courseType === 'AI_COURSE')
          } else {
            this.courseList = []
          }
        }
      )
    },
    onSlideChange () {
      this.activeIndex = this.swiper.activeIndex
    },
    leftBtnCallback () {
      this.aiDialogShow = false
    },
    rightBtnCallback () {
      this.aiDialogShow = false
      this.gotoPlay(this.clickIndex)
    },
    // 单元是否完成
    unitFinished (item) {
      return (
        item.aicourseUnitUser &&
        item.aicourseUnitUser.complete &&
        item.aicourseUnitUser.total &&
        item.aicourseUnitUser.complete >= item.aicourseUnitUser.total &&
        (item.aicourseUnitUser.challengeStatus === 'NONE' ||
          item.aicourseUnitUser.challengeStatus === 'END')
      )
    },
    // 进入ai课页面
    beforeGotoPlay (index, item) {
      if (item.externalLessonUrl) {
        if (this.isPlaying) {
          this.playMusic()
        }
        window.open(item.externalLessonUrl + '&opt=2', '_blank')
      } else {
        this.clickIndex = index
        if (!item.aicourseUnitUser) {
          this._getAiCourseUnitSectionList(item.id)
        } else {
          this.gotoPlay(index)
        }
      }
    },
    _getAiCourseUnitSectionList (unitId) {
      this.isEmpty = false
      getAiCourseUnitSectionList({
        aicourseUnitId: unitId,
        studentCourseId: this.studentCourseId
      }).then(
        response => {
          if (response.data.data.length > 0) {
            this.isEmpty = false
          } else {
            this.isEmpty = true
          }
          this.aiDialogShow = true
        }
      )
    },
    async gotoPlay (index) {
      // await this.$refs.enterbtn.play()
      setTimeout(() => {
        // this.$refs.enterbtn.pause()
        var unit = this.unitList[index]
        // console.log(unit)
        // if (window.ipc && +this.studentCourseId !== 0) {
        //   this.checkDisc(unit.id, `/ai/${this.courseId}/${this.studentCourseId}/${unit.id}`)
        // } else {
        this.$router.push({
          path: `/ai/${this.courseId}/${this.studentCourseId}/${unit.id}${this.$route.query && this.$route.query.token ? `?token=${this.$route.query.token}` : ''}`
        })
        // }
      }, 0)
    },
    async gotoPrepare (index, item, type) {
      if (this.course.type === 'SELF') {
        this.isEmpty = false
        getAiCourseUnitSectionList({
          aicourseUnitId: item.id,
          studentCourseId: this.studentCourseId
        }).then(response => {
          if (response.data.data.length > 0) {
            const query = {}
            if (type === 'result') {
              query.result = 1
            }
            this.$router.push({
              path: `/aiPrepare/${this.courseId}/${this.studentCourseId}/${index}/${item.id}/${this.assistantId}${this.$route.query && this.$route.query.token ? `?token=${this.$route.query.token}` : ''}`,
              query
            })
          } else {
            this.isEmpty = true
            this.aiDialogShow = true
          }
        })
      } else {
        // 第三方课程
        if (item.externalPrepareUrl) {
          window.open(item.externalPrepareUrl + '&opt=2', '_blank')
        } else {
          this.isEmpty = true
          this.aiDialogShow = true
        }
      }
    },
    // 课程包列表
    async handleCourseArrow () {
      // await this.$refs.buttonAu.play()
      // setTimeout(() => {
      this.showCourseList = !this.showCourseList
      // }, 800)
    },
    async handleCourse (item) {
      // setTimeout(() => {
      this.studentCourseId = item.id
      this.courseId = item.aicourse.id
      this.showCourseList = false
      this.$router.push({ path: `/aiPackage/${item.aicourse.id}/${item.id}/${this.assistantId}${this.$route.query && this.$route.query.token ? `?token=${this.$route.query.token}` : ''}` })
      this._getAiCourse()
      // }, 800)
    },
    // 预加载
    download (unitList, downloadSize, totalSize) {
      this.loadPageShow = true
      const downloadProcess = Math.floor((downloadSize) / totalSize * 100) > 95 ? 98 : Math.floor((downloadSize) / totalSize * 100)
      this.loadProcess = downloadProcess
      window.ipc.invoke('blockSave', unitList).then(res => {
        const { list, status } = res
        if (status === 'done') {
          this.loadProcess = 100
        }
        this.loadPageVideoList = list
        this.loadPageShow = false
        this.$router.push({ path: this.loadPageToPath })
      })
      window.ipc.on('saveResult', (event, result) => {
        downloadSize = downloadSize + result.size
        const process = Math.floor((downloadSize) / totalSize * 100) > 95 ? 98 : Math.floor((downloadSize) / totalSize * 100)
        this.loadProcess = process
      })
    },
    async checkDisc (id, toPath) {
      try {
        const { data } = await getUserAicourseVideoList({
          aicourseUnitId: id,
          studentCourseId: this.studentCourseId
        })
        const unitList = {}
        data.data.map(val => {
          val.aicourse.aicourseUnitList.map(aiVal => {
            unitList[aiVal.id] = {
              list: this.formateFileData(aiVal.aicourseUnitSectionList),
              videoSizeTotal: aiVal.videoSizeTotal
            }
          })
        })
        window.ipc.invoke('isExist', unitList[id].list).then(res => {
          console.log('isExist', res)
          const { list, blockSize } = res
          let catchLength = 0
          let downloadSize = 0
          for (const key in list) {
            if (list[key].isCache) {
              catchLength++
              downloadSize += list[key].size
            } else {
              downloadSize += list[key].lastBlock * blockSize
            }
          }
          if (catchLength === Object.keys(unitList[id].list).length) {
            this.$router.push({ path: toPath })
          } else {
            this.loadPageToPath = toPath
            this.loadPageVideoList = list
            this.loadPageShow = true
            this.download(list, downloadSize * 1024, unitList[id].videoSizeTotal * 1024)
          }
        })
      } catch (error) {
        console.log(error)
      }
    },
    formateFileData (sectionList) {
      const obj = {}
      sectionList.map(sectionVal => {
        const fileName = sectionVal.mediaFile.url.split('/').reverse()[0].split('.')[0]
        const { id, url, size, expendType } = sectionVal.mediaFile
        obj[fileName] = {
          isCache: false,
          lastBlock: 0,
          id,
          url,
          size,
          expendType,
          fileName
        }
      })
      return obj
    },
    checkFileType (fileUrl) {
      if (fileUrl.indexOf('?') !== -1) {
        var mediaUrl = fileUrl.split('?')[0]
        if (mediaUrl.lastIndexOf('.') !== -1) {
          return mediaUrl.substr(
            mediaUrl.lastIndexOf('.') + 1,
            mediaUrl.length
          )
        }
      } else {
        fileUrl = fileUrl
          .substring(fileUrl.lastIndexOf('/') + 1, fileUrl.length)
          .split('?')[0]
          .split('#')[0]
        if (fileUrl.lastIndexOf('.') > -1) {
          return fileUrl
            .substr(fileUrl.lastIndexOf('.') + 1, fileUrl.length)
            .toLowerCase()
        }
      }
    },
    openVideo () {
      this.showVideo = true
    },
    closeVideo () {
      this.showVideo = false
    },
    showReadInfo () {
      this.showReadId = this.courseId
      this.showRead = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.home {
  width: 100vw;
  height: 100vh;
  min-width: 965px;
  min-height: 650px;
  background: rgba(217, 246, 255, 1);
  position: relative;

  .bg-circle1 {
    width: vh2(210);
    height: vh2(210);
    min-width: 210px;
    min-height: 210px;
    background: rgba(172, 123, 240, 0.33);
    filter: blur(62.5px);
    position: absolute;
    left: vh2(32);
    bottom: vh2(63);
    z-index: 9;
  }

  .bg-circle2 {
    width: vh2(400);
    height: vh2(330);
    min-width: 400px;
    min-height: 330px;
    background: rgba(172, 123, 240, 0.33);
    filter: blur(62.5px);
    position: absolute;
    right: vh2(160);
    top: vh2(82);
    z-index: 9;
  }

  .content {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    box-sizing: border-box;
    z-index: 10;

    .a-head {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: vh2(30) vh2(30) 0 vh2(30);
      box-sizing: border-box;

      .back {
        width: vh2(60);
        height: vh2(60);
        margin-right: vh2(14);
      }

      .more {
        width: vh2(15);
        height: vh2(8);
        margin-left: vh2(15);
      }

      .ai-name {
        font-size: vh2(30);
        color: #000;
      }
      .grade_ul {
        position: absolute;
        left: 0;
        top: vh2(50);
        min-width: vh2(150);
        max-width: vh2(400);
        max-height: vh2(340);
        background: #FFFFFF;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        border-radius: 16px;
        padding: vh2(30);
        z-index: 12;
        display: flex;
        flex-direction: column;
        gap: vh2(23);
        overflow-x: hidden;
        @include aiScrollBar;

        li {
          text-align: left;
          font-family: 'PingFang SC';
          font-weight: 500;
          font-size: vh2(20);
          line-height: vh2(28);
          cursor: pointer;
        }

        .blue {
          color: #2D9CDB;
        }
      }

      .guide-btn {
        // position: absolute;
        // right: vh2(25);
        // top: vh2(40);
        height: vh2(45);
        margin-right: 30px;

        img {
          height: 100%;
          object-fit: contain;
          cursor: pointer;
        }
      }

      .book-btn {
        // position: absolute;
        // right: vh2(150);
        // top: vh2(40);
        height: vh2(45);
        margin-right: 30px;

        img {
          height: 100%;
          object-fit: contain;
          cursor: pointer;
        }
      }

    }

    .a-content {
      padding: vh2(70) 0 0 0;
      box-sizing: border-box;

      .swiper-slide {
        width: vh2(280);
        padding: 0 vh2(10);
        box-sizing: border-box;
      }

      .swiper-button-prev, .swiper-button-next {
        background: rgba(0, 0, 0, 0.4);
        border: vh2(1) solid #FFFFFF;
        border-radius: vh2(6);
        font-size: vh2(14) !important;
        padding: 0 vh2(3);
        position: fixed;
        top: auto;
        bottom: vh2(10);

        &::after {
          font-size: vh2(14) !important;
        }
      }

      .swiper-button-next {
        right: vh2(15);
      }
      .swiper-button-prev {
        // left: vh2(15);
        left: auto;
        right: vh2(55);
      }

      .a-here {
        font-weight: 500;
        font-size: vh2(16);
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #333333;
        margin-top: vh2(15);

        .triangle {
          width: vh2(30);
          height: vh2(24);
        }
      }

      .course-card {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(79, 79, 79, 0.23);
        box-shadow: 0px 4px 2px rgba(0, 0, 0, 0.07);
        border-radius: vh2(10);
        padding: vh2(10) vh2(10) vh2(20) vh2(10);
        position: relative;

        .c-img {
          width: 100%;
          height: vh2(170);
          margin-bottom: vh2(10);
          position: relative;
          .c-tag{
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            width: vh2(52);
            height: vh2(45);
            right: vh2(5);
            top: vh2(5);
            background: #F2C94C;
            border: 1px solid #F2C94C;
            box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
            border-radius: vh2(7);
            font-size: vh2(32);
            color: #000000;
            font-weight: 500;
            z-index: 4;
          }
          .c-done{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.31);
            border-radius: vh2(10);
            color: #fff;
            z-index: 2;
            .text1 {
              font-weight: 500;
              font-size: vh2(34);
              color: #FFFFFF;
              margin-bottom: vh2(5);
            }
          }

          .c-result {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: vh2(10);
            color: #fff;
            z-index: 3;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            padding-bottom: vh2(15);
            box-sizing: border-box;
            .text2 {
              font-size: vh2(16);
              color: #FFFFFF;
              text-decoration-line: underline;
            }
          }

          img {
            border-radius: vh2(10);
          }
        }

        .c-title {
          width: 100%;
          min-height: vh2(50);
          font-weight: 500;
          font-size: vh2(18);
          color: #333333;
          display: -webkit-box;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; //需要显示的行数
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: left;
        }

        .c-coin {
          width: 100%;
          height: vh2(60);
          display: flex;
          justify-content: center;
          align-items: center;
          color: #333333;
          font-size: vh2(14);

          .coin {
            width: vh2(30);
            height: vh2(30);
            margin-right: vh2(5);
          }
          .num {
            font-size: vh2(20);
            font-weight: 500;
            color: #000;
            margin-left: vh2(5);
          }
        }

        .c-btns {
          width: 100%;
          height: vh2(50);
          display: flex;
          justify-content: center;
          align-items: center;

          .btn1, .btn2 {
            border-radius: vh2(5);
            color: #000000;
            width: vh2(70);
            height: vh2(40);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-weight: 500;
            font-size: vh2(18);
          }
          .btn1 {
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid #000000;
            margin-right: vh2(30);
          }
          .btn2 {
            background: linear-gradient(90deg, #FFB347 0%, #FFCC33 100%);
          }
        }
      }
    }
  }

  .navbar-video {
    position: absolute;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10;

    .shadow {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.2);
    }

    .video-container {
      position: absolute;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50%;
      width: vh2(633);
      height: vh2(415);
      z-index: 9999;

      .close {
        position: absolute;
        object-fit: contain;
        top: vh2(12);
        right: vh2(8);
        width: vh2(16);
        height: vh2(16);
        cursor: pointer;

        .close-icon {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
