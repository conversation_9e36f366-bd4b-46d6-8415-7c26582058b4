<template>
  <div id="play-box" class="app-center ai-play-lesson-center">
    <audio ref="buttonAudio" controls="controls" hidden :src="buttonAu"></audio>
    <audio ref="rightAudio" controls="controls" hidden :src="rightAu"></audio>
    <div class="play-header">
      <Tools
        v-if="!showPlayBtn &&
          sectionContext &&
          sectionContext.currentSection &&
          !isVideoPlay &&
          sectionContext.currentSection.aiSectionType !== 'REPORT' &&
          !closeTool &&
          !isMobile"
        ref="tools"
        :unit-id="unitId"
        @handleActive="handleActive"
      />
      <!-- 画板 -->
      <Draw v-show="toolActive" ref="draw" :active="toolActive" @closeDraw="toolActive = false;$refs.tools.closeDraw()" />
      <div class="play-inner">
        <!-- <div class="video-player-box" :class="[ isWideScreen ? 'full-height-screen' : 'full-width-screen']"> -->
        <div class="video-player-box">
          <!-- 上课按钮 -->
          <div v-if="showPlayBtn" class="play-btn" @click="startLesson">开始上课</div>
          <!-- 章节过渡动画 -->
          <translate-animate
            v-if="showAnimate"
            :show-end-animate="showEndAnimate"
          />
          <!-- 章节过渡 || 退出弹框 -->
          <Dialog
            v-if="sectionContext && (showNextState || showQuitState || showJumpState)"
            style="position: fixed; z-index: 9999;"
            :context="sectionContext"
            :backcover="quizBg"
            :show-next-state="showNextState"
            :is-mobile="isMobile"
            :source="sourceProps ? sourceProps : $route.query.source"
            :title="showJumpState ? '继续学习未完成的环节？' : undefined"
            :left-text="showJumpState? '再想想': undefined"
            :right-text="showJumpState? '去学习': undefined"
            @leftCallback="leftBtnCallback"
            @rightCallback="rightBtnCallback"
          />
          <!-- 课程报告 -->
          <report
            v-if="
              sectionContext &&
                sectionContext.currentSection &&
                sectionContext.currentSection.aiSectionType == 'REPORT'
            "
            :context="sectionContext"
            :unit-id="unitId"
            @showQuit="showQuit"
            @again="_seekSection(0)"
          />
          <!-- 练习题 -->
          <quiz-box
            v-if="
              sectionContext &&
                sectionContext.currentSection &&
                sectionContext.currentSection.aiSectionType == 'QUESTION'
            "
            :key="sectionContext.currentSection.id"
            :class="{'animate__animated animate__fadeIn' : showAnimate}"
            :context="sectionContext"
            :current-section-index="sectionContext.currentSectionIndex"
            :backcover="quizBg"
            :secound="secound"
            :caption="caption"
            :challenge-status="challengeStatus"
            :challenge-score="challengeScore"
            :show-animate="showAnimate"
            :show-play-btn="showPlayBtn"
            :is-portrait="!isMobile || isPortrait"
            :is-mobile="isMobile"
            :is-full-screen-mode="isFullScreenMode"
            @playback="handlePlayback"
            @showQuit="showQuit"
            @playButtonAudio="playButtonAudio"
            @answerRight="answerRight"
            @answerWrong="answerWrong"
            @getAicourseUnitUser="_getAicourseUnitUser"
            @fullScreen="handleFullScreen"
          />
          <!-- 视频播放器 -->
          <bingo-video
            v-if="videoLoad &&
              autoplay !== null &&
              sectionContext &&
              sectionContext.currentSection &&
              sectionContext.currentSection.aiSectionType == 'VIDEO'"
            ref="bingoVideo"
            :has-full="false"
            :sources="sources"
            :autoplay="autoplay"
            @onPlayerTimeUpdate="onPlayerTimeUpdate"
            @onPlayerPlay="onPlayerPlay"
            @onPlayerPause="onPlayerPause"
            @onPlayerEnd="onPlayerPause"
          />

          <!-- 防录屏保护组件 -->
          <video-anti-recording
            v-if="sectionContext &&
              sectionContext.currentSection &&
              sectionContext.currentSection.aiSectionType == 'VIDEO'"
            :enabled="true"
            :video-player-ref="$refs.bingoVideo"
            @recording-detected="handleRecordingDetected"
            @request-pause-video="handleAntiRecordingPause"
            @recording-block-removed="handleRecordingBlockRemoved"
          />
          <video-next
            v-if="showVideoNext &&
              sectionContext &&
              sectionContext.currentSection &&
              sectionContext.currentSection.aiSectionType == 'VIDEO'"
            :context="sectionContext"
            :current-section-index="sectionContext.currentSectionIndex"
            @onPlayerEnd="onPlayerEnd"
            @seekSection="videoPrev"
          />
          <!-- 奇思妙想 -->
          <thingking
            v-if="
              !showPlayBtn &&
                sectionContext &&
                sectionContext.currentSection &&
                sectionContext.currentSection.aiSectionType == 'THINK'
            "
            :is-full-screen-mode="isFullScreenMode"
            :context="sectionContext"
            :current-section-index="sectionContext.currentSectionIndex"
            @playback="handlePlayback"
            @onThinkEnd="handlePlayback('next')"
            @updateThinkProgress="_updateProgress"
            @fullScreen="handleFullScreen"
          />
          <work
            v-if="
              !showPlayBtn &&
                sectionContext &&
                sectionContext.currentSection &&
                workType.indexOf(sectionContext.currentSection.aiSectionType) > -1
            "
            :context="sectionContext"
            :current-section-index="sectionContext.currentSectionIndex"
            @updateProgress="_updateProgress"
            @prev="handlePlayback('prev')"
            @next="handlePlayback('next')"
          />
          <Scratch
            v-if="
                showScratch &&
                sectionContext &&
                sectionContext.currentSection &&
                sectionContext.currentSection.aiSectionType == 'TRAINING'
            "
            :context="sectionContext"
            :is-mobile="isMobile"
            :student-course-id="studentCourseId"
            @toTraining="_getAiCourseUnitSectionList(true)"
            @playback="handlePlayback"
          />
          <game
            v-if="
              !showPlayBtn &&
                sectionContext &&
                sectionContext.currentSection &&
                sectionContext.currentSection.aiSectionType == 'SCREEN_INTERACT'
            "
            :context="sectionContext"
            :is-mobile="isMobile"
            @playback="handlePlayback"
            @playButtonAudio="playButtonAudio"
            @updateProgress="_updateProgress"
          />
          <!-- 顶部导航 -->
          <navigation
            v-if="sectionContext && !showPlayBtn"
            ref="navigation"
            :context="sectionContext"
            :is-h5="isMobile"
            @seek="_seekSection"
            @showQuit="showQuit"
          />
          <!-- h5返回按钮 -->
          <div
            v-if="isMobile"
            class="h5-arrow-left"
            @click="showQuit"
          >
            <img :src="AiArrowLeft" alt="" />
          </div>
          <!-- h5目录 -->
          <div
            v-if="isMobile"
            class="h5-menu"
            @click="openH5Menu"
          >
            <img :src="AiMenu" alt="" />
          </div>
        </div>
      </div>

      <!-- 弃用强制下载客户端弹框 -->
      <!-- <div v-if="showUploadHint" class="upload-bg">
        <div class="upload-dialog">
          <div class="t1">本版本是演示版请下载缤果课堂客户端使用</div>
          <a href="https://x.cuiya.cn" class="t2" target="_blank">下载</a>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script>
import DpiFixMixin from '@/utils/dpi-fix-mixin'
import {
  getAiCourseUnitSectionList,
  getAicourseUnitUser,
  getAiCourseUnitSection,
  updateAicourseUnitUserProgress,
  getStudentCourseById,
  getSecondsConfig
} from '@/api/aicourse'
import { mapGetters } from 'vuex'
import QuizBox from './components/QuizBox'
import Dialog from './components/Dialog'
import Report from './components/Report'
import Thingking from './components/Thinking'
import Work from './components/Work'
import arrowBack from '@/assets/images/ai/arrow-back.png'
import arrowBackWhite from '@/assets/images/ai/arrow-back-white.png'
import TranslateAnimate from './components/TranslateAnimate'
import buttonAu from '@/assets/audio/button.mp3'
import rightAu from '@/assets/audio/right.mp3'
import BingoVideo from '@/components/video/bingoVideo.vue'
import Tools from './components/tools/index.vue'
import Draw from './components/tools/components/draw.vue'
import Navigation from './components/Naviigation.vue'
import VideoNext from './components/VideoNext.vue'
import Game from './components/game'
import Scratch from '@/views/ai/components/scratch'
import VideoAntiRecording from '@/components/VideoAntiRecording/index.vue'
import { getAssistantId } from '@/utils/auth'
import { debounce } from '@/utils/index'
import AiArrowLeft from '@/assets/ai-image/icon/ai-arrow-left.svg'
import AiMenu from '@/assets/ai-image/icon/ai-menu.svg'
export default {
  mixins: [DpiFixMixin],
  props: {
    previewProps: {
      type: String
    },
    previewIndexProps: {
      type: String
    },
    sourceProps: {
      type: String
    }
  },
  data () {
    return {
      buttonAu,
      rightAu,
      arrowBack,
      arrowBackWhite,
      AiArrowLeft,
      AiMenu,
      index: this.$route.params.index,
      payConfirm: false,
      videoLoad: false,
      courseId: this.$route.params.courseId,
      unitId: this.$route.params.unitId,
      sources: [
        {
          type: '',
          src: '',
          duration: 0,
          size: 0
        }
      ],
      sectionContext: undefined,
      unitList: [],
      sectionList: [],
      segmentList: [],
      partionList: [],
      sectionInfoMap: {},
      currentSectionIndex: undefined,
      currentSegmentIndex: undefined,
      currentPartionIndex: undefined,
      progressIndex: undefined,
      currentSection: undefined,
      currentSegment: undefined,
      currentPartion: undefined,
      videoData: {},
      progress: 60,
      videoListFlag: false,
      video_m3u8url: '',
      secound: 5,
      caption: 's',
      showNextState: false,
      showQuitState: false,
      showJumpState: false,
      studentCourseId: this.$route.params.studentCourseId,
      challengeStatus: 'ONGOING',
      nowDate: Date.now(),
      quizBg: '',
      shareToken: this.$route.query.token,
      isWideScreen: false,
      resizeTimer: null,
      showSider: false,
      challengeScore: 0,
      buttonAudioPlay: false,
      showAnimate: false,
      showEndAnimate: false,
      showPlayBtn: false,
      autoplay: null,
      showVideoNext: false,
      workType: ['PREVIEW', 'LEARNING_ACHIEVED', 'AFTER_ACHIEVED', 'NEXT_PREVIEW'],
      isPortrait: false,
      showH5Menu: false,
      preview: this.previewProps ? this.previewProps : this.$route.query.preview,
      isVideoPlaying: false,
      isFullScreenMode: false,
      toolActive: false,
      closeTool: false,
      isElectronWeb: navigator.userAgent.indexOf('Electron') !== -1,
      showUploadHint: false,
      showScratch: true,
      screenWidth: 0,
      screenHeight: 0,
      resizeFun: null
    }
  },
  watch: {
    loginStatus: {
      handler (val) {
        console.log('loginStatus', val)
      }
    },
    $route () {
      this.$router.go(0)
    },
    isFullScreenMode (val) {
      const videoBox = document.getElementById('play-box')
      if (val) {
        const userAgent = navigator.userAgent.toLowerCase()
        if (userAgent.includes('win')) {
          const dpi = (window.devicePixelRatio || 1)
          if (dpi > 1.3) {
            videoBox.style.cssText = videoBox.style.cssText + `width: ${this.screenWidth}px;height: ${this.screenHeight}px;`
          }
        }
      } else {
        videoBox.style.cssText = videoBox.style.cssText + `width: 100vw;height: 100vh;`
      }
    }
  },
  async created () {
    this.studentCourseId = +this.preview === 1 ? 0 : this.studentCourseId
    if (this.shareToken) {
      await this.$store.dispatch('saveToken', this.shareToken)
      await this.$store.dispatch('GetUserInfo')
    }
    this._getAiCourseUnitSectionList()
    this.getQuizBg()
    this._getSecondsConfig()
  },
  mounted () {
    document.addEventListener('fullscreenchange', this.fullscreenchange)
    if (!this.isFullScreenMode) {
      this.screenHeight = screen.height
      this.screenWidth = screen.width
    }
    this.resize()
    this.resizeFun = debounce(this.resize, 1000)
    window.addEventListener('resize', this.resizeFun)
    // window.addEventListener('resize', this.resize)
    this.requestFullscreen()
    window.addEventListener('beforeunload', () => {
      if (+this.preview !== 1) return
      const previewData = {
        'index': `${this.sectionContext.currentSectionIndex}`,
        'courseId': this.courseId,
        'unitId': this.unitId
      }
      window.localStorage.setItem('previewData', JSON.stringify(previewData))
    })
  },
  beforeDestroy () {
    document.removeEventListener('fullscreenchange', this.fullscreenchange)
    // window.removeEventListener('resize', this.resize)
    window.removeEventListener('resize', this.resizeFun)
  },
  methods: {
    resize () {
      const width = document.documentElement.clientWidth
      const height = document.documentElement.clientHeight
      const videoBox = document.getElementById('play-box')
      let style = ''
      if (width >= height) {
        style += 'transform: rotate(0);'
        style += '-webkit-transform: rotate(0);'
        style += '-webkit-transform-origin: 0 0;'
        style += 'transform-origin: 0 0;'
        if (this.isFullScreenMode) {
          const userAgent = navigator.userAgent.toLowerCase()
          if (userAgent.includes('win')) {
            const dpi = (window.devicePixelRatio || 1)
            if (dpi > 1.3) {
              style += `width: ${this.screenWidth}px;height: ${this.screenHeight}px;`
            }
          }
        } else {
          style += 'width: 100vw; height: 100vh;'
        }
        this.isPortrait = true
      } else {
        style += 'width:' + window.innerHeight + 'px;'
        style += 'height:' + window.innerWidth + 'px;'
        style += '-webkit-transform: rotate(90deg); transform: rotate(90deg);'
        style += '-webkit-transform-origin: ' + window.innerWidth / 2 + 'px ' + window.innerWidth / 2 + 'px;'
        style += 'transform-origin: ' + window.innerWidth / 2 + 'px ' + window.innerWidth / 2 + 'px;'
        this.isPortrait = false
      }
      if (videoBox) {
        videoBox.style.cssText = videoBox.style.cssText + style
      }
    },
    handleResize () {
      const fixedRatio = 16 / 9
      const width = document.documentElement.clientWidth
      const height = document.documentElement.clientHeight
      const ratio = width / height
      if (ratio >= fixedRatio) {
        this.isWideScreen = true
      } else {
        this.isWideScreen = false
      }
    },
    async handlePlayback (dir) {
      this.playButtonAudio()
      if (dir === 'first') {
        await this._getAiCourseUnitSectionList(true)
        this.playback = true
      }
      if (dir === 'prev') {
        this._seekSection(this.sectionContext.currentSectionIndex - 1)
      }
      if (dir === 'next') {
        this._seekSection(this.sectionContext.currentSectionIndex + 1)
      }
    },
    async leftBtnCallback () {
      if (this.showNextState) {
        this.playButtonAudio()
        this._getAiCourseUnitSectionList(true)
        this.showNextState = false
      } else if (this.showJumpState) {
        this.showJumpState = false
      } else {
        if (this.$route.query.close) {
          window.close()
          return
        }
        if (this.sourceProps === 'prepare' || this.$route.query.source === 'prepare') {
          window.parent.postMessage({
            type: 'prepareBack'
          }, '*')
          // this.$emit('prepareBack')
          // this.exitFullscreen()
          return
        }
        const previewIndex = this.previewIndexProps ? this.previewIndexProps : this.$route.query.previewIndex
        const type = this.$route.query.type
        if (type && type === 'chuanEdu') {
          window.location.href = `${process.env.VUE_APP_WEB_URL}#/chuanEdu/detail?couresId=${this.courseId}&title=${this.$route.query.title}`
        } else if (this.preview && !previewIndex) {
          const courseId = this.courseId
          const studentCourseId = this.$route.params.studentCourseId
          const unitId = this.unitId
          window.location.href = `${process.env.VUE_APP_WEB_URL}#/parent/course/report/${courseId}/${studentCourseId}/${unitId}`
        } else {
          const assistantId = getAssistantId()
          this.$router.push({
            path: `/aiPackage/${this.$route.params.courseId}/${this.$route.params.studentCourseId}/${assistantId}${this.$route.query && this.$route.query.token ? `?token=${this.$route.query.token}` : ''}`
          })
        }
        this.exitFullscreen()
      }
    },
    async rightBtnCallback () {
      this.playButtonAudio()
      if (this.showNextState) {
        this.showNextState = false
        this._seekSection(this.sectionContext.currentSectionIndex + 1)
        this._getAiCourseUnitSectionList(true)
      } else if (this.showJumpState) {
        var index = 0
        for (const partion of this.partionList) {
          const challengIndex = partion.data.findIndex(
            item => {
              return item.aicourseUnitSectionUser &&
              item.aicourseUnitSectionUser.result === 'WRONG' &&
              item.stepType === 'CHALLENGE'
            }
          )
          if (challengIndex !== -1) {
            index += partion.data.length
          } else {
            const tempIndex = partion.data.findIndex(
              item => {
                return !item.aicourseUnitSectionUser ||
                  !item.aicourseUnitSectionUser.completed
              }
            )
            if (tempIndex !== -1) {
              index += tempIndex
              break
            } else {
              index += partion.data.length
            }
          }
        }
        this._seekSection(index)
        this.showJumpState = false
      } else {
        this.showQuitState = false
      }
    },
    async _getAicourseUnitUser () {
      var response = await getAicourseUnitUser({
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.unitId
      })
      if (+response.data.code === 200) {
        var data = response.data.data
        if (!data) return
        this.challengeStatus = data.challengeStatus
        this.challengeScore = data.challengeScore
      }
    },
    async getQuizBg () {
      const params = {
        aicourseId: this.courseId,
        studentCourseId: this.studentCourseId
      }
      const response = await getStudentCourseById(params)
      if (+response.data.code === 200) {
        const data = response.data.data
        if (!data) return
        if (data.aicourse) {
          this.quizBg = data.aicourse.backupUrl
        }
      }
    },
    videoListFlagHandler () {
      this.videoListFlag = !this.videoListFlag
    },
    payVideo () {
      if (!this.loginStatus) {
        this.$store.dispatch('setLoginFadeAtion', {
          flag: true,
          type: 1
        })
      } else {
        this.payConfirm = true
      }
    },
    async playvideo (sectionId) {
      console.log('playvideo')
      this.showVideoNext = false
      var response = await getAiCourseUnitSection({
        aicourseUnitSectionId: sectionId
      })
      if (+response.data.code === 200) {
        const data = response.data.data
        const videoUrl = data.mediaFile.url
        if (videoUrl) {
          this.video_m3u8url = videoUrl
          const videoType = this.checkFileType(videoUrl)
          const that = this
          setTimeout(function () {
            if (videoType === 'm3u8') {
              that.sources[0].type = 'application/x-mpegURL'
            } else {
              that.sources[0].type = ''
            }
            that.sources[0].src = videoUrl
            that.sources[0].size = data.mediaFile.size
            that.videoLoad = true
          })
        } else {
          this.sources[0].src = ''
        }
      }
    },
    unlock () {
      this.$toast('课程未解锁，请按计划上课', {
        position: 'center',
        duration: '2000'
      })
    },
    async _getAiCourseUnitSectionList (update) {
      const previewIndex = this.previewIndexProps ? this.previewIndexProps : this.$route.query.previewIndex
      var response = await getAiCourseUnitSectionList({
        aicourseUnitId: this.unitId,
        studentCourseId: this.studentCourseId
      })
      if (+response.data.code === 200) {
        const data = response.data.data
        this.sectionList = data || []
        this.segmentList = []
        this.partionList = []
        // 添加课程报告
        // if (!this.preview) {
        var report = {
          'indexNo': this.sectionList.length + 1,
          'aiSectionType': 'REPORT',
          'stepType': 'REPORT',
          'stepName': '课程报告',
          'step': 999
        }
        report = JSON.parse(JSON.stringify(report))
        this.sectionList.push(report)
        // }
        let partionIndex = 0
        for (var i = 0; i < this.sectionList.length; i++) {
          //  区分环节
          var arrindex = this.segmentList.findIndex(
            (item) => {
              return item.step === this.sectionList[i].step
            })
          if (arrindex === -1) {
            // 不存在盘符数据的添加盘符数据
            this.segmentList.push({
              'step': this.sectionList[i].step,
              'stepName': this.sectionList[i].stepName ||
                            (this.sectionList[i].stepType === 'COMMON' || this.sectionList[i].stepType === 'WORK'
                              ? `环节${this.sectionList[i].step}`
                              : '终极挑战'),
              'data': [this.sectionList[i]]
            })
          } else {
            // 有盘符则往盘符数据中添加
            this.segmentList[arrindex].data.push(this.sectionList[i])
          }
          //  区分分段
          if (this.sectionList[i].aiSectionType !== 'QUESTION') {
            this.partionList.push({
              'index': partionIndex,
              'stepType': this.sectionList[i].stepType,
              'stepName': this.sectionList[i].stepName ||
                          (this.sectionList[i].stepType === 'COMMON' || this.sectionList[i].stepType === 'WORK'
                            ? `环节${this.sectionList[i].step}`
                            : '终极挑战'),
              'data': [this.sectionList[i]]
            })
            partionIndex++
          } else {
            if (i === 0 ||
            this.sectionList[i - 1].aiSectionType !== 'QUESTION' ||
            this.sectionList[i - 1].step !== this.sectionList[i].step) {
              this.partionList.push({
                'index': partionIndex,
                'stepType': this.sectionList[i].stepType,
                'stepName': this.sectionList[i].stepName ||
                            (this.sectionList[i].stepType === 'COMMON' || this.sectionList[i].stepType === 'WORK'
                              ? `环节${this.sectionList[i].step}`
                              : '终极挑战'),
                'data': [this.sectionList[i]]
              })
              partionIndex++
            } else {
              this.partionList[partionIndex - 1].data.push(this.sectionList[i])
            }
          }
        }

        // 进度位置
        let index = 0
        for (const partion of this.partionList) {
          const challengIndex = partion.data.findIndex(
            item => {
              return item.aicourseUnitSectionUser &&
              item.aicourseUnitSectionUser.result === 'WRONG' &&
              item.stepType === 'CHALLENGE'
            }
          )
          if (challengIndex !== -1) {
            index += partion.data.length
          } else {
            const tempIndex = partion.data.findIndex(
              item => {
                return !item.aicourseUnitSectionUser ||
                  !item.aicourseUnitSectionUser.completed
              }
            )
            if (tempIndex !== -1) {
              index += tempIndex
              break
            } else {
              index += partion.data.length
            }
          }
        }

        if (update) {
          this.progressIndex = index
          this.sectionContext.sectionList = this.sectionList
          this.sectionContext.segmentList = this.segmentList
          this.sectionContext.partionList = this.partionList
          this.sectionContext.progress = this.progressIndex
        } else {
          const previewDataStr = window.localStorage.getItem('previewData')
          if (previewIndex && +this.preview === 1) {
            index = +previewIndex
          } else if (!previewIndex && +this.preview === 1 && previewDataStr) {
            const previewData = JSON.parse(previewDataStr)
            if (+previewData.courseId === +this.courseId && +previewData.unitId === +this.unitId) {
              index = +previewData.index
            }
          }
          this.progressIndex = index
          if (index === 0) {
            this.showPlayBtn = true
          } else {
            this.autoplay = true
          }
          this.currentSectionIndex = this.progressIndex
          this.currentSection = this.sectionList[this.currentSectionIndex]
          this.currentSegmentIndex = this.segmentList.findIndex(
            item => {
              return item.step === this.sectionList[this.currentSectionIndex].step
            }
          )
          this.currentSegment = this.segmentList[this.currentSegmentIndex]

          const finished = false
          for (var item of this.partionList) {
            for (var section of item.data) {
              if (this.currentSection.indexNo === section.indexNo &&
                  this.currentSection.step === section.step
              ) {
                this.currentPartionIndex = item.index
                this.finished = true
                break
              }
            }
            if (finished) break
          }
          this.currentPartion = this.partionList[this.currentPartionIndex]
          this.sectionContext = {
            'sectionList': this.sectionList,
            'segmentList': this.segmentList,
            'partionList': this.partionList,
            'currentSection': this.currentSection,
            'currentSegment': this.currentSegment,
            'currentPartion': this.currentPartion,
            'currentSectionIndex': this.currentSectionIndex,
            'currentSegmentIndex': this.currentSegmentIndex,
            'currentPartionIndex': this.currentPartionIndex,
            'progress': this.currentSectionIndex
          }
          if (this.sectionContext.currentSection.aiSectionType === 'VIDEO') {
            this.playvideo(this.sectionContext.currentSection.id)
          }
          if ((this.currentSection.aiSectionType === 'QUESTION' ||
              this.currentSection.aiSectionType === 'THINK') &&
              !this.preview &&
              !this.isMobile &&
              !this.isElectronWeb
          ) {
            this.showUploadHint = true
          } else {
            this.showUploadHint = false
          }
        }
      }
    },
    // 跳转片段
    _seekSection (index) {
      if (index < 0 || index > this.sectionContext.sectionList.length - 1) return
      if (index === this.sectionContext.currentSectionIndex) {
        console.log('禁止当前片段重复跳转')
        return
      }
      // if (index === this.sectionContext.sectionList.length - 1) {
      //   const notFinishedIndex = this.sectionContext.sectionList.findIndex(
      //     item => {
      //       return !item.aicourseUnitSectionUser || !item.aicourseUnitSectionUser.completed
      //     }
      //   )

      //   if (!this.preview && notFinishedIndex !== index) {
      //     this.showJumpState = true
      //     return
      //   }
      // }
      const currentSectionIndex = index
      const currentSection = this.sectionContext.sectionList[index]
      const currentSegmentIndex = this.sectionContext.segmentList.findIndex(
        item => {
          return item.step === this.sectionContext.sectionList[currentSectionIndex].step
        }
      )
      const currentSegment = this.sectionContext.segmentList[currentSegmentIndex]
      let currentPartionIndex = 0
      const finished = false
      for (var item of this.partionList) {
        for (var section of item.data) {
          if (currentSection.indexNo === section.indexNo &&
              currentSection.step === section.step
          ) {
            currentPartionIndex = item.index
            this.finished = true
            break
          }
        }
        if (finished) break
      }
      const currentPartion = this.sectionContext.partionList[currentPartionIndex]

      this.sectionContext.currentSectionIndex = currentSectionIndex
      this.sectionContext.currentSection = currentSection
      this.sectionContext.currentSegmentIndex = currentSegmentIndex
      this.sectionContext.currentSegment = currentSegment
      this.sectionContext.currentPartionIndex = currentPartionIndex
      this.sectionContext.currentPartion = currentPartion

      console.log(this.sectionContext.currentSection)
      if ((currentSection.aiSectionType === 'QUESTION' ||
              currentSection.aiSectionType === 'THINK') &&
              !this.preview &&
              !this.isMobile &&
              !this.isElectronWeb
      ) {
        this.showUploadHint = true
      } else {
        this.showUploadHint = false
      }
      // 重置工具条
      this.toolActive = false
      this.$refs.tools && this.$refs.tools.closeDraw()
      this.$refs.draw && this.$refs.draw.clearDraw()
      this.closeTool = true
      setTimeout(() => {
        this.closeTool = false
      }, 1000)

      if (this.sectionContext.currentSection.aiSectionType === 'VIDEO') {
        // 如果视频组件已存在，需要延迟跳转，否则会把剩余时间带到新视频去
        if (this.$refs.bingoVideo) {
          this.$refs.bingoVideo.pause()
          setTimeout(() => {
            this.playvideo(this.sectionContext.currentSection.id)
          }, 100)
        } else {
          this.playvideo(this.sectionContext.currentSection.id)
        }
      }
      this.showScratch = false
      this.$nextTick(() => {
        this.showScratch = true
      })
    },
    async _getAiCourseUnitSection (sectionId) {
      if (this.sectionInfoMap[sectionId] != null) return
      var response = await getAiCourseUnitSection({
        aicourseUnitSectionId: sectionId,
        studentCourseId: this.studentCourseId
      })
      if (+response.data.code === 200) {
        this.sectionInfoMap[sectionId] = response.data.data
        console.log(this.sectionInfoMap)
      }
    },
    // 获取答题时间
    async _getSecondsConfig () {
      var response = await getSecondsConfig()
      if (+response.data.code === 200) {
        this.caption = response.data.caption
        this.secound = response.data.keyValue
      }
    },
    checkFileType (fileUrl) {
      if (fileUrl.indexOf('?') !== -1) {
        var mediaUrl = fileUrl.split('?')[0]
        if (mediaUrl.lastIndexOf('.') !== -1) {
          return mediaUrl.substr(
            mediaUrl.lastIndexOf('.') + 1,
            mediaUrl.length
          )
        }
      } else {
        fileUrl = fileUrl
          .substring(fileUrl.lastIndexOf('/') + 1, fileUrl.length)
          .split('?')[0]
          .split('#')[0]
        if (fileUrl.lastIndexOf('.') > -1) {
          return fileUrl
            .substr(fileUrl.lastIndexOf('.') + 1, fileUrl.length)
            .toLowerCase()
        }
      }
    },
    onPlayerEnd () {
      // const currentSection = this.sectionContext.currentSection
      // const nextSection = this.sectionContext.sectionList[this.sectionContext.currentSectionIndex + 1]
      // if (!currentSection.aicourseUnitSectionUser ||
      // !currentSection.aicourseUnitSectionUser.completed) {
      //   this._updateAicourseUnitUserProgress(this.sectionContext.currentSection.id)
      // }
      // if (currentSection.step !== nextSection.step) {
      //   this.showNextState = true
      // } else {

      // const videoPlayer = document.querySelector('.video-player')
      // videoPlayer.classList.add('animate__animated', 'animate__fadeOut')
      // this.showAnimate = true
      // videoPlayer.addEventListener('animationend', () => {
      this._getAiCourseUnitSectionList(true)
      this.handlePlayback('next')
      //   setTimeout(() => { this.showAnimate = false }, 2000)
      // })

      // }
    },
    onPlayerTimeUpdate (remainingTime) {
      const media = this.sources[0]
      const overTime = media.duration > 60 ? 10 : 5
      if (remainingTime <= overTime && !this.showVideoNext) {
        this.showVideoNext = true
        const currentSection = this.sectionContext.currentSection
        if (!currentSection.aicourseUnitSectionUser || !currentSection.aicourseUnitSectionUser.completed) {
          this._updateAicourseUnitUserProgress(this.sectionContext.currentSection.id)
        }
      }
    },
    async _updateProgress () {
      console.log('完成章节')
      const currentSection = this.sectionContext.currentSection
      if (!currentSection.aicourseUnitSectionUser || !currentSection.aicourseUnitSectionUser.completed) {
        console.log('上报进度')
        await this._updateAicourseUnitUserProgress(currentSection.id)
      }
      this._getAiCourseUnitSectionList(true)
    },
    async _updateAicourseUnitUserProgress (sectionId) {
      const params = {
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.unitId,
        aicourseUnitSectionId: sectionId,
        token: this.shareToken || null
      }
      await updateAicourseUnitUserProgress(params)
        .then(response => {
          console.log(response)
        })
        .catch(err => {
          console.log(err)
        })
    },
    showQuit () {
      this.playButtonAudio()
      this.showQuitState = true
    },
    // 播放按键音乐
    async playButtonAudio () {
      const audio = this.$refs.buttonAudio
      audio.volume = 0.1
      if (!audio.paused) {
        audio.load()
      }
      audio.play()
    },
    // 播放挑战答题正确音乐
    async playRightAudio () {
      const audio = this.$refs.rightAudio
      audio.volume = 0.1
      if (!audio.paused) {
        audio.load()
      }
      audio.play()
    },
    startLesson () {
      this.showPlayBtn = false
      this.autoplay = true
      this.requestFullscreen()
      if (this.sectionContext.currentSection.aiSectionType === 'VIDEO') {
        this.$refs && this.$refs.bingoVideo && this.$refs.bingoVideo.play()
      }
    },
    fullscreenchange () {
      // 监听全屏事件
      if (this.isFullScreen()) {
        this.isFullScreenMode = true
      } else {
        this.isFullScreenMode = false
      }
    },
    handleFullScreen () {
      console.log('isFullScreen', this.isFullScreen())
      if (this.isFullScreen()) {
        this.isFullScreenMode = false
        this.exitFullscreen()
      } else {
        this.isFullScreenMode = true
        this.requestFullscreen()
      }
    },
    // 是否全屏
    isFullScreen () {
      return document.fullscreenElement || document.msFullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || false
    },
    // 全屏
    requestFullscreen (el) {
      const isFullScreen = this.isFullScreen()
      const ele = el || document.documentElement
      const rfs = ele.requestFullscreen || ele.webkitRequestFullscreen || ele.mozRequestFullScreen || ele.msRequestFullscreen

      // 如果全屏，返回
      if (isFullScreen) return

      if (rfs) {
        rfs.call(ele)
      } else if (typeof window.ActiveXObject !== 'undefined') {
        const wscript = new window.ActiveXObject('WScript.Shell')
        if (wscript) {
          wscript.SendKeys('{F11}')
        }
      }
    },
    // 退出全屏
    exitFullscreen () {
      const isFullScreen = this.isFullScreen()
      const ele = document
      const efs = ele.exitFullscreen || ele.webkitExitFullscreen || ele.mozCancelFullScreen

      // 如果不是全屏，返回
      if (!isFullScreen) return

      if (efs) {
        efs.call(ele)
      } else if (typeof window.ActiveXObject !== 'undefined') {
        const wscript = new window.ActiveXObject('WScript.Shell')
        if (wscript) {
          wscript.SendKeys('{F11}')
        }
      }
    },
    //  回到正确
    answerRight () {
      this.sectionContext.currentSection.aicourseUnitSectionUser = {
        'completed': true,
        'result': 'CORRECT'
      }
      this._getAiCourseUnitSectionList(true)
    },
    //  回答错误
    answerWrong () {
      this.sectionContext.currentSection.aicourseUnitSectionUser = {
        'completed': true,
        'result': 'WRONG'
      }
      this._getAiCourseUnitSectionList(true)
    },
    openH5Menu () {
      this.$refs.navigation.openH5Menu()
    },
    onPlayerPlay () {
      this.isVideoPlaying = true
      this.showVideoNext = false
    },
    onPlayerPause () {
      this.isVideoPlaying = false
      this.showVideoNext = true
    },
    handleActive (val) {
      this.toolActive = val
    },
    videoPrev (index) {
      if (index >= 0) {
        this._seekSection(index)
      } else {
        this.showQuit()
      }
    },

    // 防录屏相关方法
    handleRecordingDetected(data) {
      console.log('检测到录屏行为:', data)

      // 可以在这里添加额外的处理逻辑
      // 比如记录日志、发送统计等
      this.$toast('检测到录屏行为，视频已暂停保护', {
        position: 'center',
        duration: '3000'
      })
    },

    handleAntiRecordingPause() {
      // 确保视频暂停
      if (this.$refs.bingoVideo) {
        this.$refs.bingoVideo.pause()
      }
      this.isVideoPlaying = false
    },

    handleRecordingBlockRemoved() {
      console.log('防录屏保护已解除')
      // 可以在这里添加恢复后的处理逻辑
    }
  },
  computed: {
    ...mapGetters(['loginStatus', 'token']),
    isMobile () {
      const ua = navigator.userAgent.toLowerCase()
      const t1 = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(
        ua
      )
      if (t1) document.title = '缤果AI课'
      // const t2 = !ua.match("iphone") && navigator.maxTouchPoints > 1;
      return t1
    },
    isVideoPlay () {
      if (this.sectionContext.currentSection.aiSectionType === 'VIDEO') {
        if (this.isVideoPlaying) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    }
  },
  components: {
    // videoPlayer,
    QuizBox,
    Dialog,
    // Progress,
    Report,
    TranslateAnimate,
    BingoVideo,
    Thingking,
    Tools,
    Draw,
    Navigation,
    VideoNext,
    Work,
    Game,
    Scratch,
    VideoAntiRecording
  }
}
</script>
<style lang="scss">
@function rem($px) {
  @return $px * 100vh / 650;
}
@import "@/styles/mixin";
@import "@/styles/variables";
.ai-play-lesson-center {
  background: #000000;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  //transform: scale(0.5);

  .play-header {
    position: relative;
    height: 100%;
    width: 100%;

    .play-inner {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .video-player-box {
      // min-width: 1180px;
      // min-height: calc(1180px * 9 / 16);
      width: 100%;
      height: 100%;
      margin: 0;
      position: relative;
      overflow: hidden;

      &.full-width-screen {
        width: 100vw;
        height: calc(100vw * 9 / 16);
      }

      &.full-height-screen {
        height: 100vh;
        width: calc(100vh * 16 / 9);
      }

      .video-player {
        height: 100%;
        width: 100%;
      }
    }

    .sider-toggle {
      position: absolute;
      top: 50%;
      right: 0;
      height: 80px;
      width: 80px;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.45);
      transform: translate(50%, -50%);
      cursor: pointer;
      z-index: 3000;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 4px;
        height: 100%;
        width: 50%;
        // background: url('../../assets/images/ai/ai-sider-a-left.png') center center no-repeat;
        // background-size: 16px 24px;
        background: url('../../assets/images/ai/ai-sider-a-left-2.png') center center no-repeat;
        background-size: contain;
      }

      .open-tip {
        position: absolute;
        display: none;
        top: 50%;
        left: 0;
        width: 130px;
        height: 36px;
        background: #FFFFFF;
        border-radius: 10px;
        font-size: 12px;
        font-weight: bold;
        color: #232323;
        line-height: 36px;
        text-align: center;
        transform: translate(-110%, -50%);
        filter: drop-shadow(0px 0px 5px rgba(0, 0, 0, 0.09));

        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          height: 10px;
          width: 16px;
          background: #FFFFFF;
          transform: translate(40%, -50%);
          clip-path: ellipse(50% 40% at 50% 50%);
          z-index: -1;
        }
      }

      &:hover {
        .open-tip {
          display: block;
        }
      }
    }

    .bg-pre {
      cursor: pointer;
      img {
        position: absolute;
        z-index: 2000;
        width: calc(100vw * 30 / 965);
        height: calc(100vw * 40 / 965);
        object-fit: contain;
        left: calc(100vw * 32 / 965);
        z-index: 9999;
      }
    }

    .bg-pre-white {
      width: calc(100vw * 42 / 965);
      height: calc(100vw * 42 / 965);
      background: rgba(0, 0, 0, 0.46);
      position: absolute;
      z-index: 9999;
      border-radius: 100px;
      left: calc(100vw * 27 / 965);
      top: calc(100vw * 14 / 965);
      cursor: pointer;
      img {
        width: calc(100vw * 12 / 965);
        height: calc(100vw * 22 / 965);
        object-fit: contain;
        margin-top: calc(100vw * 10 / 965);
        margin-left: calc(100vw * 13 / 965);
      }
    }

    .ani-overlay {
      position: fixed;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5); // 4.19 14:50 万芬叫改到0.5，说的太黑了
      // transform: translate(-50%, -50%);
      z-index: 9998;
    }

    .sider2 {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: calc(100vh * 384 / 650);
      filter: drop-shadow(-2px 0px 8px rgba(0, 0, 0, 0.1));
      transform: translate(150%, 0);
      transition: all 0.5s cubic-bezier(0.075, 0.82, 0.165, 1);
      z-index: 9998;

      .bg {
        position: absolute;
        height: 100%;
        width: calc(100vh * 384 / 650);
        object-fit: contain;
      }

      &.show {
        transform: translate(0, 0);
      }

      .close-sider {
        position: absolute;
        left: 0;
        top: 50%;
        height: rem(52);
        width: rem(38);
        background: url('../../assets/images/ai/ai-sider-a-right-2.png') center center no-repeat;
        background-size: contain;
        cursor: pointer;
        transform: translate(0, -50%);
        z-index: 10;
      }

      .unit-info {
        width: rem(253);
        height: rem(71);
        margin: rem(14) rem(28) 0 auto;
        background: url('../../assets/images/ai/bg-title.png') center center no-repeat;
        background-size: cover;
        position: relative;

        .main-title {
          height: rem(47);
          font-size: rem(18);
          font-weight: 500;
          color: #FFFFFF;
          padding-top: rem(31);
          word-break: break-all;
          text-align: center;
          @include ellipsisMore(2)
        }

        .decoration {
          position: absolute;
          right: rem(23);
          top: rem(5);
          font-size: rem(12);
          font-weight: 500;
          color: #9AF1FC;
          line-height: rem(17);
          text-shadow: 0px 0px 6px rgba(39, 135, 255, 0.84);
        }
      }

      .unit-progress {
        height: calc(100% - 124 * 100vh / 650);
        // padding: 6px 10px 0 16px;

        .el-scrollbar {
          height: 100%;
          .el-scrollbar__wrap {
            overflow-x: hidden;
          }
        }

      }
    }
  }
  .lesson-chapter-desc {
    width: 1130px;
    margin: 0 auto 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 99999;
    padding: 25px;
    h1 {
      font-size: 30px;
      color: #484848;
      margin-bottom: 20px;
    }
    .desc-text {
      padding: 10px;
      color: #484848;
      border: 1px dashed rgba($color: #161823, $alpha: 0.72);
      border-radius: 3px;
      min-height: 80px;
      @include wordbreak;
      white-space: pre-line;
    }
  }

  .play-btn {
    width: vh2(150);
    height: vh2(50);
    line-height: vh2(50);
    font-size: vh2(20);
    position: fixed;
    z-index: 9998;
    transform: translate(-50%,-50%);
    left: 50%;
    top: 50%;
    cursor: pointer;
    background: url('~assets/ai-image/ai/bg-play-btn.png') center center no-repeat;
    background-size: contain;
    text-align: center;
    font-weight: 500;
  }

  .h5-arrow-left,
  .h5-menu {
    position: absolute;
    z-index: 2000;
    width: vh2(52);
    height: vh2(52);
    top: vh2(13);

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .h5-arrow-left {
    left: vh2(25);
  }

  .h5-menu {
    right: vh2(25);
    cursor: pointer;
  }
}
</style>

<style lang="scss" scoped>
.upload-bg {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9999;
}
.upload-dialog {
  position: absolute;
  width: vh2(485);
  height: vh2(204);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 31px;
  border: 1px solid #A4A4A4;
  background: #FFF;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 vh2(20);
  gap: vh2(20);

  .t1 {
    color: #000;
    text-align: center;
    font-size: vh2(28);
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  .t2 {
    color: #F2994A;
    text-align: center;
    font-size: vh2(28);
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-decoration-line: underline;
    cursor: pointer;
  }
}
</style>
