<template>
  <div class="app-center live-lesson-center">
    <div class="live-header">
      <img v-if="coverUrl" v-lazy="coverUrl" class="live-cover" />
      <div class="live-layer"></div>
      <div class="live-inner">
        <img v-lazy="coverUrl" class="live-video-img" />
        <div class="live-info">
          <h1>{{ title }}</h1>
          <p v-if="subType">分类：{{ subType }}</p>
          <div class="lesson-desc">
            {{ subTitle }}
          </div>
          <div class="converted-lesson-progress">
            <div class="left">
              课程进度:
              <div class="progress-box">
                <div
                  class="progress-box-inner"
                  :style="`width:${(finishedProgress / total) * 100}%`"
                ></div>
              </div>
              <span class="red">{{ finishedProgress }}</span>
              <span>/{{ total }}</span>
            </div>
            <div v-if="finishedProgress<total" class="btn-play" @click="gotoPlay(finishedProgress)">
              <template v-if="finishedProgress==0">
                开始上课
              </template>
              <template v-else>
                继续上课
              </template>
            </div>
            <div v-else class="btn-play canScheduleLesson">
              已完结
            </div>
          </div>
        </div>
        <!-- <div class="live-setting">
          <div class="live-collect-ed">
            <i></i>
          </div>
        </div> -->
      </div>
    </div>
    <div class="live-list-box">
      <nav class="live-list-nav">
        <li
          :class="liveListNav === 1 && 'active'"
          @click="changeliveListNav(1)"
        >
          课程目录({{ videoGoodsUnitListLen }}节)
        </li>
        <li
          :class="liveListNav === 2 && 'active'"
          @click="changeliveListNav(2)"
        >
          课程详情
        </li>
      </nav>
      <div class="live-list-inner">
        <div :class="liveListNav === 2 && 'fade'" class="live-list-inner-1">
          <div
            v-if="description"
            class="description-text"
            v-html="description"
          ></div>
        </div>
        <div :class="liveListNav === 1 && 'fade'" class="live-list-inner-2">
          <div class="lesson-chapter-list">
            <ul>
              <li v-for="(item, index) in videoGoodsUnitList" :key="index">
                <div class="chapter-cover">
                  <img v-lazy="item.coverUrl" />
                  <div v-if="item.lock" class="locked">
                    <i></i>
                  </div>
                </div>
                <div class="chapter-desc">
                  <h1>第 {{ (index + 1).toString() }} 讲：{{ item.title }}</h1>
                  <p v-if="item.aicourseUnitUser">
                    <span
                      v-if="item.aicourseUnitUser.startTime"
                      class="chapter-desc-time"
                    >上课：{{
                      $moment.formatYYYYMMDD(item.aicourseUnitUser.startTime)
                    }}</span>
                  </p>
                  <p v-if="item.subTitle" v-html="item.subTitle"></p>
                  <div class="chapter-setting">
                    <!-- <template v-if="!item.aicourseUnitUser || !item.aicourseUnitUser.startTime || item.aicourseUnitUser.startTime < nowDate"> -->
                    <div
                      v-if="item.aicourseUnitUser && item.aicourseUnitUser.complete && item.aicourseUnitUser.total && item.aicourseUnitUser.complete >= item.aicourseUnitUser.total"
                      class="btn-play"
                      @click="gotoPlay(index)"
                    >
                      <i></i>
                      <span>回看</span>
                    </div>
                    <div
                      v-else
                      class="btn-play start"
                      @click="gotoPlay(index)"
                    >
                      <span>{{ !item.aicourseUnitUser || (item.aicourseUnitUser && !item.aicourseUnitUser.complete) ? '开始上课' : '继续上课' }}</span>
                    </div>
                    <!-- </template> -->
                    <!-- <template v-else>
                      <div class="btn-play unlock" @click="unlock()">
                        <i></i>未解锁
                      </div>
                    </template> -->
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pay-confirm-box"
      :class="payConfirm && 'fade'"
      @touchmove.prevent
    >
      <div class="confirm-inner">
        <div class="confirm-title">
          本视频为付费视频<br />如想订阅视频请联系咨询
        </div>
        <div class="us-tel">电话：************</div>
        <!-- <div class="us-qrcode">
          <img :src="cover" alt="" />
        </div> -->
        <!-- <div class="us-btn" @click="openOnline">在线联系</div> -->
        <div class="close-pay-confirm-box" @click="closePayConfirm"></div>
      </div>
    </div>
  </div>
</template>
<script>
import cover from '@/assets/images/<EMAIL>'
import { getStudentCourseById, getAiCourseUnitList } from '@/api/aicourse'
import { mapGetters } from 'vuex'

export default {
  data () {
    return {
      cover: cover,
      courseId: this.$route.params.courseId,
      coverUrl: '',
      title: '',
      subTitle: '',
      subType: '',
      finishedProgress: 0,
      total: 0,
      description: '',
      payConfirm: false,
      liveListNav: 1,
      videoGoodsUnitList: [],
      videoGoodsUnitListLen: 0,
      studentCourseId: this.$route.params.studentCourseId,
      nowDate: Date.now()
    }
  },
  watch: {
    loginStatus: {
      handler () {
        if (this.loginStatus) {
          this._getAiCourse()
        } else {
          this.$router.push({
            path: `/home`
          })
        }
      }
    }
  },
  created () {
    if (this.loginStatus) {
      this._getAiCourse()
    } else {
      this.$router.push({
        path: `/home`
      })
    }
  },
  methods: {
    changeliveListNav (type) {
      if (this.liveListNav !== +type) {
        this.liveListNav = +type
      }
    },
    _getAiCourse () {
      const param = {
        aicourseId: this.courseId,
        studentCourseId: this.studentCourseId
      }
      getStudentCourseById(param)
        .then(response => {
          if (+response.data.code === 200) {
            const reslut = response.data.data
            if (reslut.aicourse) {
              this.coverUrl = reslut.aicourse.coverUrl
              this.title = reslut.aicourse.title
              this.subTitle = reslut.aicourse.subTitle
              this.description = reslut.aicourse.description
            }
            this.finishedProgress = reslut.finishedProgress
            this.total = reslut.total
          }
        })
        .catch(err => {
          this.$toast(err, {
            position: 'center',
            duration: '2000'
          })
        })
      getAiCourseUnitList(param)
        .then(response => {
          if (+response.data.code === 200) {
            const data = response.data.data
            this.videoGoodsUnitList = data
            this.videoGoodsUnitListLen = data.length
          }
        })
        .catch(err => {
          this.$toast(err, {
            position: 'center',
            duration: '2000'
          })
        })
    },
    payVideo () {
      if (!this.loginStatus) {
        this.$store.dispatch('setLoginFadeAtion', {
          flag: true,
          type: 1
        })
      } else {
        this.payConfirm = true
      }
    },
    unlock () {
      this.$toast('课程未解锁，请按计划上课', {
        position: 'center',
        duration: '2000'
      })
    },
    closePayConfirm () {
      this.payConfirm = false
    },
    openOnline () {
      const openOnlineBtn = document.getElementById('nb_invite_ok')
      if (openOnlineBtn) {
        openOnlineBtn.click()
      }
    },
    gotoPlay (index) {
      const { href } = this.$router.resolve({
        path: `/ai/${this.courseId}/${this.studentCourseId}/${index}`
      })
      setTimeout(function () {
        window.open(href, '_blank')
      })
    }
  },
  computed: {
    ...mapGetters(['loginStatus', 'token'])
  },
  components: {}
}
</script>
<style lang="scss">
@import "@/styles/mixin";
@import "@/styles/variables";
.live-lesson-center {
  background: #fbfbfb;
  .live-header {
    position: relative;
    min-height: 360px;
    width: 100%;
    top: 0;
    left: 0;
    right: 0;
    overflow: hidden;
    z-index: 10;
    padding-bottom: 50px;
    .live-cover {
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: blur(10px);
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
    }
    .live-layer {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1;
    }
    .live-inner {
      position: relative;
      z-index: 9;
      width: 780px;
      margin: 0 auto;
      padding: 70px 50px 70px 350px;
      .live-video-img {
        position: absolute;
        left: 0;
        top: 0;
        width: 304px;
        height: 189px;
        border-radius: 10px;
        top: 70px;
      }
      .live-info {
        position: relative;
        h1 {
          font-size: 32px;
          color: #fff;
          margin-bottom: 15px;
        }
        p {
          font-size: 16px;
          color: #fff;
          margin-bottom: 10px;
        }
        .lesson-desc {
          font-size: 14px;
          color: #fff;
          margin-bottom: 20px;
          @include ellipsisMore(4);
        }
        .converted-lesson-progress {
          color: #fff;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .left {
            display: flex;
            align-items: center;
          }
          .progress-box {
            width: 290px;
            height: 8px;
            background: rgba(255, 137, 20, 0.1);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            margin: 0px 10px;
            color: #fff;
            .progress-box-inner {
              height: 8px;
              background: linear-gradient(90deg, #ffba4e 0%, #ff7a17 100%);
              border-radius: 4px;
              position: absolute;
              left: 0;
              top: 0;
            }
          }
          span {
            &.red {
              color: $pcbc;
            }
            font-size: 12px;
            color: #fff;
          }
          .btn-play {
            display: flex;
            align-items: center;
            justify-content: center;
            color: $pcbc;
            font-size: 14px;
            width: 160px;
            height: 44px;
            background: #fff;
            border-radius: 23px;
            cursor: pointer;
            &.canScheduleLesson {
              background: #efefef;
              color: #a1a1a1;
              cursor: not-allowed;
            }
            i {
              width: 16px;
              height: 16px;
              @include bg-image("icon-button-play");
              background-size: cover;
              margin-right: 2px;
            }
          }
        }
      }
      .live-setting {
        position: absolute;
        right: 0;
        top: 70px;
        .live-collect {
          i {
            display: inline-block;
            width: 32px;
            height: 30px;
            @include bg-image("icon-collect");
            background-size: cover;
          }
        }
        .live-collect-ed {
          i {
            display: inline-block;
            width: 32px;
            height: 30px;
            @include bg-image("icon-collect-ed");
            background-size: cover;
          }
        }
      }
    }
  }
  .live-list-box {
    width: 1180px;
    padding: 0px 0px 0px;
    margin: 0 auto 20px;
    position: relative;
    z-index: 11;
    min-height: 510px;
    top: -80px;
    .live-list-nav {
      width: 100%;
      height: 40px;
      background: #fff;
      li {
        min-width: 120px;
        padding: 0px 10px;
        line-height: 38px;
        text-align: center;
        color: #484848;
        font-size: 16px;
        border-bottom: 2px solid transparent;
        display: inline-block;
        cursor: pointer;
        &.active {
          color: $pcbc;
          border-bottom: 2px solid $pcbc;
        }
      }
    }
    .live-list-inner-1 {
      min-height: 510px;
      background: #fff;
      display: none;
      padding: 20px;
      &.fade {
        display: block;
      }
      // .description-text {
      // }
    }
    .live-list-inner-2 {
      min-height: 510px;
      background: #fff;
      display: none;
      &.fade {
        display: block;
      }
      .lesson-chapter-list {
        width: 1180px;
        margin: 0 auto 20px;
        position: relative;
        z-index: 9;
        ul {
          padding: 25px 25px 25px;
          background: #fff;
          li {
            &:nth-last-child(1) {
              margin-bottom: 0;
            }
            margin-bottom: 25px;
            position: relative;
            padding-left: 180px;
            .chapter-cover {
              width: 165px;
              position: absolute;
              left: 0;
              top: 0;
              height: 120px;
              border-radius: 5px;
              overflow: hidden;
              .locked {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                align-items: center;
                justify-content: center;
                i {
                  width: 22px;
                  height: 24px;
                  @include bg-image("icon-lock");
                  background-size: cover;
                }
              }
              .watched {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                align-items: center;
                justify-content: center;
              }
              img {
                width: 100%;
                height: 120px;
                object-fit: cover;
              }
            }
            .chapter-desc {
              min-height: 120px;
              padding-right: 180px;
              h1 {
                color: #161823;
                font-size: 16px;
                margin-bottom: 10px;
              }
              p {
                color: #a1a1a1;
                font-size: 14px;
                margin-bottom: 20px;
                @include wordbreak;
                white-space: pre-line;
              }
              .chapter-desc-time {
                color: #484848;
              }
              .chapter-setting {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                .btn-play {
                  width: 138px;
                  height: 36px;
                  border-radius: 23px;
                  border: 1px solid $pcbc;
                  color: $pcbc;
                  font-size: 14px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;
                  &.start {
                    background: $pcbc;
                    color: #fff;
                    cursor: pointer;
                  }
                  &.unlock {
                    border: 1px solid #484848;
                    color: #484848;
                  }
                  i {
                    width: 16px;
                    height: 16px;
                    @include bg-image("icon-button-play");
                    background-size: cover;
                    margin-right: 2px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.progress-radial {
  display: inline-block;
  margin: 15px;
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 1);
}
.progress-radial b {
  color: #fff;
  position: absolute;
  left: 50%;
  top: 50%;
  width: 80%;
  height: 80%;
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  margin-left: -40%;
  margin-top: -40%;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pay-confirm-box {
  display: none;
  align-items: center;
  justify-content: center;
  background: transparent;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  &.fade {
    display: flex;
  }
  .confirm-inner {
    width: 450px;
    display: flex;
    justify-content: center;
    padding: 45px 0px;
    background: rgba(0, 0, 0, 0.8);
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
    border-radius: 23px;
    flex-direction: column;
    align-items: center;
    position: relative;
    .confirm-title {
      font-size: 16px;
      color: #fff;
      margin-bottom: 20px;
      text-align: center;
    }
    .us-tel {
      font-size: 16px;
      color: #fff;
      margin-bottom: 20px;
    }
    .us-qrcode {
      width: 170px;
      height: 170px;
      margin-bottom: 40px;
      img {
        width: 170px;
        height: 170px;
      }
    }
    .us-btn {
      width: 158px;
      height: 44px;
      background: $pcbc;
      border-radius: 23px;
      color: #fff;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      cursor: pointer;
    }
    .close-pay-confirm-box {
      width: 40px;
      height: 40px;
      @include bg-image("close-pay-confirm-box");
      background-size: cover;
      position: absolute;
      left: 50%;
      margin-left: -20px;
      bottom: -20px;
      z-index: 1;
      cursor: pointer;
    }
  }
}
</style>
