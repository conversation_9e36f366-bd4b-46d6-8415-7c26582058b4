<template>
  <div class="progress align-center">
    <!-- 课前互动 -->
    <div class="icon-wrapper">
      <progress-item
        :list="warmup"
        :step-index="warmup[0].step"
        :playback="playback"
        :current-index="currentIndex"
        :segment-index="segmentIndex"
        :is-report-finish="isReportFinish()"
        :index="warmup[0].step"
        @click="handleClick"
      />
      <div class="progress-deco">
        <img
          class="fit-img"
          :src="decoFinished"
        />
      </div>
    </div>
    <div v-for="(item, index) in progressList" :key="index">
      <div class="icon-wrapper">
        <!-- 环节数目 -->
        <div
          v-if="item[0][0].stepType !== 'CHALLENGE' && item[0][0].stepType !== 'REPORT' && item[0][0].stepType !== 'WARMUP'"
          class="progress-segment"
          :class="stepIndex() === item[0][0].step ? 'progress-segment-select' : 'progress-segment-unselect' "
        >
          <!-- 环节{{ index + 1 }} -->
          {{ item[0][0].stepName || `环节${item[0][0].step}` }}
        </div>
        <progress-item
          v-for="(item2, index2) in item"
          :key="index2"
          :list="item2"
          :step-index="stepIndex()"
          :playback="playback"
          :current-index="currentIndex"
          :segment-index="segmentIndex"
          :is-report-finish="isReportFinish()"
          :index="index2"
          @click="handleClick"
        />
        <div class="progress-deco">
          <img
            class="fit-img"
            :class="{'shadow' : stepIndex() === item[0][0].step }"
            :src="stepIndex() === item[0][0].step
              ? decoOnGoingIcon
              : isFinished(index)"
          />
        </div>
      </div>
    </div>
    <div class="progress-line"></div>
  </div>
</template>

<script>
import ProgressItem from './ProgressItem'
import decoIcon from '@/assets/images/ai/deco.png'
import decoOnGoingIcon from '@/assets/images/ai/deco-ongoing.png'
import decoFinished from '@/assets/images/ai/deco-finished.png'
export default {
  components: { ProgressItem },
  props: {
    currentIndex: Number,
    data: Array,
    playback: Boolean,
    segmentIndex: Number
  },
  data () {
    return {
      progressList: [],
      decoIcon,
      decoOnGoingIcon,
      decoFinished,
      warmup: [{ 'indexNo': 0, 'aiSectionType': 'WARMUP', 'stepType': 'WARMUP', 'step': 0 }]
    }
  },
  watch: {
    data: {
      handler () {
        this.getProgressList2()
      },
      immediate: true
    }
  },
  methods: {
    getProgressList2 () {
      const arr = this.data
      // 调用方法，对象被判断的属性值相同，则在同个数组内
      let newArr = []
      // 单次调用，输出二维数组
      newArr = this.classify(arr, 'step')
      console.log('newArr', newArr)

      const resultArr = []
      var resArr = []
      var tempArr = []
      const reducer = (previousType, currentItem, index, array) => {
        console.log(index, previousType, currentItem, currentItem.step)
        if (currentItem.aiSectionType === 'VIDEO' || currentItem.aiSectionType === 'THINK') {
          if (index !== 0 && tempArr !== []) {
            resArr.push([...tempArr])
            tempArr.splice(0, tempArr.length)
            tempArr = []
          }
          resArr.push([currentItem])
        } else {
          tempArr.push(currentItem)
          if (index === array.length - 1) {
            resArr.push([...tempArr])
            tempArr.splice(0, tempArr.length)
          }
        }
        return currentItem.aiSectionType
      }
      for (var newArrItem of newArr) {
        resArr = []
        tempArr = []
        newArrItem.reduce(reducer, 0)
        resultArr.push([...resArr])
      }
      console.log('resultArr', resultArr)
      this.progressList = resultArr
    },
    classify (arr, key) {
      const kind = [] // 存放属性标识
      const newArr = [] // 返回的数据
      arr.map((item) => {
        // 判断key是否存在，不存在则添加
        if (!kind.includes(item[key])) {
          kind.push(item[key]) // kind添加新标识
          newArr.push([]) // 添加数组
        }
        const index = kind.indexOf(item[key]) // 返回带有标识在kind内的下标，判断加入哪个数组
        newArr[index].push(item) // 将对象存入数组
      })
      return newArr
    },
    handleClick (index) {
      this.$emit('skip', index)
    },
    isFinished (index) {
      // var finishedLength = 0
      // var stepLength = 0
      var item = this.progressList[index]
      var targetItem = item[item.length - 1]
      if (index === this.progressList.length - 1) {
        if (this.isReportFinish()) {
          return decoFinished
        }
        return decoIcon
      } else {
        // for (var item of this.progressList[index]) {
        //   finishedLength += item.filter(
        //     (item) =>
        //     item.aicourseUnitSectionUser &&
        //     item.aicourseUnitSectionUser.completed
        //   ).length
        //   stepLength += item.length
        // }
        // if (finishedLength === stepLength) {
        //   return decoFinished
        // }
        return this.currentIndex > targetItem[targetItem.length - 1].indexNo - 1 ? decoFinished : decoIcon
      }
    },
    stepIndex () {
      return this.data[this.segmentIndex].step
    },
    isReportFinish () {
      return this.currentIndex === this.data.length - 1
    }
  }
}
</script>

<style lang="scss" scoped>
@function rem($px) {
  @return $px * 100vh / 650;
}
  .progress {
    position: relative;
    padding: rem(8) rem(32) 0 rem(95);
  }

  .icon-wrapper {
    position: relative;
    width: rem(257);
    z-index: 1;
    margin-left: auto;
  }

  .progress-segment {
    font-family: PingFangSC-Medium;
    font-size: rem(20);
    height: rem(40);
    line-height: rem(40);
    padding-top: rem(11);
  }

  .progress-segment-unselect {
    color: #89D2EC;
  }

  .progress-segment-select {
    color: #FFF4B1;
  }

  .progress-line {
    position: absolute;
    top: 0;
    right: rem(323);
    margin: rem(34) 0;
    height: calc(100% - 56 * 100vh / 650);
    opacity: 0.38;
    border: 2px solid #AFF7FF;
  }

  .progress-deco {
    position: absolute;
    top: rem(11);
    left: rem(-46);
    width: rem(30);
    height: rem(40);
  }

  .shadow {
    filter: drop-shadow(0 0 11px rgba(248, 229, 118, 0.57));
  }
</style>
