<template>
  <div class="dialog-container-bg flex-col align-center justify-center">
    <div class="dialog-container">
      <!-- <div class="tips">{{ title && (showNextState ? context.currentSegment.stepName? `${context.currentSegment.stepName}学习完成！` : `第${context.currentSegment.step}环节学习完成！` : '确定要离开课堂吗？' )}}</div> -->
      <div class="tips">{{ tips }}</div>
      <div class="row">
        <div class="btn-middle" @click="$emit('leftCallback')">
          <span>{{ createLeftText }}</span>
        </div>
        <div class="btn-middle" @click="$emit('rightCallback')">
          <div v-if="showNextState" style="margin-right:9px">{{ second }}</div>
          <span>{{ createRightText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import bg from '@/assets/images/ai/ai-bg.png'
export default {
  name: 'Dialog',
  props: {
    context: Object,
    showNextState: Boolean,
    isMobile: Boolean,
    title: String,
    leftText: String,
    rightText: String,
    source: String
  },
  data () {
    return {
      second: 3,
      timer: null,
      bg
    }
  },
  computed: {
    isLastSegment: function () {
      if (this.context.currentSegment === null) return true
      return this.context.currentSegment.id === this.context.lastSegment.id
    },
    tips () {
      if (this.title) return this.title
      return this.showNextState
        ? this.context.currentSegment.stepName
          ? `${this.context.currentSegment.stepName}学习完成！`
          : `第${this.context.currentSegment.step}环节学习完成！`
        : this.source === 'prepare' ? '确定要离开预览课堂吗' : '确定要离开课堂吗？'
    },
    createLeftText () {
      if (this.leftText) return this.leftText
      return this.showNextState ? '再学一次' : '离开'
    },
    createRightText () {
      if (this.rightText) return this.rightText
      return this.showNextState ? '下一步' : '留下'
    }
  },
  created () {
    //  当h5添加滑动效果时，div会到最顶层，需要把overflow改成hidden来解决这个问题
    this.hideScrollStyle()
    this.timer = setInterval(() => this.subCount(), 1000)
  },
  destroyed () {
    this.addScrollStyle()
    clearInterval(this.timer)
  },
  methods: {
    subCount () {
      if (!this.showNextState) return
      this.second -= 1
      if (this.second <= 0) {
        setTimeout(() => {
          this.$emit('rightCallback')
        }, 100)
      }
    },
    hideScrollStyle () {
      if (!this.isMobile) return
      const playButton = document.getElementsByClassName('question-container')
      const contentBox = document.getElementsByClassName('content-box')
      const descirbe = document.getElementsByClassName('descirbe')
      const gameVideo = document.getElementsByClassName('game-video-player')
      if (playButton && playButton.length > 0) {
        playButton['0'].style.cssText = 'overflow: hidden'
      }
      if (contentBox && contentBox.length > 0) {
        contentBox['0'].style.cssText = 'overflow: hidden'
      }
      if (descirbe && descirbe.length > 0) {
        descirbe['0'].style.cssText = 'overflow: hidden'
      }
      if (gameVideo && gameVideo.length > 0) {
        const video = document.getElementsByClassName('vjs-tech')
        video['0'].style.cssText = 'display: none'
      }
    },
    addScrollStyle () {
      if (!this.isMobile) return
      const playButton = document.getElementsByClassName('question-container')
      const contentBox = document.getElementsByClassName('content-box')
      const descirbe = document.getElementsByClassName('descirbe')
      const gameVideo = document.getElementsByClassName('game-video-player')
      if (playButton && playButton.length > 0) {
        playButton['0'].style.cssText = 'overflow: auto'
      }
      if (contentBox && contentBox.length > 0) {
        contentBox['0'].style.cssText = 'overflow: auto'
      }
      if (descirbe && descirbe.length > 0) {
        descirbe['0'].style.cssText = 'overflow: auto'
      }
      if (gameVideo && gameVideo.length > 0) {
        const video = document.getElementsByClassName('vjs-tech')
        video['0'].style.cssText = 'display: block'
      }
    }
  }
}
</script>

<style lang="scss">
@import "@/styles/mixin";

.dialog-container-bg {
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgba(9, 8, 8, 0.7);
    z-index: 3001;
    max-height: 100%;
    left: 0;
    top: 0;
}

.dialog-container {
    width: vh2(521);
    height: vh2(307);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #BDBDBD;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.tips {
    font-family: 'PingFang SC';
    font-size: vh2(30);
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    line-height: vh2(42);
    margin-bottom: vh2(110);
    padding-top: vh2(60);
}

.row {
    display: flex;
    justify-content: space-between;
    width: vh2(330);
}

.btn-middle {
    font-family: 'PingFang SC';
    font-weight: 500;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    cursor: pointer;
    width: vh2(150);
    height: vh2(54);
    text-align: center;
    font-size: vh2(18);
    color: #000000;
    background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
    background-size: contain;
}

</style>
