<template>
  <div id="game-box" class="game-box">
    <template v-if="gameData">
      <template v-if="gameData.type === 'QA_PK'">
        <description v-if="step === 1 && videConfig" :text-config="textConfig" :vide-config="videConfig" @changeStep="changeStep" />
        <afoot v-else-if="step === 2" :game-data="gameData" @changeStep="changeStep" @setGameResult="setGameResult" />
        <result v-else :game-result="gameResult" @updateProgress="$emit('updateProgress')" />
      </template>
      <iframe v-else class="game-iframe" :src="gameData.url" frameborder="0"></iframe>
    </template>
  </div>
</template>

<script>
import Description from './description.vue'
import Afoot from './afoot.vue'
import Result from './result.vue'
import { getGameInfo, getConfig } from '@/api/aicourse'
export default {
  components: { Description, Afoot, Result },
  props: {
    gameId: Number,
    sectionId: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      queryGameId: this.$route.query.gameid,
      gameData: undefined,
      textConfig: undefined,
      step: 1,
      videConfig: undefined,
      gameResult: undefined
    }
  },
  watch: {
    gameId: {
      handler (val) {
        if (val) {
          this.changeStep(1)
          this._getGameInfo()
        }
      },
      immediate: true
    }
  },
  created () {
    if (this.queryGameId) {
      this._getGameInfo()
    }
    this._getVideoConfig()
    this._getTextConfig()
  },
  mounted () {
    if (window.location.pathname.indexOf('aigame') > -1) {
      this.resize()
      window.addEventListener('resize', this.resize)
    }
  },
  destroyed () {
    if (window.location.pathname.indexOf('aigame') > -1) {
      window.removeEventListener('resize', this.resize)
    }
  },
  methods: {
    resize () {
      const width = document.documentElement.clientWidth
      const height = document.documentElement.clientHeight
      const gameBox = document.getElementById('game-box')
      let style = ''
      if (width >= height) {
        // 竖屏
        style += 'transform: rotateZ(90deg) translateY(-100%)'
        style += '-webkit-transform: rotateZ(90deg) translateY(-100%)'
        style += '-webkit-transform-origin: 0 0;'
        style += 'transform-origin: 0 0;'
      } else {
        // 横屏
        style += 'width:' + window.innerHeight + 'px;'
        style += 'height:' + window.innerWidth + 'px;'
        style += '-webkit-transform: rotate(90deg); transform: rotate(90deg);'
        style += '-webkit-transform-origin: ' + window.innerWidth / 2 + 'px ' + window.innerWidth / 2 + 'px;'
        style += 'transform-origin: ' + window.innerWidth / 2 + 'px ' + window.innerWidth / 2 + 'px;'
      }
      gameBox.style.cssText = style
    },
    async _getGameInfo () {
      this.gameData = undefined
      var response = await getGameInfo({
        gameId: this.gameId || this.queryGameId,
        sectionId: this.sectionId
      })
      if (+response.data.code === 200 && response.data.data) {
        this.gameData = response.data.data
        if (this.gameData.type !== 'QA_PK') this.$emit('updateProgress')
      }
    },
    async _getVideoConfig () {
      const params = {
        'configType': 'AI_PK_VIDEO'
      }
      var response = await getConfig(params)
      if (+response.data.code === 200 && response.data.data.length > 0) {
        this.videConfig = [
          {
            type: '',
            src: response.data.data[0].keyValue,
            duration: 0
          }
        ]
      } else {
        this.videConfig = []
      }
    },
    async _getTextConfig () {
      const params = {
        'configType': 'AI_PK_TEXT'
      }
      var response = await getConfig(params)
      if (+response.data.code === 200 && response.data.data.length > 0) {
        this.textConfig = response.data.data[0].keyValue
      }
    },
    changeStep (step) {
      this.step = step
      this.$emit('changeStep', step)
    },
    setGameResult (gameResult) {
      this.gameResult = gameResult
    }
  }
}
</script>

<style lang="scss" scoped>
.game-box {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid #FFFFFF;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.3);
}

.game-iframe {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  // padding-top: vh2(60);
  // padding-right: vh2(10);
}
</style>
