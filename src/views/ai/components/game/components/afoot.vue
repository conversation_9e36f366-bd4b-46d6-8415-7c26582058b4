<template>
  <div class="afoot-container">
    <!-- 倒计时 -->
    <audio ref="countAudio" controls="controls" hidden :src="countAu"></audio>
    <div v-if="countdownSecond > 0" class="countdown">
      <div class="countdonw-t">{{ countdownSecond }}</div>
    </div>
    <!-- A组 -->
    <div class="pk-container blue">
      <div class="header">
        <div class="box box1">已完成:{{ leftContext.finished }}</div>
        <div class="box box2">答对:{{ leftContext.answerRight }}</div>
        <div class="box box2">A组</div>
      </div>
      <div class="content">
        <div v-if="leftContext.question" class="question-box" style="flex: 1">
          <div class="question">
            <div v-if="gameData.time" class="second">{{ gameData.time - leftSecond }}</div>
            <template v-if="leftContext.currentIndex < questionList.length">
              <div class="number">{{ leftContext.currentIndex + 1 }}</div>
              <span v-html="leftContext.question.question"></span>
              <img v-if="leftContext.trueNumber !== undefined" class="icon-result" :src="trueIcon" alt="" />
              <img v-if="leftContext.falseNumber !== undefined" class="icon-result" :src="falseIcon" alt="" />
            </template>
            <div v-else class="game-finish">
              <img class="game-winner" :src="gameWinner" alt="" />
              <div class="game-winner-t">作答完成</div>
            </div>
          </div>
          <div v-if="leftContext.currentIndex < questionList.length" class="answer-group">
            <div
              class="answer-left"
              :class="{
                'true-answer': leftContext.trueNumber === 0,
                'wrong-answer': leftContext.falseNumber === 0
              }"
              @click="selectLeftAnswer(0)"
            >{{ leftContext.question.answerOptionList[0].answer }}</div>
            <div
              class="answer-right"
              :class="{
                'true-answer': leftContext.trueNumber === 1,
                'wrong-answer': leftContext.falseNumber === 1
              }"
              @click="selectLeftAnswer(1)"
            >{{ leftContext.question.answerOptionList[1].answer }}</div>
          </div>
        </div>
        <div class="second-line">
          <div class="second-all">
            <div class="progress" :style="{ 'height': (leftContext.currentIndex / questionList.length) * 100 + '%'}">
              <img class="icon-game-time" :src="iconGameTime" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <img class="pk" :src="iconGamePK" alt="" />
    <!-- B组 -->
    <div class="pk-container green">
      <div class="header">
        <div class="box box1">已完成:{{ rightContext.finished }}</div>
        <div class="box box2">答对:{{ rightContext.answerRight }}</div>
        <div class="box box2">B组</div>
      </div>
      <div class="content">
        <div class="second-line">
          <div class="second-all">
            <div class="progress" :style="{ 'height': (rightContext.currentIndex / questionList.length) * 100 + '%'}">
              <img class="icon-game-time" :src="iconGameTime" alt="" />
            </div>
          </div>
        </div>
        <div v-if="rightContext.question" class="question-box" style="flex: 1">
          <div class="question">
            <div v-if="gameData.time" class="second">{{ gameData.time - rightSecond }}</div>
            <template v-if="rightContext.currentIndex < questionList.length">
              <div class="number">{{ rightContext.currentIndex + 1 }}</div>
              <span v-html="rightContext.question.question"></span>
              <img v-if="rightContext.trueNumber !== undefined" class="icon-result" :src="trueIcon" alt="" />
              <img v-if="rightContext.falseNumber !== undefined" class="icon-result" :src="falseIcon" alt="" />
            </template>
            <div v-else class="game-finish">
              <img class="game-winner" :src="gameWinner" alt="" />
              <div class="game-winner-t">作答完成</div>
            </div>
          </div>
          <div v-if="rightContext.currentIndex < questionList.length" class="answer-group">
            <div
              class="answer-left"
              :class="{
                'true-answer': rightContext.trueNumber === 0,
                'wrong-answer': rightContext.falseNumber === 0
              }"
              @click="selectRightAnswer(0)"
            >{{ rightContext.question.answerOptionList[0].answer }}</div>
            <div
              class="answer-right"
              :class="{
                'true-answer': rightContext.trueNumber === 1,
                'wrong-answer': rightContext.falseNumber === 1
              }"
              @click="selectRightAnswer(1)"
            >{{ rightContext.question.answerOptionList[1].answer }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import iconGamePK from '@/assets/ai-image/ai/icon-game-pk.svg'
import iconGameTime from '@/assets/ai-image/ai/icon-game-time.svg'
import trueIcon from '@/assets/ai-image/ai/icon-game-true.svg'
import falseIcon from '@/assets/ai-image/ai/icon-game-false.svg'
import gameWinner from '@/assets/ai-image/ai/game-winner.png'
import countAu from '@/assets/audio/count-down.mp3'
export default {
  props: {
    gameData: Object
  },
  data () {
    return {
      iconGamePK,
      iconGameTime,
      trueIcon,
      falseIcon,
      gameWinner,
      countAu,
      countdownTimer: null,
      leftTimer: null,
      rightTimer: null,
      countdownSecond: 3,
      leftSecond: 0,
      rightSecond: 0,
      questionList: [],
      leftContext: {
        'question': undefined,
        'questionList': [],
        'currentIndex': 0,
        'finished': 0,
        'answerRight': 0,
        'trueNumber': undefined,
        'falseNumber': undefined,
        'useSecond': 0
      },
      rightContext: {
        'question': undefined,
        'questionList': [],
        'currentIndex': 0,
        'finished': 0,
        'answerRight': 0,
        'trueNumber': undefined,
        'falseNumber': undefined,
        'useSecond': 0
      }
    }
  },
  created () {
    this.questionList = this.gameData.questionList
    this.leftContext.questionList = this.questionList.slice().sort(() => Math.random() - 0.5)
    this.rightContext.questionList = this.questionList.slice().sort(() => Math.random() - 0.5)
    this.leftContext.question = this.leftContext.questionList[0]
    this.rightContext.question = this.rightContext.questionList[0]
    if (this.gameData.time && this.gameData.time !== 0) {
      this.leftContext.useSecond = this.gameData.time
      this.rightContext.useSecond = this.gameData.time
    }
    this.$nextTick(() => {
      this.countdownTimer = setInterval(() => this.gameStartCount(), 1000)
      this.$refs.countAudio.volume = 0.1
      this.$refs.countAudio.play()
    })
  },
  destroyed () {
    clearInterval(this.countdownTimer)
    clearInterval(this.leftTimer)
    clearInterval(this.rightTimer)
  },
  methods: {
    gameStartCount () {
      this.countdownSecond -= 1
      if (this.countdownSecond < 1) {
        clearInterval(this.countdownTimer)
        if (!this.$refs.countAudio.paused) {
          this.$refs.countAudio.pause()
        }
        this.leftTimer = setInterval(() => this.leftCount(), 1000)
        this.rightTimer = setInterval(() => this.rightCount(), 1000)
      }
    },
    leftCount () {
      this.leftSecond += 1
      if (this.gameData.time && this.gameData.time !== 0 && this.leftSecond > this.gameData.time) {
        const result = {
          'resultA': this.leftContext,
          'resultB': this.rightContext
        }
        this.$emit('setGameResult', result)
        this.$emit('changeStep', 3)
        clearInterval(this.leftTimer)
      }
    },
    rightCount () {
      this.rightSecond += 1
      if (this.gameData.time && this.gameData.time !== 0 && this.rightSecond > this.gameData.time) {
        const result = {
          'resultA': this.leftContext,
          'resultB': this.rightContext
        }
        this.$emit('setGameResult', result)
        this.$emit('changeStep', 3)
        clearInterval(this.rightTimer)
      }
    },
    selectLeftAnswer (index) {
      if (this.leftContext.trueNumber !== undefined || this.leftContext.falseNumber !== undefined) return
      const selectAnswerId = +this.leftContext.question.answerOptionList[index].id
      const trueAnswerId = +this.leftContext.question.answer
      if (selectAnswerId === trueAnswerId) {
        this.leftContext.trueNumber = index
        this.leftContext.answerRight += 1
      } else {
        this.leftContext.falseNumber = index
      }
      this.leftContext.finished += 1
      setTimeout(() => {
        this.leftContext.currentIndex += 1
        this.leftContext.trueNumber = undefined
        this.leftContext.falseNumber = undefined
        if (this.leftContext.currentIndex < this.questionList.length) {
          this.leftContext.question = this.leftContext.questionList[this.leftContext.currentIndex]
        } else {
          clearInterval(this.leftTimer)
          this.leftContext.useSecond = this.leftSecond
          if (this.rightContext.currentIndex >= this.questionList.length) {
            const result = {
              'resultA': this.leftContext,
              'resultB': this.rightContext
            }
            this.$emit('setGameResult', result)
            this.$emit('changeStep', 3)
          }
        }
      }, 500)
    },
    selectRightAnswer (index) {
      if (this.rightContext.trueNumber !== undefined || this.rightContext.falseNumber !== undefined) return
      const selectAnswerId = +this.rightContext.question.answerOptionList[index].id
      const trueAnswerId = +this.rightContext.question.answer
      if (selectAnswerId === trueAnswerId) {
        this.rightContext.trueNumber = index
        this.rightContext.answerRight += 1
      } else {
        this.rightContext.falseNumber = index
      }
      this.rightContext.finished += 1
      setTimeout(() => {
        this.rightContext.currentIndex += 1
        this.rightContext.trueNumber = undefined
        this.rightContext.falseNumber = undefined
        if (this.rightContext.currentIndex < this.questionList.length) {
          this.rightContext.question = this.rightContext.questionList[this.rightContext.currentIndex]
        } else {
          clearInterval(this.rightTimer)
          this.rightContext.useSecond = this.rightSecond
          if (this.leftContext.currentIndex >= this.questionList.length) {
            const result = {
              'resultA': this.leftContext,
              'resultB': this.rightContext
            }
            this.$emit('setGameResult', result)
            this.$emit('changeStep', 3)
          }
        }
      }, 500)
    }
  }
}
</script>

<style lang="scss" scoped>
.afoot-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: vh2(25) vh2(40);
    display: flex;
    align-items: center;
    gap: vw2(20);

    .pk {
        width: vw2(77);
        height: vw2(77);
        object-fit: contain;
    }
}

.countdown {
  width: 100%;
  height: 100%;
  position: fixed;
  background: rgba(0, 0, 0, 0.9);
  left: 0;
  top: 0;
  z-index: 2001;

  .countdonw-t {
    font-family: 'PingFang SC';
    font-weight: 600;
    font-size: vh2(400);
    color: #FFFFFF;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.pk-container {
    height: 100%;
    flex: 1;
    background: #12D8FA;
    border-radius: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .header {
        display: flex;
        justify-content: center;
        gap: 14px;
        padding: vh2(12) vw2(17) vh2(48);
        justify-content: space-between;

        .box {
          height: vw2(40);
          background: #000000;
          border-radius: 40px;
          color: #FFFFFF;
          text-align: center;
          font-size: vw2(15);
          line-height: vw2(40);
          text-align: center;
          white-space: pre;
        }

        .box1 {
          min-width: vw2(80);
        }

        .box2 {
          min-width: vw2(70);
        }
    }

    .content {
        flex: 1;
        display: flex;

        .question {
            box-sizing: border-box;
            position: relative;
            height: vh2(300);
            background: rgba(255, 255, 255, 0.8);
            padding: vh2(56) vh2(15);

            .second {
                position: absolute;
                left: 50%;
                top: vh2(-37);
                transform: translate(-50%, 0);
                width: vh2(80);
                height: vh2(80);
                background: url('~assets/ai-image/ai/icon-game-clock.svg')
                center center no-repeat;
                background-size: contain;
                color: #000000;
                font-size: vh2(36);
                line-height: vh2(80);
                text-align: center;
                font-weight: 600;
            }

            .number {
                width: vh2(40);
                height: vh2(40);
                background: #4F4F4F;
                border-radius: 8px;
                text-align: center;
                line-height: vh2(40);
                color: #FFFFFF;
                font-size: vh2(20);
                font-weight: 600;
                display: inline-block;
            }

            span {
                font-family: 'PingFang SC';
                font-style: normal;
                font-weight: 600;
                font-size: vh2(30);
                line-height: vh2(40);
                color: #000000;
                word-break: break-all;
                white-space: pre-wrap;
            }

            .icon-result {
                width: vh2(60);
                height: vh2(60);
                object-fit: contain;
                position: absolute;
                bottom: vh2(6);
                left: 50%;
                transform: translate(-50%, 0);
            }

            .game-finish {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .game-winner {
                width: vh2(148);
                height: vh2(148);
                object-fit: contain;
                margin-bottom: vh2(6);
                transform: scale(0);
                animation: zoomIn 1s ease-in-out forwards;
            }

            @keyframes zoomIn {
                from {
                    transform: scale(0);
                }
                to {
                    transform: scale(1);
                }
            }

            .game-winner-t {
                font-weight: 600;
                font-size: vh2(50);
                line-height: vh2(70);
                color: #000000;
            }
        }

        .answer-group {
            display: flex;
            margin-top: vh2(40);

            .answer-left,
            .answer-right {
                height: vh2(50);
                min-width: vh2(100);
                background: #F2994A;
                border: vh2(4) solid #000000;
                border-radius: 10px;
                margin: 0 auto;
                padding: 0 vh2(10);
                box-sizing: border-box;
                font-size: vh2(30);
                line-height: vh2(42);
                font-weight: 600;
                color: #000000;
                text-align: center;
                cursor: pointer;
                white-space: nowrap;
            }

            .true-answer {
              background: #6FCF97;
            }

            .wrong-answer {
                animation: wrongAnimate 0.3s ease-in-out 1;
                background: #EB5757;
            }

            /* 文字动画代码 */
            @keyframes wrongAnimate {
                0% {
                    transform: translateX(0);
                }
                25% {
                    transform: translateX(-10px);
                }
                75% {
                    transform: translateX(10px);
                }
                100% {
                    transform: translateX(0);
                }
            }
        }

        .second-line {
            box-sizing: border-box;
            width: vh2(26);
            height: 100%;
            padding: 0 vh2(6);

            .second-all {
                width: 100%;
                height: 100%;
                background: rgba(19, 19, 19, 1);
                border-radius: 45px;
                box-sizing: border-box;
                position: relative;
            }

            .progress {
                width: 100%;
                height: 0%;
                background: rgba(242, 201, 76, 1);
                border-radius: 45px;
                position: absolute;
                bottom: 0;
            }
          }

            .icon-game-time {
                width: vh2(33);
                height: vh2(33);
                object-fit: contain;
                position: absolute;
                left: vh2(-9);
                top: vh2(-20);
                z-index: 10;
            }
        }
}

.blue {
    background: rgba(18, 216, 250, 1);
}

.green {
    background: #6FCF97;
}
</style>
