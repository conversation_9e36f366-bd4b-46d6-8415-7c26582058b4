<template>
  <div class="game-result">
    <div class="part">
      <img class="cup" :src="['A', 'C'].indexOf(winner) > -1 ? gameWinnerCup : gameLoserCup" alt="" />
      <div class="group-name">A组</div>
      <div :class="[['A', 'C'].indexOf(winner) > -1 ? 'winner-podium' : 'loser-podium']">
        <img class="bg-podium" :src="['A', 'C'].indexOf(winner) > -1 ? gameWinnerPodium : gameLoserPodium" alt="" />
        <div class="t t1 mb30">{{ winner === 'A' ? '胜利' : winner === 'C' ? '平局' : '失败' }}</div>
        <div class="t t2 mb30">答对 {{ resultA.answerRight }} 道</div>
        <div class="t t2">{{ resultA.useSecond | formatmmss }}</div>
      </div>
    </div>
    <div class="part">
      <img class="cup" :src="['B', 'C'].indexOf(winner) > -1 ? gameWinnerCup : gameLoserCup" alt="" />
      <div class="group-name">B组</div>
      <div :class="[['B', 'C'].indexOf(winner) > -1 ? 'winner-podium' : 'loser-podium']">
        <img class="bg-podium" :src="['B', 'C'].indexOf(winner) > -1 ? gameWinnerPodium : gameLoserPodium" />
        <div class="t t1 mb30">{{ winner === 'B' ? '胜利' : winner === 'C' ? '平局' : '失败' }}</div>
        <div class="t t2 mb30">答对 {{ resultB.answerRight }} 道</div>
        <div class="t t2">{{ resultB.useSecond | formatmmss }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import gameWinnerCup from '@/assets/ai-image/ai/game-winner-cup.png'
import gameWinnerPodium from '@/assets/ai-image/ai/game-winner-podium.svg'
import gameLoserCup from '@/assets/ai-image/ai/game-loser-cup.png'
import gameLoserPodium from '@/assets/ai-image/ai/game-loser-podium.svg'
export default {
  filters: {
    formatmmss (s) {
      const minute = Math.floor(s / 60)
      const second = s - 60 * minute
      return `${minute}分${second}秒`
    }
  },
  props: {
    gameResult: Object
  },
  data () {
    return {
      gameWinnerCup,
      gameWinnerPodium,
      gameLoserCup,
      gameLoserPodium,
      resultA: undefined,
      resultB: undefined,
      winner: undefined
    }
  },
  created () {
    this.$emit('updateProgress')
    this.resultA = this.gameResult.resultA
    this.resultB = this.gameResult.resultB
    if (this.resultA.answerRight > this.resultB.answerRight) {
      this.winner = 'A'
    } else if (this.resultA.answerRight < this.resultB.answerRight) {
      this.winner = 'B'
    } else {
      if (this.resultA.useSecond < this.resultB.useSecond) {
        this.winner = 'A'
      } else if (this.resultA.useSecond > this.resultB.useSecond) {
        this.winner = 'B'
      } else {
        this.winner = 'C'
      }
    }
  },
  methods: {
    formatmmss () {

    }
  }
}
</script>

<style lang="scss" scoped>
.game-result {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    padding: vh2(40) 0 0;
    justify-content: center;

    .part {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        height: 100%;

        .cup {
            width: vh2(90);
            height: vh2(90);
            object-fit: contain;
        }

        .group-name {
            font-weight: 600;
            font-size: vh2(60);
            color: #333333;
        }

        .winner-podium,
        .loser-podium {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            position: relative;
        }

        .winner-podium {
            width: vh2(300);
            padding-bottom: vh2(90);
            margin-top: vh2(95);
        }

        .loser-podium {
            width: vh2(300);
            padding-bottom: vh2(30);
            margin-top: vh2(50);
        }

        .bg-podium {
            width: 100%;
            object-fit: contain;
            z-index: 9;
            position: absolute;
            bottom: 0;
        }

        .t {
            color: #FFFFFF;
            text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
            font-weight: 600;
            z-index: 10;
        }

        .t1 {
            font-size: vh2(60);
        }

        .t2 {
            font-size: vh2(28);
        }

        .mb30 {
            margin-bottom: vh2(30);
        }
    }
}
</style>
