<template>
  <div class="game-description">
    <div class="container">
      <div class="title">答题挑战赛</div>
      <div class="game-description-content">
        <div v-if="videConfig && videConfig.length > 0" class="left">
          <div id="gameVideo" class="game-video">
            <video-player
              ref="videoPlayer"
              class="game-video-player"
              :options="playerOptions"
              playsinline
              webkit-playsinline
              x5-video-player-type="h5"
              x5-playsinline="true"
              x5-video-orientation="portraint"
            />
          </div>
        </div>
        <div class="right">
          <div id="right" class="right-inner">
            <div class="descirbe" v-html="textConfig"></div>
            <div class="btn-start" @click="changeStep">开始挑战</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import iconGame2 from '@/assets/ai-image/ai/icon-game-2.png'
import iconGame3 from '@/assets/ai-image/ai/icon-game-3.svg'
import close from '@/assets/ai-image/icon/video-close.svg'
import { videoPlayer } from 'vue-video-player'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
export default {
  components: { videoPlayer },
  props: {
    textConfig: String,
    videConfig: Array
  },
  data () {
    return {
      // iconGame1,
      iconGame2,
      iconGame3,
      close,
      showVideo: false,
      sources: [
        {
          type: '',
          src: '',
          duration: 0
        }
      ],
      playerOptions: {
        language: 'zh-CN',
        autoplay: true,
        muted: false,
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'progressControl' }, // 播放进度条
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'timeDivider' },
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            { name: 'FullscreenToggle' } // 全屏
          ]
        },
        sources: this.videConfig,
        hls: true,
        notSupportedMessage: '此视频暂无法播放,请稍后再试'
      }
    }
  },
  mounted () {
    const isMobile = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
    if (isMobile) {
      this.playerOptions.autoplay = false
      this.playerOptions.controlBar = false
    } else if (window.location.pathname.indexOf('aigame') > -1) {
      this.playerOptions.muted = true
    }
    this.resize()
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    changeStep () {
      this.$emit('changeStep', 2)
    },
    resize () {
      var div1Height = document.getElementById('gameVideo').clientHeight
      document.getElementById('right').style.minHeight = div1Height + 'px'
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.game-description {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    position: relative;

    .container {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: vh2(15);
        display: flex;
        flex-direction: column;

        .title {
            color: #000000;
            font-weight: 600;
            font-size: vh2(60);
            // flex: 1;
        }

        .game-description-content {
          display: flex;
          // height: vh2(460);
          flex: 1;
          width: 100%;
        }

        .left {
          height: 100%;
          width: 70%;
          position: relative;

          .game-video {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            // padding-bottom: 56.25%;
            border-radius: 10px;
            background-color: transparent;
            transform: translate(0, -50%);
          }
        }

        .right {
          // width: calc(100% - #{vh2(818)});
          width: 30%;
          height: 100%;
          padding-left: vh2(34);

          .right-inner {
            position: absolute;
            top: 50%;
            right: 0;
            transform: translate(0, -50%);
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            align-items: center;
            position: relative;
            gap: vh2(20);
          }

          .btn-start {
            width: vh2(160);
            height: vh2(56);
            font-weight: 300;
            font-size: vh2(30);
            line-height: vh2(56);
            color: #000000;
            background: linear-gradient(90deg, #FFE259 0%, #FFA751 100%);
            border: vh2(5) solid #000000;
            border-radius: 24px;
            text-align: center;
            font-family: 'Hannotate SC';
            cursor: pointer;
          }

          .descirbe {
              font-size: vh2(25);
              line-height: vh2(38);
              color: #000000;
              font-weight: 300;
              font-family: 'Hiragino Sans GB';
              word-break: break-all;
              white-space: pre-line;
              flex: 1;
              height: 100%;
              overflow: auto;
              @include aiPkScrollBar;
          }
        }
    }
}
</style>

<style lang="scss">
.game-video {

  .video-player {
    width: 100%;
    height: 100%;
  }

  .video-js {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 10px;
    padding-bottom: 56.25%;
  }

  .video-js .vjs-tech {
    border-radius: 10px;
    object-fit: cover;
  }

  .video-js .vjs-big-play-button {
    left: 0 !important;
    top: 0 !important;
    width: 100%;
    height: 100%;
    border: none;
    background: none;
    z-index: 10;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;

    .vjs-icon-placeholder:before {
      content: '';
      height: 50px;
      width: 50px;
      background: url('~assets/images/ai/ai-play-btn.png') center center no-repeat;
      background-size: contain;
      position: absolute;
      left: 50% !important;
      top: 50% !important;
      transform: translate(-50%, -50%);
    }
  }

  .vjs-has-started.vjs-paused .vjs-big-play-button {
    display: block !important;
  }

  .video-js .vjs-slider {
    background: rgba(255, 255, 255, 0.2);
  }

  .video-js .vjs-load-progress div {
    background: rgba(255, 255, 255, 0.5);
  }

  .video-js .vjs-control-bar {
    height: 6em;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 20;
    border-radius: 10px;

    .vjs-play-control {
      .vjs-icon-placeholder:before {
        font-size: 2.2em;
        height: initial;
        top: 50%;
        transform: translate(0, -50%) !important;
      }
    }

    .vjs-time-control {
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 6em;

      &.vjs-current-time {
        padding-right: 0;
      }

      &.vjs-time-divider {
        min-width: 0;
        padding: 0 4px;
      }

      &.vjs-duration {
        padding-left: 0;
      }
    }

    .vjs-control.vjs-button {
      .vjs-icon-placeholder:before {
        font-size: 2em;
        height: initial;
        top: 50%;
        transform: translate(0, -50%) !important;
      }
    }

    .vjs-fullscreen-control .vjs-icon-placeholder:before {
      color: transparent;
      background: url('~assets/images/ai/ai-fullscreen.png') center center
        no-repeat;
      background-size: 16px 16px;
    }

    .vjs-fullscreen-control .vjs-icon-placeholder:before {
      color: transparent;
      background: url('~assets/images/ai/ai-fullscreen.png') center center
        no-repeat;
      background-size: 16px 16px;
    }
  }

  .video-js.vjs-fullscreen {
    .vjs-fullscreen-control .vjs-icon-placeholder:before {
      color: transparent;
      background: url('~assets/images/ai/ai-miniscreen.png') center center
        no-repeat;
      background-size: 16px 16px;
    }
  }
  .vjs-error .vjs-error-display .vjs-modal-dialog-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .vjs-icon-play,
  .video-js .vjs-big-play-button .vjs-icon-placeholder:before {
    font-size: 30px;
    margin-top: 5px;
  }
  .video-js .vjs-play-progress:before {
    top: 50%;
    transform: translateY(-50%);
  }
  .video-js .vjs-volume-vertical {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 1em;
  }
  .vjs-slider-vertical .vjs-volume-level:before {
    left: 50%;
    transform: translateX(-50%);
  }

  .vjs-custom-waiting .vjs-loading-spinner {
    display: block;
    visibility: visible;
  }
  .video-js.vjs-custom-waiting .vjs-loading-spinner:before,
  .video-js.vjs-custom-waiting .vjs-loading-spinner:after {
    /* I just copied the same animation as in the default css file */
    -webkit-animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8)
        infinite,
      vjs-spinner-fade 1.1s linear infinite;
    animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8) infinite,
      vjs-spinner-fade 1.1s linear infinite;
  }

  .f2em {
    font-size: 2em
  }
}
</style>
