<template>
  <div class="game">
    <div class="content">
      <!-- 需要单独作为一个url使用 -->
      <game
        ref="game"
        :game-id="context.currentSection.sourceId"
        :section-id="context.currentSection.id"
        @changeStep="changeStep"
        @updateProgress="$emit('updateProgress')"
      />
    </div>
    <div class="btn-group">
      <div v-if="preText" class="pre" @click="topCallBack">{{ preText }}</div>
      <div class="next" @click="bottomCallBack">{{ nextText }}</div>
    </div>
    <Dialog
      v-if="showDialog"
      :context="context"
      :is-mobile="isMobile"
      :title="title"
      left-text="再想想"
      :right-text="rightText"
      @leftCallback="leftDialogCallback"
      @rightCallback="rightDialogCallback"
    />
  </div>
</template>

<script>
import Game from './components/game.vue'
import Dialog from '../Dialog.vue'
export default {
  components: { Game, Dialog },
  props: {
    context: Object,
    isMobile: Boolean
  },
  data () {
    return {
      step: 1,
      showDialog: false,
      title: '',
      rightText: ''
    }
  },
  computed: {
    preText () {
      // switch (this.step) {
      //   case 1:
      //     var currentSectionIndex = this.context.currentSectionIndex
      //     if (currentSectionIndex < 1) {
      //       return ''
      //     }
      //     return '上一步'
      //   case 2:
      //     return '上一步'
      //   case 3:
      //     return '再开一局'
      // }
      return '上一步'
    },
    nextText () {
      // switch (this.step) {
      //   case 1:
      //   case 2:
      //     return '跳过'
      //   case 3:
      //     return '下一步'
      // }
      return '下一步'
    }
  },
  methods: {
    changeStep (step) {
      this.step = step
    },
    topCallBack () {
      // switch (this.step) {
      //   case 1:
      //     this.$emit('playback', 'prev')
      //     break
      //   case 2:
      //     this.$emit('playButtonAudio')
      //     this.title = '确定退出挑战？'
      //     this.rightText = '确定'
      //     this.showDialog = true
      //     break
      //   case 3:
      //     this.$emit('playButtonAudio')
      //     this.$refs.game.changeStep(this.step - 1)
      //     break
      // }
      this.$emit('playback', 'prev')
    },
    bottomCallBack () {
      this.$emit('playButtonAudio')
      // switch (this.step) {
      //   case 1:
      //   case 2:
      //     this.title = '确定跳过挑战赛？'
      //     this.rightText = '跳过'
      //     this.showDialog = true
      //     break
      //   case 3:
      //     this.$emit('playback', 'next')
      //     break
      // }
      // 去掉跳过挑战赛弹框 ---- 2023.8.14
      // this.title = '确定跳过挑战赛？'
      // this.rightText = '跳过'
      // this.showDialog = true
      this.$emit('updateProgress')
      this.$emit('playback', 'next')
    },
    leftDialogCallback () {
      this.showDialog = false
    },
    rightDialogCallback () {
      switch (this.rightText) {
        case '跳过':
          this.$emit('updateProgress')
          this.$emit('playback', 'next')
          this.showDialog = false
          break
        case '确定':
          this.$refs.game.changeStep(1)
          this.showDialog = false
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.game {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    background: linear-gradient(107.15deg, #E9EFFF -0.58%, #ACF6FF 100%);
    display: flex;
    padding: vh2(50) vh2(20) vh2(20);

    .content {
        width: 83%;
        height: 100%;
    }

    .btn-group {
        flex: 1;
        gap: vh2(15);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;

        .pre,
        .next {
            font-size: vh2(20);
            font-family: 'PingFang SC';
            font-weight: 500;
            color: #000000;
            line-height: vh2(50);
            text-align: center;
            cursor: pointer;
            height: vh2(50);
            width: vh2(150);
        }

        .pre {
            background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
            background-size: contain;
        }

        .next {
            background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
            background-size: contain;
        }
    }
}
</style>
