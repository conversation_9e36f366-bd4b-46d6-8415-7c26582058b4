<template>
  <div class="question-container">
    <lottie v-show="showTrueAni" :width="1000" :height="700" :options="trueAniOptions" class="animation" @animCreated="handleTrueAnimation" />
    <lottie v-show="showFalseAni" :width="1000" :height="700" :options="falseAniOptions" class="animation" @animCreated="handleFalseAnimation" />
    <audio ref="taudio" controls="controls" hidden :src="trueAu"></audio>
    <audio ref="faudio" controls="controls" hidden :src="falseAu"></audio>
    <div class="question flex align-center justify-center">
      <div class="">
        <span
          v-for="(item, index) in splitQuestion"
          :key="index"
          :class="[{ blank: questionList[index] === '[*]', filled: questionList[index] !== splitQuestion[index] }]"
          @click="popBlank(index)"
        >{{ item }}</span>
      </div>
    </div>

    <div class="answer flex align-center justify-center">
      <div class="full-h flex-col align-start justify-between">
        <div
          v-for="(item, index) in optionList"
          :key="index"
          class="option flex align-center"
          :class="{
            'selectable':
              !complete &&
              !playback &&
              currentSegmentIndex >= currentAnsweredIndex,
            'select-true':
              trueAnswer.length > 0 &&
              answer.includes(item.id) &&
              trueAnswer.includes(item.id) &&
              answer.indexOf(item.id) === trueAnswer.indexOf(item.id),
            'select-false':
              (trueAnswer.length > 0 &&
                answer.includes(item.id) &&
                !trueAnswer.includes(item.id)) ||
              (trueAnswer.length > 0 &&
                answer.includes(item.id) &&
                trueAnswer.includes(item.id) &&
                answer.indexOf(item.id) !== trueAnswer.indexOf(item.id)),
            'true-answer':
              trueAnswer.length > 0 &&
              !answer.includes(item.id) &&
              trueAnswer.includes(item.id),
          }"
          @click="handleSelect(index)"
        >
          <div class="content">{{ item.answer }}</div>
          <div
            v-if="
              trueAnswer.length > 0 &&
                trueAnswer.includes(item.id) &&
                trueAnswer.indexOf(item.id) !== answer.indexOf(item.id)
            "
            class="answer-tip"
          >
            <span>正确答案</span>
            <br />
            <span>（第{{ Number(trueAnswer.indexOf(item.id)) + 1 }}空）</span>
          </div>
        </div>
      </div>
    </div>
    <div v-show="showTrueAni || showFalseAni" class="ani-overlay"></div>
    <div v-show="showScore" class="ani-score">+{{ score }}</div>
  </div>
</template>

<script>
import { updateAicourseUserAnswer, getUserAnswer } from '@/api/aicourse'
import { mapGetters } from 'vuex'
import lottie from 'vue-lottie'
import trueAni from '@/assets/images/ai/true.json'
import falseAni from '@/assets/images/ai/false.json'
import trueAu from '@/assets/audio/true-au.mp3'
import falseAu from '@/assets/audio/false-au.mp3'
export default {
  components: { lottie },
  props: {
    showAnswered: Boolean, // / 是否已经选择完成, 进入核对答案环节
    playback: {
      type: Boolean,
      require: true,
      default: () => false
    },
    unitUserId: {
      type: Number,
      require: true
    },
    question: {
      type: Object,
      require: true,
      default: () => {}
    },
    aicourseUnitId: {
      type: Number,
      require: true
    },
    currentSegmentIndex: {
      type: Number,
      require: true
    },
    currentAnsweredIndex: {
      type: Number,
      require: true
    }
  },
  data () {
    return {
      studentCourseId: this.$route.params.studentCourseId,
      answer: [],
      trueAnswer: [],
      optionList: [],
      splitQuestion: [],
      questionList: [],
      selectedOpts: [],
      trueAniOptions: { animationData: trueAni, loop: false, autoplay: false },
      trueLottie: '',
      falseAniOptions: { animationData: falseAni, loop: false, autoplay: false },
      falseLottie: '',
      showTrueAni: false,
      showFalseAni: false,
      complete: false,
      count: 0,
      timer: null,
      trueAu,
      falseAu,
      score: 0,
      showScore: false,
      token: this.$route.query.token,
      shareUserId: this.$route.query.userId
    }
  },
  created () {
    this.handleQuestion()
    this.handleOpt()
  },
  mounted () {
    this.timer = setInterval(() => this.subCount(), 1000)
    if (this.playback || this.currentSegmentIndex < this.currentAnsweredIndex) {
      this._getUserAnswer()
    }
  },
  destroyed () {
    clearInterval(this.timer)
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  methods: {
    handleQuestion () {
      const splitQuestion = this.question.question.split(/(\[\*\])/g)
      this.questionList = this.question.question.split(/(\[\*\])/g)

      console.log(splitQuestion)
      this.splitQuestion = splitQuestion
    },
    handleOpt () {
      const optionList = this.question.answerOptionList || []
      const length = optionList.length
      if (length <= 3) {
        this.optionList = optionList
      } else {
        this.optionList = optionList.slice(0, 3)
      }
    },
    handleSelect (index) {
      if (this.playback || this.currentSegmentIndex < this.currentAnsweredIndex) return
      if (this.selectedOpts.length === 0) {
        this.selectedOpts.push(this.optionList[index])
        this.selectedOptsChange()
      } else if (this.selectedOpts.length === this.questionList.filter(item => item === '[*]').length) {
        console.log('done')
      } else {
        // if (!this.selectedOpts.includes(this.optionList[index])) {
        this.selectedOpts.push(this.optionList[index])
        this.selectedOptsChange()
        // }
      }
    },
    handleViewAnswer (list) {
      list.forEach(el => {
        const index = this.optionList.findIndex(item => item.id === el)
        console.log(index, el)
        if (index !== undefined && index !== null) {
          this.selectedOpts.push(this.optionList[index])
        }
      })
      this.selectedOptsChange()
      // if (this.selectedOpts.length === 0) {
      //   this.selectedOpts.push(this.optionList[index])
      //   this.selectedOptsChange()
      // } else if (this.selectedOpts.length === this.questionList.filter(item=> item === '[*]').length){
      //   console.log('done')
      // } else {
      //   if (!this.selectedOpts.includes(this.optionList[index])) {
      //     this.selectedOpts.push(this.optionList[index])
      //     this.selectedOptsChange()
      //   }
      // }
    },
    popBlank (index) {
      console.log(index)
      if (this.playback || this.currentSegmentIndex < this.currentAnsweredIndex) return
      if (this.questionList[index] !== this.splitQuestion[index]) {
        const blankList = []
        this.questionList.forEach((item, index) => {
          if (item === '[*]') {
            blankList.push(index)
          }
        })
        console.log(blankList)
        this.selectedOpts.splice(blankList.indexOf(index))
        // this.selectedOpts.splice(_.findLastIndex(this.selectedOpts, item => item.answer === this.splitQuestion[index]))
        console.log('pop')
        this.selectedOptsChange()
      }
    },
    _updateAicourseUserAnswer () {
      const answer = this.answer.join(',')
      const params = {
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.aicourseUnitId,
        questionId: this.question.id,
        answer,
        times: this.count,
        token: this.token || null
      }
      this.complete = true
      updateAicourseUserAnswer(params).then(response => {
        if (+response.data.code === 200) {
          const data = response.data.data || {}
          this.$emit('updateUnitUserId', data.sourceId)
          console.log(data)
          this.trueAnswer = data.question.answer.split(',').map(Number)
          if (this.answer.join(',') === this.trueAnswer.join(',')) {
            console.log('answer true')
            this.showTrueAni = true
            this.playMusic(true)
            this.handlePlayTrueAnimation()
          } else {
            console.log('answer false')
            this.showFalseAni = true
            this.playMusic(false)
            this.handlePlayFalseAnimation()
          }
        } else {
          this.$toast(response.data.message, {
            position: 'center',
            duration: '2000'
          })
          setTimeout(() => {
            this.handleAnswered(this.answer)
          }, 2000)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    _getUserAnswer () {
      const params = {
        questionId: this.question.id,
        questionSource: 'AI',
        sourceId: this.unitUserId,
        userId: this.shareUserId || this.userInfo.id
      }
      getUserAnswer(params).then(response => {
        if (+response.data.code === 200) {
          if (!response.data.data) return
          const data = response.data.data || {}
          console.log(data)
          const userAnswerList = data.answerIds.split(',').map(Number)
          this.handleViewAnswer(userAnswerList)
          this.trueAnswer = data.question.answer.split(',').map(Number)
          // userAnswerList.forEach((item) => {
          //   this.handleViewAnswer(item)
          // })
          // this.selectedOpts = data.answerIds.split(',').map(Number)

          // this.selectedOptsChange()
        } else {
          this.$toast(response.data.message, {
            position: 'center',
            duration: '2000'
          })
          setTimeout(() => {
            this.handleAnswered(this.answer)
          }, 2000)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handleAnswered (evt) {
      this.$emit('answered', evt)
    },
    handleTrueAnimation (anim) {
      this.trueLottie = anim
      this.trueLottie.addEventListener('complete', () => {
        console.log('complete')
        this.showTrueAni = false
        this.showScore = false
        // / TODO: 进入下一个环节
        this.handleAnswered(this.answer)
        // this.$emit('answered', this.answer);
      })
    },
    handleFalseAnimation (anim) {
      this.falseLottie = anim
      this.falseLottie.addEventListener('complete', () => {
        console.log('complete')
        this.showFalseAni = false
        // / TODO: 进入下一个环节
        this.handleAnswered(this.answer)
        // this.$emit('answered', this.answer);
      })
    },
    handlePlayTrueAnimation () {
      this.trueLottie.play()
      const score = this.question.score || 0
      this.score = this.question.score || 0
      this.$emit('getScore', score)
      setTimeout(() => {
        this.showScore = true
      }, 2000)
    },
    handlePlayFalseAnimation () {
      this.falseLottie.play()
      this.$emit('getScore', 0)
    },
    playMusic (type) {
      if (type) {
        this.$refs.taudio.currentTime = 0
        this.$refs.taudio.volume = 0.1
        this.$refs.taudio.play()
        setTimeout(() => {
          this.$refs.taudio.pause()
        }, 3000)
      } else {
        this.$refs.faudio.currentTime = 0
        this.$refs.faudio.volume = 0.1
        this.$refs.faudio.play()
        setTimeout(() => {
          this.$refs.faudio.pause()
        }, 3000)
      }
    },
    subCount () {
      this.count += 1
    },
    selectedOptsChange () {
      const foundIndex = []
      const answerList = []
      this.questionList.forEach((item, index) => {
        if (item === '[*]') {
          foundIndex.push(index)
        }
      })
      this.splitQuestion = [...this.questionList]
      for (let i = 0; i < this.selectedOpts.length; i++) {
        this.splitQuestion.splice(foundIndex[i], 1, this.selectedOpts[i].answer)
      }
      this.selectedOpts.forEach(item => {
        answerList.push(item.id)
      })
      this.answer = [...answerList]
      if (this.selectedOpts.length === this.questionList.filter(item => item === '[*]').length && !this.playback && this.currentSegmentIndex >= this.currentAnsweredIndex) {
        console.log('done')
        this._updateAicourseUserAnswer()
      }
      console.log('splitQuestion', this.splitQuestion)
      console.log('selectedOpts', this.selectedOpts)
      console.log('answerList', this.answer)
    }
  },
  watch: {
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
@import "@/styles/variables";

.question-container {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;

  * {
    box-sizing: border-box;
  }
}

.question {
  padding: 0 16px 0;
  width: 100%;
  height: 150px;
  font-size: 30px;
  font-weight: 500;
  color: #2C304E;
  word-break: break-all;
  line-height: 45px;

  span {
    white-space: pre-wrap;
    word-wrap : break-word
  }
}

.answer {
  padding: 24px 16px 10px;
  width: 100%;
  height: calc(100% - 150px);
}

.animation {
  position: absolute;
  left: 50%;
  top: 50%;
  // width: 600px !important;
  transform: translate(-50%, -58%);
  z-index: 9999;
}

.ani-overlay {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 200vw;
  height: 200vh;
  background: rgba(0, 0, 0, 0.7);
  transform: translate(-50%, -50%);
  z-index: 9998;
}

.ani-score {
  position: absolute;
  left: 480px;
  top: 250px;
  font-size: 80px;
  font-weight: bolder;
  color: #FEDD46;
  z-index: 9999;
}

.option {
  position: relative;
  padding: 0 10px 0 8px;
  height: 78px;
  background: #FFEFDB;
  border-radius: 40px;
  border: 3px solid #FFEFDB;

  .content {
    padding: 0 10px;
    min-width: 250px;
    font-size: 30px;
    font-weight: 500;
    color: #2C304E;
    line-height: 42px;
    text-align: center;
  }

  &.selectable:hover {
    cursor: pointer;
    border: 3px solid #6A90FF;
  }

  &.selected {
    background: #F1F1F1;
    border: 3px dashed #D5D5D5;
    color: #8E8E8E;
    cursor: not-allowed;
  }

  &.select-true {
    position: relative;
    background: #FFEFDB;
    border: 3px solid #6A90FF;

    &::after {
      position: absolute;
      top: -2px;
      right: -2px;
      content: '';
      height: 40px;
      width: 64px;
      background: url('../../../assets/images/ai/select-true.png') center center no-repeat;
      background-size: cover;
    }
  }

  &.select-false {
    position: relative;
    background: #FFEFDB;
    border: 3px solid #FF5B49;

    &::after {
      position: absolute;
      top: -2px;
      right: -2px;
      content: '';
      height: 40px;
      width: 64px;
      background: url('../../../assets/images/ai/select-false.png') center center no-repeat;
      background-size: cover;
    }
  }

  &.true-answer {
    position: relative;
    background: #6A90FF;
    border: 3px solid #6A90FF;
    box-shadow: 0px 2px 5px 0px rgba(0, 52, 205, 0.32);

    .serial {
      background: #FFFFFF;
      color: #6A90FF;
    }

    .content {
      color: #FFFFFF;
    }
  }

  .answer-tip {
    position: absolute;
    right: -100px;
    top: 50%;
    color: #6A90FF;
    font-size: 18px;
    font-weight: 500;
    transform: translateY(-50%);
    text-align: center;
  }
}

.picture {
  width: 42%;
}

::v-deep.blank {
  margin: 0 8px;
  min-width: 40px;
  display: inline-block;
  color: transparent;
  background: #E5ECFF;
  border-radius: 10px;
  line-height: 42px;
  user-select: none;
}

::v-deep.filled {
  min-width: 40px;
  padding: 0 10px;
  display: inline-block;
  color: #6A90FF;
  background: #E5ECFF;
  border-radius: 10px;
  line-height: 42px;
  user-select: none;
  cursor: pointer;
}
</style>
