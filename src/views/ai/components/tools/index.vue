<template>
  <div ref="tools" class="ai-tools">
    <div class="ai-tools-box" :class="{'ai-tools-box-wide' : isWideTools}">
      <i v-if="!isWideTools" class="el-icon-d-arrow-left ai-tools-btn" @click="isWideTools = true"></i>
      <i v-if="isWideTools" class="el-icon-d-arrow-right ai-tools-btn" @click="isWideTools = false"></i>
      <div class="tool-item" :class="{'a-tool-item':active === 5}" @click="hanleClick(5)">
        <img :src="active === 5 ? Aiassist : Aiassist" />
        AI助教
      </div>
      <div v-show="!isPreview" class="tool-item" :class="{'a-tool-item':active === 0}" @click="hanleClick(0)">
        <img :src="active === 0 ? Apk : pk" />
        分组PK
      </div>
      <div class="tool-item" :class="{'a-tool-item':isDraw}" @click="hanleDraw">
        <img :src="isDraw ? Apen : pen" />
        画笔
      </div>
      <div class="tool-item" :class="{'a-tool-item':active === 3}" @click="startAnimate">
        <img :src="active === 3 ? Ahecai : hecai" />
        喝彩
      </div>
      <div class="tool-item" :class="{'a-tool-item':active === 1}" @click="hanleClick(1)">
        <img :src="active === 1 ? Atime : time" />
        倒计时
      </div>
      <div class="tool-item" :class="{'a-tool-item':active === 4}" @click="hanleClick(4)">
        <img :src="active === 4 ? Ascreen : Screen" />
        投屏
      </div>
      <div class="tool-item" :class="{'a-tool-item':active === 2}" @click="hanleClick(2)">
        <img :src="active === 2 ? Aquestion : question" />
        抽问
      </div>
      <div v-show="quickPkShow && !isPreview" class="ai-tools-pk" @click="handleQuickPk">
        <img src="../../../../assets/ai-image/tools-img/pk-icon.png" />
      </div>
      <div v-if="sources[0].src" class="tool-help" :class="{'tool-help-wide' : isWideTools}" @click="openVideo">
        <img :src="Help" />
        帮助
      </div>
    </div>

    <div class="ai-tools-items" :class="{'ai-tools-items-wide' : isWideTools}">
      <Pk v-if="active === 0" ref="pk" :unit-id="unitId" @close="active = -1" @quickPkReload="_getAicourseUnitUser2" />
      <Time v-else-if="active === 1" @close="active = -1" />
      <lotterys v-else-if="active === 2" @close="active = -1" />
      <screeen v-else-if="active === 4" :unit-id="unitId" @close="active = -1" />
      <!-- <AiAssist ref="aiAssist" @close="active = -1" /> -->
      <AiAssistPlus ref="aiAssistPlus" @close="active = -1" />
      <!-- <acclaim v-if="active === 3" @close="active = -1" @startAnimate="startAnimate" /> -->
    </div>

    <div v-if="showAnimate" class="svga-box">
      <div v-if="showSvga" class="shadow-box"></div>
      <div id="svga-animate"></div>
      <audio ref="audioAnimate" controls="controls" hidden></audio>
    </div>

    <div v-if="showVideo" class="navbar-video">
      <div class="shadow"></div>
      <div class="video-container">
        <my-video :sources="sources" />
        <div class="close" @click.stop="closeVideo">
          <img :src="close" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SVGA from 'svgaplayerweb'
import pen from '@/assets/ai-image/tools-img/pen.png'
import Apen from '@/assets/ai-image/tools-img/pen-active.png'
import pk from '@/assets/ai-image/tools-img/pk.png'
import Apk from '@/assets/ai-image/tools-img/pk-active.png'
import time from '@/assets/ai-image/tools-img/time.png'
import Atime from '@/assets/ai-image/tools-img/time-active.png'
import question from '@/assets/ai-image/tools-img/question.png'
import Aquestion from '@/assets/ai-image/tools-img/question-active.png'
import hecai from '@/assets/ai-image/tools-img/hecai.png'
import Ahecai from '@/assets/ai-image/tools-img/hecai-active.png'
import Screen from '@/assets/ai-image/tools-img/screen.png'
import Ascreen from '@/assets/ai-image/tools-img/screen_active.png'
import Help from '@/assets/ai-image/tools-img/help.png'
import Aiassist from '@/assets/ai-image/tools-img/ai-assist.png'
import Time from './components/time.vue'
import Lotterys from './components/lotterys.vue'
import screeen from './components/screeen.vue'
import Pk from './components/pk.vue'
// import AiAssist from './components/AiAssist.vue'
import AiAssistPlus from './components/AiAssistPlus.vue'
// import Acclaim from './components/acclaim.vue'
import { getAicourseUnitUser2 } from '@/api/aicourse'
import dianzan from '@/assets/audio/dianzan.mp3'
import guzhang from '@/assets/audio/guzhang.mp3'
import close from '@/assets/ai-image/icon/video-close.svg'
import MyVideo from '@/components/video/bingoVideo.vue'
import { getConfig } from '@/api/aicourse'

export default {
  components: {
    Time,
    Lotterys,
    Pk,
    MyVideo,
    screeen,
    // AiAssist
    AiAssistPlus
  },
  props: {
    unitId: {
      type: [String, Number],
      default: 0
    }
  },
  data () {
    return {
      isWideTools: false,
      isDraw: false,
      quickPkShow: false,
      active: -1,
      Ascreen,
      close,
      Help,
      pen,
      Apen,
      pk,
      Apk,
      time,
      Atime,
      question,
      Aquestion,
      hecai,
      Ahecai,
      Screen,
      Aiassist,
      groupNum: 0,
      showAnimate: false,
      showSvga: false,
      svgaFiles: [
        { 'name': '点赞', 'url': 'https://static.bingotalk.cn/courses/courseware/63ec9da875703.svga', 'music': dianzan },
        { 'name': '鼓掌', 'url': 'https://static.bingotalk.cn/courses/courseware/63ec9f64e508a.svga', 'music': guzhang }
      ],
      isPreview: false,
      showVideo: false,
      sources: [
        {
          type: '',
          src: '',
          duration: 0
        }
      ]
    }
  },
  watch: {
    unitId: {
      handler (val) {
        if (val) {
          this._getAicourseUnitUser2()
        }
      }
    }
  },
  mounted () {
    this.isPreview = !!(this.$route.query && this.$route.query.preview)
    this.$bus.$on('click', () => {
      this.active = -1
    })
    setTimeout(() => {
      if (this.unitId) {
        this._getAicourseUnitUser2()
      }
    }, 0)
    this.prefetch()
    this.getVideoUrl()
  },
  methods: {
    async _getAicourseUnitUser2 () {
      const { data: { data }} = await getAicourseUnitUser2({
        studentCourseId: this.$route.params.studentCourseId,
        aicourseUnitId: this.unitId
      })
      if (data && data.groupNum) {
        this.groupNum = data.groupNum
        this.quickPkShow = true
      } else {
        this.quickPkShow = false
      }
    },
    hanleClick (val) {
      this.active = val
      if (val === 5) {
        this.$refs.aiAssistPlus.open()
      }
    },
    hanleDraw () {
      this.isDraw = !this.isDraw
      this.$emit('handleActive', this.isDraw)
    },
    closeDraw () {
      this.isDraw = false
    },
    handleTools (e) {
      if (!this.$refs.tools.contains(e.target)) this.active = -1
    },
    handleQuickPk () {
      this.active = 0
      this.$nextTick(() => {
        this.$refs.pk.active = 'pkDetail'
        this.$refs.pk.handelGroupItem(this.groupNum)
      })
    },
    startAnimate () {
      const self = this
      const randomIndex = Math.floor(Math.random() * this.svgaFiles.length)
      const item = this.svgaFiles[randomIndex]
      self.showAnimate = true
      this.$nextTick(() => {
        const audio = this.$refs.audioAnimate
        audio.volume = 0.1
        audio.src = item.music
        var parser = new SVGA.Parser('#svga-animate')
        var player = new SVGA.Player('#svga-animate')
        player.loops = 1
        player.onFinished(() => {
          self.showSvga = false
          self.showAnimate = false
          audio.pause()
          player.clear()
        })
        parser.load(
          item.url,
          function (videoItem) {
            self.showSvga = true
            audio.play()
            player.setContentMode('Fill')
            player.setVideoItem(videoItem)
            player.startAnimation()
          },
          function (error) {
            console.log(error)
          }
        )
      })
    },
    prefetch () {
      for (const img of this.svgaFiles) {
        const link = document.createElement('link')
        link.rel = 'prefetch'
        link.href = img.url
        document.head.appendChild(link)
      }
    },
    openVideo () {
      this.showVideo = true
    },
    closeVideo () {
      this.showVideo = false
    },
    async getVideoUrl () {
      // if (this.course.guideResource) {
      //   const data = this.course.guideResource
      //   const videoUrl = data.url
      //   const videoType = this.checkFileType(videoUrl)
      //   if (videoType === 'm3u8') {
      //     this.sources[0].type = 'application/x-mpegURL'
      //   } else {
      //     this.sources[0].type = ''
      //   }
      //   this.sources[0].src = videoUrl
      // } else {
      //   this.sources = [{
      //     type: '',
      //     src: '',
      //     duration: 0
      //   }]
      // }
      const params = {
        'configType': 'AI_HELP'
      }
      var response = await getConfig(params)
      if (+response.data.code === 200 && response.data.data.length > 0) {
        this.sources[0].src = response.data.data[0].keyValue
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-tools {
  position: absolute;
  z-index: 9999;
  right: 10px;
  top: 20%;

  .ai-tools-box {
    width: vh2(70);
    //height: vh2(420);
    height: vh2(300);
    background: rgba(255, 255, 255, 0.6);
    border: vh2(1) solid #FFFFFF;
    box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.25);
    border-radius: vh2(10);
    color: #333333;
    font-weight: 500;
    font-size: vh2(12);
    padding: vh2(20) 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
    position: relative;
    z-index: 1000000;
    flex-wrap: wrap;
    //overflow: hidden;
    .ai-tools-btn{
      position: absolute;
      bottom: vh2(15);
      width: vh2(15);
      height: vh2(15);
      cursor: pointer;
      z-index: 1000001;
      font-size: vh2(15);
    }
    .bg-filter {
      background: rgba(64, 183, 242, .61);
      filter: blur(13px);
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .a-tool-item {
      color: #2D9CDB;
    }

    .tool-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      position: relative;
      height: vh2(50);
      width: vh2(60);
      margin: vh2(5);
      img {
        width: vh2(24);
        height: vh2(24);
        margin-bottom: vh2(5);
      }
    }

    .tool-help {
      position: absolute;
      bottom: - vh2(34);
      width: vh2(70);
      height: vh2(30);
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      background: rgba(255, 255, 255, 0.60);
      cursor: pointer;

      img {
        width: vh2(15);
        height: vh2(15);
        margin-right: vh2(3);
        margin-top: - vh2(2);
      }
    }
    .tool-help-wide {
      width: vh2(140) !important;
    }
  }
  .ai-tools-box-wide {
    width: vh2(140) !important;
  }
  .ai-tools-items {
    position: absolute;
    right: vh2(85);
    top: 0;
    bottom: 0;
    // display: inline-block;
    display: flex;
    align-items: center;
  }
  .ai-tools-items-wide {
    right: vh2(160) !important;
  }
  .ai-tools-pk {
    position: absolute;
    right: vh2(1);
    top: vh2(-50);
    //bottom: vh2(420);
    display: inline-block;
    width: vh2(70);
    height: vh2(50);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    img {
      width: vh2(58);
      height: vh2(46);
    }
  }
  .svga-box{
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba($color: #000000, $alpha: 0.8);

    .shadow-box {
      width: 100%;
      height: 100%;
      // background-color: rgba($color: #000000, $alpha: 0.8);
    }

    #svga-animate {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }

}
.navbar-video {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999999;

    .shadow {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.2);
    }

    .video-container {
      position: absolute;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50%;
      width: vh2(633);
      height: vh2(415);
      z-index: 9999;

      .close {
        position: absolute;
        object-fit: contain;
        top: vh2(12);
        right: vh2(8);
        width: vh2(16);
        height: vh2(16);
        cursor: pointer;
        z-index: 99999;

        .close-icon {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
</style>
