<template>
  <div>
    <div v-if="['newPk', 'addPk'].indexOf(active) > -1" class="pk-bg" :class="active" :style="bgStyle" @click="rankTypeShow = false">
      <div v-if="active !== 'ranks'" class="t-title">
        <div>
          {{ title }}
        </div>
        <div class="pointer" @click="close"><i class="el-icon-error" style="color: #BDBDBD;"></i></div>
      </div>
      <template v-if="active === 'newPk'">
        <div class="t-content">
          <div class="w flex justify-between align-center f12 mb10">
            <div>选择分组</div>
            <div class="pointer flex align-center" @click="active = 'ranks'">
              <img class="rank-icon" src="../../../../../assets/ai-image/tools-img/rank-icon.png" />
              排行榜
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <div class="w new-btns">
            <div class="new-pk-btn" @click="active = 'addPk'">
              新建分组
            </div>

            <div v-for="item in courseHistoryGroupList" :key="item" class="new-pk-btn f12" @click="handelGroupItem(item)">
              分{{ item }}组
            </div>
          </div>
        </div>
      </template>
      <template v-else-if="active === 'addPk'">
        <div class="t-content add-new-box-a">
          <div class="w220 h flex flex-col justify-around">
            <div class="w flex justify-around">
              <div class="flex align-center">
                输入组数量：
              </div>
              <div class="flex align-center score-box">
                <i class="el-icon-remove pointer f22 color4f" @click="handleGroupNum('-')"></i>
                <el-input
                  v-model="groupNum"
                  class="mr5 ml5 w45 h30"
                  maxlength="2"
                  disabled
                  onkeyup="value=value.replace(/[^\d]/g,'')"
                />
                <i class="el-icon-circle-plus pointer f22 color4f" @click="handleGroupNum('+')"></i>
              </div>
            </div>

            <div class="f12 color82" style="text-align: center;">
              小组名称用蔬菜、水果、动物随机命名
            </div>

            <div class="new-pk-btn" @click="handleAddGroupSave">
              选好了
            </div>
          </div>
        </div>
      </template>

    </div>
    <div v-else class="pk-fiexd-bg-box">
      <div class="pk-fiexd-bg">
        <template v-if="active === 'pkDetail'">
          <div v-if="active !== 'ranks'" class="t-title-pkDetail">
            <div class="ml20">
              {{ title }}
            </div>
            <div class="close2" @click="close">
              <i class="el-icon-error" style="color: #fff;"></i>
            </div>
          </div>
          <div class="t-content">
            <div class="group-body box-border">
              <div v-for="(item, index) in pkDetailDate" :key="item.id" class="group-item">
                <div class="flex align-center w150 f16 fb-600">
                  <img class="headimg2" :src="iconName[item.icon].file" />
                  {{ item.name }}
                </div>
                <div>
                  <template v-if="item.studentList">
                    <el-popover
                      placement="bottom"
                      :visible-arrow="false"
                      width="200"
                      popper-class="poper-pk"
                      trigger="click"
                    >
                      <div class="flex flex-wrap">
                        <div v-for="(item, index) in item.studentList.split(',')" :key="index" class="m">
                          {{ item }}
                        </div>
                      </div>
                      <div slot="reference" class="flex w align-center pointer f16 fb-600">
                        <span>{{ item.studentList.split(',').length }}人</span>
                        <i class="el-icon-arrow-right"></i>
                      </div>
                    </el-popover>
                  </template>
                  <template v-else>
                    <div class="f16 fb-600">暂无成员</div>
                  </template>
                </div>
                <div class="flex align-center score-box2">
                  <div class="group-btn" @click="subRandom(index, '-1')">-1</div>
                  <div class="group-btn" @click="subRandom(index, '-2')">-2</div>
                  <div class="group-btn" @click="subRandom(index, '-5')">-5</div>
                  <el-input
                    v-model="item.score"
                    class="mr5 ml5 w60 h40"
                    maxlength="2"
                    disabled
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                  />
                  <div class="group-btn" @click="addRandom(index, '+1')">+1</div>
                  <div class="group-btn" @click="addRandom(index, '+2')">+2</div>
                  <div class="group-btn" @click="addRandom(index, '+5')">+5</div>
                </div>
              </div>
              <div v-if="!pkDetailDate" class=" flex h w align-center justify-center">
                体验课不支持分组
              </div>
            </div>
            <div class="group-btn-box">
              <div class="new-pk-btn2 mr10" @click="checkRank">
                <img class="mr5" :width="vh2(25)" src="../../../../../assets/ai-image/tools-img/rank-icon2.png" />
                查看排行
              </div>
              <div class="new-pk-btn2" @click="close">
                <img class="mr5" :width="vh2(25)" src="../../../../../assets/ai-image/tools-img/closePage.png" />
                关闭
              </div>
            </div>
          </div>
        </template>
        <template v-if="active === 'ranks'">
          <div class="t-content">
            <div class="rank-title relative">
              <img class="rank-master" src="../../../../../assets/ai-image/tools-img/rank-master.png" />
              <div></div>
              <div class="relative">
                <img :width="vh2(25)" src="../../../../../assets/ai-image/tools-img/rank-icon2.png" />
                <span style="text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);">
                  排行榜
                </span>
                <div class="fliter-box">
                  <div class="relative">
                    <div class="pointer" @click="rankTypeShow = !rankTypeShow">
                      {{ rankType }}<i :class="rankTypeShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                    </div>
                    <div v-show="rankTypeShow" class="rank-select">
                      <div class="r-item pointer" @click="rankType = '按本节排行'; rankTypeShow = false">
                        <div class="w30">
                          <img v-show="rankType === '按本节排行'" :width="vh2(20)" src="../../../../../assets/ai-image/tools-img/rank-right.png" />
                        </div>
                        <div>按本节排行</div>
                      </div>
                      <div class="r-item pointer" @click="rankType = '按总累计'; rankTypeShow = false">
                        <div class="w30">
                          <img v-show="rankType === '按总累计'" :width="vh2(20)" src="../../../../../assets/ai-image/tools-img/rank-right.png" />
                        </div>
                        <div>按总累计</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="pointer close2" @click="close">
                <i class="el-icon-error" style="color: #fff;"></i>
              </div>
            </div>
            <div class="rank-content flex">
              <div class="r-left">
                <div v-if="courseHasGroupList.length === 0" class="empty">
                  暂无数据
                </div>
                <div
                  v-for="item in courseHasGroupList"
                  :key="item"
                  class="group-name"
                  :class="{'group-name-active': +rankActiveGroupNum === +item}"
                  @click="handleRank(item)"
                >
                  {{ item }}组
                </div>
              </div>
              <div class="r-right">
                <el-table
                  :data="tableData"
                  :height="vh2(560)"
                  :header-cell-class-name="'pk-head-cell'"
                  :header-cell-style="{background: 'transparent', color: '#000000', border: 'none', fontSize: `${vh2(20)}px`, height: `${vh2(70)}px`}"
                  :header-row-style="{background: 'transparent', color: '#000000', border: 'none', fontSize: `${vh2(20)}px`, height: `${vh2(70)}px`}"
                  :cell-style="{background: 'transparent', color: '#000000', border: 'none', fontSize: `${vh2(20)}px`}"
                  :row-style="{background: 'transparent', color: '#000000', border: 'none', fontSize: `${vh2(20)}px`}"
                  stripe
                  style="width: 100%"
                >
                  <el-table-column
                    label="排名"
                    :width="vh2(80)"
                  >
                    <template slot-scope="scope">
                      <div class="flex align-center justify-center w20">
                        <template v-if="scope.$index + 1 === 1"><img :height="vh2(40)" :src="rank1" /></template>
                        <template v-else-if="scope.$index + 1 === 2"><img :height="vh2(40)" :src="rank2" /></template>
                        <template v-else-if="scope.$index + 1 === 3"><img :height="vh2(40)" :src="rank3" /></template>
                        <template v-else>
                          <div class="ranking-num f16">{{ scope.$index + 1 }}</div>
                        </template>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="组员"
                    :width="vh2(280)"
                  >
                    <template slot-scope="scope">
                      <div class="flex w align-center f16 fb-600">
                        <img class="headimg" :src="iconName[scope.row.icon].file" alt="" />
                        <span>{{ scope.row.name }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="成员"
                    :width="vh2(140)"
                  >
                    <template slot-scope="scope">
                      <template v-if="scope.row.studentList">
                        <el-popover
                          placement="bottom"
                          :visible-arrow="false"
                          :width="vh2(200)"
                          popper-class="poper-pk"
                          trigger="click"
                        >
                          <div class="flex flex-wrap">
                            <div v-for="(item, index) in scope.row.studentList.split(',')" :key="index" class="m">
                              {{ item }}
                            </div>
                          </div>
                          <div slot="reference" class="flex w align-center f16 pointer fb-600">
                            <span>{{ scope.row.studentList.split(',').length }}人</span>
                            <i class="el-icon-arrow-right"></i>
                          </div>
                        </el-popover>
                      </template>
                      <template v-else>
                        暂无成员
                      </template>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="累计缤果币"
                  >
                    <template slot-scope="scope">
                      <div class="flex w align-center f16 fb-600">
                        <img class="img2 mr10" src="../../../../../assets/ai-image/tools-img/binguocoin.png" />
                        {{ scope.row.totalScore }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="本节缤果币"
                  >
                    <template slot-scope="scope">
                      <div class="flex w align-center f16 fb-600">
                        <img class="img2 mr10" src="../../../../../assets/ai-image/tools-img/binguocoin.png" />
                        {{ scope.row.score }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div class="group-btn-box">
              <div class="new-pk-btn2 mr10" @click="checkPK">
                <img class="mr5" :width="vh2(20)" src="../../../../../assets/ai-image/tools-img/BACK.png" />
                返回
              </div>
              <div class="new-pk-btn2" @click="close">
                <img class="mr5" :width="vh2(25)" src="../../../../../assets/ai-image/tools-img/closePage.png" />
                关闭
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import rank1 from '../../../../../assets/ai-image/tools-img/rank-1.png'
import rank2 from '../../../../../assets/ai-image/tools-img/rank-2.png'
import rank3 from '../../../../../assets/ai-image/tools-img/rank-3.png'
import { getLessonGroup, groupLesson, changeGroupScore, aiLessonRank } from '@/api/aicourse'
import { name } from '@/utils/group.js'

export default {
  props: {
    unitId: {
      type: [String, Number],
      default: 0
    }
  },
  data () {
    return {
      active: 'newPk',
      groupNum: 2,
      num: 1,
      tableData: [],
      pkDetailDate: [],
      courseHasGroupList: [],
      courseHistoryGroupList: [],
      rank1,
      rank2,
      rank3,
      iconName: name,
      rankActiveGroupNum: 0,
      rankType: '按本节排行',
      rankTypeShow: false,
      isFromCheckRank: false // 是否点击了查看排行
    }
  },
  computed: {
    title () {
      let str = ''
      switch (this.active) {
        case 'newPk':
          str = ''
          break
        case 'pkDetail':
          str = `分组PK（${(this.pkDetailDate && this.pkDetailDate.length) || '-'}组）`
          break
        case 'addPk':
          str = '新建分组'
          break
      }
      return str
    },
    bgStyle () {
      let obj = {}
      switch (this.active) {
        case 'newPk':
          obj = {}
          break
        case 'pkDetail':
        case 'addPk':
          obj = {
            width: this.vh2(350),
            height: this.vh2(285)
          }
          break
        case 'ranks':
          obj = {
            width: this.vh2(730),
            height: this.vh2(495)
          }
          break
      }
      return obj
    }
  },
  watch: {
    active: {
      async handler (val) {
        if (val === 'ranks') {
          await this._getLessonGroup()
          this._aiLessonRank()
        }
      }
    },
    rankType: {
      async handler () {
        await this._getLessonGroup()
        this._aiLessonRank()
      }
    }
  },
  mounted () {
    setTimeout(() => {
      this._getLessonGroupHistory()
      this._getLessonGroup()
    }, 0)
  },
  methods: {
    stop () {},
    close () {
      this.$emit('close')
    },
    async _getLessonGroupHistory () {
      const { data: { data }} = await getLessonGroup({
        studentCourseId: this.$route.params.studentCourseId
      })
      const arr = []
      data.forEach(element => {
        arr.push(element.groupNum)
      })
      this.courseHistoryGroupList = Array.from(new Set(arr)).sort((a, b) => {
        return +a - +b
      })
    },
    async _getLessonGroup () {
      const { data: { data }} = await getLessonGroup({
        studentCourseId: this.$route.params.studentCourseId,
        lessonId: this.rankType === '按本节排行' ? this.unitId : 0
      })
      const arr = []
      data.forEach(element => {
        arr.push(element.groupNum)
      })
      this.courseHasGroupList = Array.from(new Set(arr)).sort((a, b) => {
        return +a - +b
      })
      if (this.isFromCheckRank) {
        this.rankActiveGroupNum = this.pkDetailDate.length
        this.isFromCheckRank = false
      } else {
        this.rankActiveGroupNum = Number(this.courseHasGroupList[0]) || -1
      }
    },
    async _aiLessonRank () {
      const { data: { data }} = await aiLessonRank({
        studentCourseId: this.$route.params.studentCourseId,
        unitId: this.unitId,
        groupNum: this.rankActiveGroupNum,
        totalSort: this.rankType === '按本节排行' ? 0 : 1
      })
      this.tableData = data
    },
    handleRank (item) {
      this.rankActiveGroupNum = item
      this._aiLessonRank()
    },
    handleGroupNum (type) {
      if (type === '-' && this.groupNum > 2) {
        this.groupNum--
      } else if (type === '+' && this.groupNum < 12) {
        this.groupNum++
      }
    },
    async handleAddGroupSave () {
      // 0 蔬菜 1水果 2动物
      const num = Math.floor(Math.random() * 3)
      const names = ['vegetb', 'fruit', 'animal'][num]
      const arr = []
      const items = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
      const randomArr = this.getRandomArrayElements(items, Number(this.groupNum))
      randomArr.map((val, index) => {
        const { icon, name } = this.iconName[names + val]
        arr.push({
          icon,
          name: `${index + 1}组：${name}`,
          lessonId: this.unitId,
          studentCourseId: this.$route.params.studentCourseId,
          groupNum: this.groupNum
        })
      })
      const { data: { data }} = await groupLesson(arr)
      this.pkDetailDate = data
      this.active = 'pkDetail'
      this._getLessonGroup()
      this.groupNum = 2
      this.$emit('quickPkReload')
    },
    getRandomArrayElements (arr, count) {
      const shuffled = arr.slice(0); let i = arr.length; const min = i - count; let temp; let index
      while (i-- > min) {
        index = Math.floor((i + 1) * Math.random())
        temp = shuffled[index]
        shuffled[index] = shuffled[i]
        shuffled[i] = temp
      }
      return shuffled.slice(min)
    },
    async handelGroupItem (item) {
      const { data: { data }} = await getLessonGroup({
        studentCourseId: this.$route.params.studentCourseId,
        lessonId: this.unitId,
        groupNum: item
      })
      this.active = 'pkDetail'
      this.pkDetailDate = data
      this.$emit('quickPkReload')
    },
    async addRandom (index, num) {
      const { data: { data }} = await changeGroupScore({
        lessonId: this.pkDetailDate[index].lessonId,
        lessonGroupId: this.pkDetailDate[index].id,
        studentCourseId: this.pkDetailDate[index].studentCourseId,
        groupNum: this.pkDetailDate[index].groupNum,
        change: num
      })
      this.pkDetailDate = data
    },
    async subRandom (index, num) {
      if (+this.pkDetailDate[index].score > 0) {
        const { data: { data }} = await changeGroupScore({
          lessonId: this.pkDetailDate[index].lessonId,
          lessonGroupId: this.pkDetailDate[index].id,
          studentCourseId: this.pkDetailDate[index].studentCourseId,
          groupNum: this.pkDetailDate[index].groupNum,
          change: num
        })
        this.pkDetailDate = data
      }
    },
    checkRank () {
      this.active = 'ranks'
      this.isFromCheckRank = true
    },
    checkPK () {
      this.active = 'pkDetail'
    },
    vh2 (px) {
      return px * document.body.clientHeight / 965
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";

.fb-600 {
  font-weight: 600;
}
.pk-bg {
  background: rgba(255, 255, 255, 0.95);
  border: vh2(1) solid #FFFFFF;
  box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.25);
  border-radius: vh2(10);
  width: vh2(154);
  height: vh2(280);
  padding: vh2(10) vh2(8);
  box-sizing: border-box;
  color: #000;
  font-size: vh2(12);
  font-weight: 500;
  position: relative;
}
.addPk {
  width: vh2(240) !important;
}
.el-icon-error {
  font-size: vh2(40);
  color: rgba(255, 255, 255, 0.8);
}
.t-title {
  .el-icon-error {
    font-size: 20px;
  }
}
.pk-fiexd-bg-box {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0,0,0,0.72);

  .pk-fiexd-bg {
    background: rgba(255, 255, 255, 0.95);
    border: vh2(1) solid #FFFFFF;
    box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.25);
    border-radius: vh2(10);
    // padding: vh2(15) vh2(10) vh2(10) vh2(10);
    box-sizing: border-box;
    color: #fff;
    font-size: vh2(18);
    width: vh2(730);
    height: vh2(500);

    .t-title-pkDetail {
      height: vh2(50);
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(90deg, #E55D87 0%, #5FC3E4 100%);
      border-radius: vh2(10) vh2(10) 0px 0px;
      box-sizing: border-box;
      padding: 0 vh2(10);
      font-size: vh2(16);
    }
  }
}

.headimg {
  border-radius: 50%;
  width: vh2(30);
  height: vh2(30);
  margin-right: vh2(5);
}

.headimg2 {
  border-radius: 50%;
  width: vh2(30);
  height: vh2(30);
  margin-right: vh2(5);
}

.bg-img {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  z-index: -1;
}
.rank-icon {
  width: vh2(15);
  height: vh2(15);
  margin-right: vh2(3);
}

.t-title {
  height: vh2(20);
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: vh2(5);
  img {
    width: vh2(15);
    height: vh2(15);
    cursor: pointer;
  }
}

.t-content {
  width: 100%;

  .new-btns {
    height: vh2(210);
    overflow-y: auto;
    @include aiPkScrollBar;
  }

  .new-pk-btn {
    background: rgba(251, 237, 150, 0.3);
    border: vh2(1) solid rgba(0, 0, 0, 0.3);
    box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.05);
    border-radius: vh2(12);
    margin: 0 auto;
    width: vh2(120);
    height: vh2(34);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-bottom: vh2(10);
    &:last-child {
      margin-bottom: 0;
    }
  }

  .new-pk-btn2 {
    background: linear-gradient(83.59deg, #E55D87 5.05%, #5FC3E4 60.36%);
    // border: vh2(1) solid rgba(0, 0, 0, 0.3);
    // box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.05);
    border-radius: vh2(10);
    // margin: 0 auto;
    width: vh2(120);
    height: vh2(34);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-bottom: vh2(10);
    // &:last-child {
    //   margin-bottom: 0;
    // }
  }

  .group-btn-box {
    width: 100%;
    height: vh2(50);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .group-body {
    width: 100%;
    overflow-y: auto;
    height: vh2(400);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: vh2(30) vh2(30) 0 vh2(30);
    color: #000;

    .group-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: vh2(10);
      padding-left: 0;
      box-sizing: border-box;
    }

    .group-btn {
      width: vh2(35);
      height: vh2(35);
      color: #333333;
      font-size: vh2(14);
      background: #F2C94C;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      margin: 0 vh2(2);
      cursor: pointer;
      font-weight: 600;
    }
  }

  .rank-title {
    height: vh2(50);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(90deg, #E55D87 0%, #5FC3E4 100%);
    border-radius: vh2(10) vh2(10) 0px 0px;
    box-sizing: border-box;
    padding: 0 vh2(10);

    .rank-master {
      position: absolute;
      left: vh2(100);
      bottom: 0;
      height: vh2(45);
    }

    .fliter-box {
      position: absolute;
      left: vh2(200);
      bottom: vh2(2);
      width: vh2(120);
      font-size: vh2(12);
    }

    .rank-select {
      position: absolute;
      left: -vh2(40);
      top: vh2(20);
      background: rgba(255, 255, 255, 0.95);
      border: vh2(1) solid #FFFFFF;
      box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.25);
      border-radius: vh2(10);
      width: vh2(150);
      height: vh2(92);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: vh2(10);
      box-sizing: border-box;
      font-size: vh2(12);
      z-index: 99;
      color: #000;

      .r-item {
        width: 100%;
        height: vh2(30);
        display: flex;
        align-items: center;
      }
    }
  }

  .rank-content {
    height: vh2(400);
    padding: vh2(20) vh2(10) 0 vh2(10);
    box-sizing: border-box;

    .empty {
      font-size: 14px;
      color: #909399;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 80%;
    }

    .r-left {
      border: vh2(1) solid #E0E0E0;
      border-radius: vh2(10);
      width: vh2(85);
      height: 100%;
      overflow-y: auto;
      padding: vh2(12) 0;
      box-sizing: border-box;

      .group-name {
        width: vh2(61);
        height: vh2(37);
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        margin-bottom: vh2(15);
        cursor: pointer;
        color: #000000;
        font-size: vh2(14);
        &:last-child {
          margin-bottom: 0;
        }
      }
      .group-name-active {
        background: linear-gradient(90deg, #77A1D3 0%, #79CBCA 50%, #E684AE 100%);
        border-radius: vh2(10);
        color: #fff;
      }
    }
    .r-right {
      padding: 0 0 0 vh2(40);
      box-sizing: border-box;
      width: calc(100% - #{vh2(100)});
      height: 100%;
      overflow-y: auto;
      color: #000000;
      ::v-deep .el-table {
        background: transparent !important;
        color: #000000 !important;
        &::before {
          height: 0;
        }
      }
      ::v-deep .el-table th.el-table__cell {
        background: transparent !important;
      }
      ::v-deep .el-table td.gutter {
        background: transparent !important;
      }
      ::v-deep .el-table th.gutter {
        background: transparent !important;
      }
      ::v-deep .pk-head-cell {
        .cell {
          height: 100% !important;
          display: flex;
          align-items: center;
        }
      }
    }
  }
  .score-box {
    height: vh2(30);
    img {
      cursor: pointer;
    }
    ::v-deep .el-input__inner {
      height: vh2(30) !important;
      line-height: vh2(30) !important;
      outline: 0;
      background-color: #FFFFFF !important;
      color: #000000 !important;
      padding: 0 vh2(8);
      border: 1px solid #828282 !important;
      text-align: center;
      font-size: vh2(18);
      font-weight: 600;
    }
  }

  .score-box2 {
    height: vh2(50);
    img {
      cursor: pointer;
    }
    ::v-deep .el-input__inner {
      height: vh2(30) !important;
      line-height: vh2(30) !important;
      outline: 0;
      background-color: #FFFFFF !important;
      color: #000000 !important;
      padding: 0 vh2(8);
      border: 1px solid #828282 !important;
      text-align: center;
      font-size: vh2(18);
      font-weight: 600;
    }
  }
}
.close2 {
  align-self: flex-start;
  margin-top: vh2(5);
  cursor: pointer;
}

.img1 {
  width: vh2(14);
}
.img2 {
  width: vh2(17);
}

.f12 {
  font-size: vh2(12);
}

.f14 {
  font-size: vh2(14);
}

.f16 {
  font-size: vh2(14);
}

.f18 {
  font-size: vh2(18);
}

.f22 {
  font-size: vh2(22);
}

.pt10 {
  padding-top: vh2(10);
}

.m {
  margin: 0 vh2(10) vh2(10) 0;
}

.mr10 {
  margin-right: vh2(10);
}

.mr5 {
  margin-right: vh2(5);
}
.ml5 {
  margin-left: vh2(5);
}
.mb10 {
  margin-bottom: vh2(10);
}

.ml20 {
  margin-left: vh2(20);
}

.w45 {
  width: vh2(45);
}

.w30 {
  width: vh2(30);
}
.w20 {
  width: vh2(20);
}
.w60 {
  width: vh2(60);
}
.w220 {
  width: vh2(220);
}

.w150 {
  width: vh2(150);
}
.h30 {
  height: (30);
}
.h40 {
  height: (40);
}

.color4f {
  color: #4F4F4F;
}

.color82 {
  color: #828282;
}

.add-new-box-a {
  width: vh2(240);
  height: calc(100% - #{vh2(40)});
}
</style>

<style lang="scss">
.poper-pk {
  z-index: 99999 !important;
}
</style>
