<template>
  <div :class="{'time-bg': activeType==='init', 'time-bg2': activeType!=='init'}">
    <audio ref="endAu" controls="controls" hidden :src="endAu"></audio>
    <audio ref="didaAu" controls="controls" hidden :src="didaAu"></audio>
    <template v-if="activeType === 'init'">
      <div class="t-title">
        <div>选择倒计时</div>
        <div @click="closeTime">
          <i class="el-icon-error" style="color: #BDBDBD;"></i>
        </div>
      </div>
      <div class="w flex flex-wrap">
        <div v-for="item in timeFrom" :key="item.num" class="t-item" @click="quickStart(item)">{{ item.name }}</div>
        <div class="t-item" @click="activeType = 'custom'">自定义</div>
      </div>
    </template>
    <template v-else-if="activeType === 'custom'">
      <div class="t-title">
        <div>定时器</div>
        <div @click="closeTime">
          <i class="el-icon-error" style="color: #BDBDBD;"></i>
        </div>
      </div>
      <div class="square-box">
        <div class="square">
          <i class="icon-xiangshang" @click="handelChange(0, 5, '+')"></i>
          <div class="border">{{ setTimeObj[0] }}</div>
          <i class="icon-xiangxia" @click="handelChange(0, 5, '-')"></i>
        </div>
        <div class="square">
          <i class="icon-xiangshang" @click="handelChange(1, 9, '+')"></i>
          <div class="border">{{ setTimeObj[1] }}</div>
          <i class="icon-xiangxia" @click="handelChange(1, 9, '-')"></i>
        </div>
        <div class="semicolon">:</div>
        <div class="square">
          <i class="icon-xiangshang" @click="handelChange(2, 5, '+')"></i>
          <div class="border">{{ setTimeObj[2] }}</div>
          <i class="icon-xiangxia" @click="handelChange(2, 5, '-')"></i>
        </div>
        <div class="square">
          <i class="icon-xiangshang" @click="handelChange(3, 9, '+')"></i>
          <div class="border">{{ setTimeObj[3] }}</div>
          <i class="icon-xiangxia" @click="handelChange(3, 9, '-')"></i>
        </div>
      </div>

      <div class="w flex justify-around">
        <div class="t-item2" @click="activeType = 'init'">选择时间</div>
        <div class="t-item2" @click="startTime">开始计时</div>
      </div>
    </template>
    <template v-else-if="activeType === 'ongoing'">
      <div class="t-title">
        <div>定时器</div>
        <div @click="closeTime">
          <i class="el-icon-error" style="color: #BDBDBD;"></i>
        </div>
      </div>
      <div class="square-box">
        <div class="square">
          <div class="border">{{ getTimeObj[0] }}</div>
        </div>
        <div class="square">
          <div class="border">{{ getTimeObj[1] }}</div>
        </div>
        <div class="semicolon">:</div>
        <div class="square">
          <div class="border">{{ getTimeObj[2] }}</div>
        </div>
        <div class="square">
          <div class="border">{{ getTimeObj[3] }}</div>
        </div>
      </div>

      <div class="w flex justify-around">
        <div class="t-item" @click="reStartTime">重置</div>
        <div class="t-item" @click="closeTime">结束</div>
      </div>
    </template>
    <template v-else-if="activeType === 'end'">
      <div class="t-title">
        <div>定时器</div>
        <div @click="closeTime">
          <i class="el-icon-error" style="color: #BDBDBD;"></i>
        </div>
      </div>
      <div class="w flex justify-center align-center" style="font-size: 20px;height:60px;">
        计时结束
      </div>
      <div class="w flex justify-around">
        <div class="t-item2" @click="reStartTime">重新计时</div>
      </div>
    </template>
  </div>
</template>

<script>
import moment from 'moment'
import endAu from '@/assets/audio/time/time-end.mp3'
import didaAu from '@/assets/audio/time/dida.mp3'
export default {
  data () {
    return {
      timeFrom: [
        {
          name: '10秒',
          num: 10,
          setTimeObj: {
            0: 0,
            1: 0,
            2: 1,
            3: 0
          }
        },
        {
          name: '20秒',
          num: 20,
          setTimeObj: {
            0: 0,
            1: 0,
            2: 2,
            3: 0
          }
        },
        {
          name: '30秒',
          num: 30,
          setTimeObj: {
            0: 0,
            1: 0,
            2: 3,
            3: 0
          }
        },
        {
          name: '40秒',
          num: 40,
          setTimeObj: {
            0: 0,
            1: 0,
            2: 4,
            3: 0
          }
        },
        {
          name: '50秒',
          num: 50,
          setTimeObj: {
            0: 0,
            1: 0,
            2: 5,
            3: 0
          }
        },
        {
          name: '1分钟',
          num: 60,
          setTimeObj: {
            0: 0,
            1: 1,
            2: 0,
            3: 0
          }
        },
        {
          name: '2分钟',
          num: 120,
          setTimeObj: {
            0: 0,
            1: 2,
            2: 0,
            3: 0
          }
        },
        {
          name: '3分钟',
          num: 180,
          setTimeObj: {
            0: 0,
            1: 3,
            2: 0,
            3: 0
          }
        },
        {
          name: '5分钟',
          num: 300,
          setTimeObj: {
            0: 0,
            1: 5,
            2: 0,
            3: 0
          }
        },
        {
          name: '10分钟',
          num: 600,
          setTimeObj: {
            0: 1,
            1: 0,
            2: 0,
            3: 0
          }
        }
      ],
      activeType: 'init',
      setTimeObj: {
        0: 0,
        1: 0,
        2: 0,
        3: 0
      },
      getTimeObj: {
        0: 0,
        1: 0,
        2: 0,
        3: 0
      },
      tInter: null,
      timeInfo: '',
      endAu,
      didaAu
    }
  },
  methods: {
    quickStart (item) {
      this.timeInfo = `${moment().add(1, 's')}|${item.num}`
      this.getTimeObj[0] = item.setTimeObj[0]
      this.getTimeObj[1] = item.setTimeObj[1]
      this.getTimeObj[2] = item.setTimeObj[2]
      this.getTimeObj[3] = item.setTimeObj[3]
      this.startInter()
      this.activeType = 'ongoing'
    },
    startTime () {
      const arr = Object.values(this.setTimeObj)
      const min = `${arr[0]}${arr[1]}`
      const s = `${arr[2]}${arr[3]}`
      const time = Number(min) * 60 + Number(s)
      const num = arr.reduce((p, c) => {
        return p + c
      })
      this.timeInfo = `${moment().add(1, 's')}|${time}`
      if (num) {
        this.getTimeObj[0] = this.setTimeObj[0]
        this.getTimeObj[1] = this.setTimeObj[1]
        this.getTimeObj[2] = this.setTimeObj[2]
        this.getTimeObj[3] = this.setTimeObj[3]
        this.startInter()
        this.activeType = 'ongoing'
      } else {
        this.$message.error('请设置时间!')
      }
    },
    startInter () {
      if (this.tInter) {
        clearInterval(this.tInter)
      }
      this.simpleTime = true
      this.tInter = setInterval(() => {
        const info = this.timeInfo.split('|')

        const startTime = new Date().getTime()
        let endTime = moment(info[0]).add(info[1], 's')
        endTime = new Date(endTime).getTime()
        const diffTime = endTime - startTime
        const endAu = this.$refs.endAu
        endAu.volume = 0.1
        const didaAu = this.$refs.didaAu
        didaAu.volume = 0.1
        if (diffTime < 1000 && diffTime > 0) {
          endAu.currentTime = 0
          endAu.play()
        }

        if (diffTime < 6000 && diffTime > 5000) {
          didaAu.currentTime = 0
          didaAu.play()
        }
        if (diffTime < 0) {
          clearInterval(this.tInter)
          this.activeType = 'end'
          this.reset()
        }
        // 分钟秒数
        // const h = parseInt(diffTime % (1000 * 60 * 60 * 24) / (1000 * 60 * 60)) // 时
        const m = parseInt(diffTime % (1000 * 60 * 60) / (1000 * 60)) // 分钟
        const s = parseInt(diffTime % (1000 * 60) / 1000) // 秒数
        if (m < 10) {
          this.getTimeObj[0] = 0
          this.getTimeObj[1] = Number(m)
        } else {
          const str = m + ''
          const arr = str.split('')
          this.getTimeObj[0] = Number(arr[0])
          this.getTimeObj[1] = Number(arr[1])
        }
        if (s < 10) {
          this.getTimeObj[2] = 0
          this.getTimeObj[3] = Number(s)
        } else {
          const str = s + ''
          const arr = str.split('')
          this.getTimeObj[2] = Number(arr[0])
          this.getTimeObj[3] = Number(arr[1])
        }
      }, 1000)
    },
    reStartTime () {
      if (this.tInter) {
        clearInterval(this.tInter)
      }
      const endAu = this.$refs.endAu
      endAu.volume = 0.1
      const didaAu = this.$refs.didaAu
      didaAu.volume = 0.1
      this.pausedAudio(endAu)
      this.pausedAudio(didaAu)
      this.simpleTime = false
      this.activeType = 'custom'
      this.setTimeObj = {
        0: 0,
        1: 0,
        2: 0,
        3: 0
      }
    },
    closeTime () {
      if (this.tInter) {
        clearInterval(this.tInter)
      }
      const endAu = this.$refs.endAu
      endAu.volume = 0.1
      const didaAu = this.$refs.didaAu
      didaAu.volume = 0.1
      this.pausedAudio(endAu)
      this.pausedAudio(didaAu)
      this.reset()
      this.$emit('close')
    },
    pausedAudio (name) {
      if (!name.paused) {
        name.pause()
      }
    },
    reset () {
      this.simpleTime = false
      this.timerShow = false
      this.setTimeObj = {
        0: 0,
        1: 0,
        2: 0,
        3: 0
      }
    },
    handelChange (index, max, type) {
      switch (type) {
        case '+':
          if (this.setTimeObj[index] === max) {
            this.setTimeObj[index] = max
          } else {
            this.setTimeObj[index] = this.setTimeObj[index] + 1
          }
          break
        case '-':
          if (this.setTimeObj[index] === 0) {
            this.setTimeObj[index] = 0
          } else {
            this.setTimeObj[index] = this.setTimeObj[index] - 1
          }
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@function vh2($px) {
  @return $px * 100vh / 650;
}
.el-icon-error {
  font-size: vh2(20);
  cursor: pointer;
}

.time-bg, .time-bg2 {
  width: vh2(220);
  padding: vh2(10);
  box-sizing: border-box;
  color: #000;
  font-size: vh2(12);
  background: rgba(255, 255, 255, 0.95);
  border: vh2(1) solid #FFFFFF;
  box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.25);
  border-radius: vh2(10);
}

.time-bg2 {
  height: vh2(170);
  background-size: cover;
}
.time-bg {
  height: vh2(245);
  background-size: cover;
}

.t-title {
  height: vh2(20);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: vh2(20);
  img {
    width: vh2(18);
    height: vh2(18);
    cursor: pointer;
  }
}

.t-item {
  width: 29%;
  height: vh2(30);
  background: rgba(251, 237, 150, 0.3);
  border: vh2(1) solid rgba(0, 0, 0, 0.3);
  box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.05);
  border-radius: vh2(12);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: vh2(15);
  cursor: pointer;
  margin-right: 6px;
}

.t-item2 {
  width: vh2(80);
  height: vh2(30);
  background: rgba(251, 237, 150, 0.3);
  border: vh2(1) solid rgba(0, 0, 0, 0.3);
  box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.05);
  border-radius: vh2(12);
  display: flex;
  justify-content: center;
  align-items: center;
  // margin-bottom: 20px;
  cursor: pointer;
}

.square-box {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  margin-bottom: vh2(10);

  .square {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: vh2(70);
    width: vh2(30);
    color: #000000;
    .iconfont {
      cursor: pointer;
      font-size: vh2(8);
      &:hover {
        color: #575B66;
      }
    }

    .border {
      width: 100%;
      height: vh2(40);
      background: rgba(251, 237, 150, 0.3);
      border: vh2(1) solid rgba(0, 0, 0, 0.3);
      border-radius: vh2(5);
      font-weight: 600;
      font-size: vh2(30);
      color: #000;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .semicolon {
    font-weight: 500;
    font-size: vh2(20);
    color: #000;
    align-self: center;
  }

  .mr10 {
    margin-right: vh2(10);
  }

  .icon-xiangshang {
    border: vh2(5) solid transparent;
    border-bottom: vh2(5) solid #000;
    cursor: pointer;
    &:hover {
      border-bottom-color: rgba($color: #000000, $alpha: 0.7);
    }
  }

  .icon-xiangxia {
    border: vh2(5) solid transparent;
    border-top: vh2(5) solid #000;
    cursor: pointer;
    &:hover {
      border-top-color: rgba($color: #000000, $alpha: 0.7);
    }
  }
}
</style>
