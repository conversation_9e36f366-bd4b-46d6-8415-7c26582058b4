<template>
  <div class="pk-bg">
    <div class="t-title">
      <div class="pointer" @click="close"><i class="el-icon-error"></i></div>
    </div>
    <div class="t-content">
      <div class="w flex justify-between align-center f12 mb10">
        <div>选择效果</div>
      </div>
      <div class="choose-list">
        <div v-for="item in chooseList" :key="item.name" class="choose-item" @click="startAnimate(item)">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import dianzan from '@/assets/audio/dianzan.mp3'
import guzhang from '@/assets/audio/guzhang.mp3'
export default {
  data () {
    return {
      chooseList: [
        { 'name': '点赞', 'url': 'https://static.bingotalk.cn/courses/courseware/63ec9da875703.svga', 'music': dianzan },
        { 'name': '鼓掌', 'url': 'https://static.bingotalk.cn/courses/courseware/63ec9f64e508a.svga', 'music': guzhang }
      ]
    }
  },
  methods: {
    close () {
      this.$emit('close')
    },
    startAnimate (item) {
      this.$emit('startAnimate', item)
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.pk-bg {
  background: rgba(255, 255, 255, 0.95);
  border: vh2(1) solid #FFFFFF;
  box-shadow: 0px vh2(4) vh2(4) rgba(0, 0, 0, 0.25);
  border-radius: vh2(10);
  width: vh2(154);
  height: vh2(280);
  padding: vh2(10) vh2(8);
  box-sizing: border-box;
  color: #000;
  font-size: vh2(12);
  font-weight: 500;
  position: relative;
}
.el-icon-error {
  font-size: vh2(20);
  color: #BDBDBD;
}

.t-title {
  height: vh2(20);
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: vh2(5);

  img {
    width: vh2(15);
    height: vh2(15);
    cursor: pointer;
  }

}

.t-content {
  width: 100%;

  .choose-list {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 10px;
  }

  .choose-item {
    width: 100%;
    height: vh2(54);
    background: rgba(251, 237, 150, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.3);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    color: #000000;
    font-weight: 500;
    font-size: vh2(12);
    line-height: vh2(54);
    text-align: center;
    cursor: pointer;
  }
}

.f12 {
  font-size: vh2(12);
}

.f14 {
  font-size: vh2(14);
}

.f16 {
  font-size: vh2(14);
}

.f18 {
  font-size: vh2(18);
}

.f22 {
  font-size: vh2(22);
}

.pt10 {
  padding-top: vh2(10);
}

.m {
  margin: 0 vh2(10) vh2(10) 0;
}

.mr10 {
  margin-right: vh2(10);
}

.mr5 {
  margin-right: vh2(5);
}
.ml5 {
  margin-left: vh2(5);
}
.mb10 {
  margin-bottom: vh2(10);
}

.ml20 {
  margin-left: vh2(20);
}

.w45 {
  width: vh2(45);
}

.w30 {
  width: vh2(30);
}
.w20 {
  width: vh2(20);
}
.w60 {
  width: vh2(60);
}
.w220 {
  width: vh2(220);
}

.w150 {
  width: vh2(150);
}
.h30 {
  height: (30);
}
.h40 {
  height: (40);
}

.color4f {
  color: #4F4F4F;
}

.color82 {
  color: #828282;
}
</style>
