<template>
  <div class="draw-box">
    <canvas
      id="canvas"
      :width="client.width"
      :height="client.height"
      @mousedown="canvasDown($event)"
      @mouseup="canvasUp($event)"
      @mousemove="canvasMove($event)"
      @touchstart="canvasDown($event)"
      @touchend="canvasUp($event)"
      @touchmove="canvasMove($event)"
    >
    </canvas>
    <div class="draw-tools">
      <div class="clear-box flex justify-between align-center">
        <div class="pointer" @click="clearDraw">清屏</div>
        <img class="pointer" width="20" src="../../../../../assets/ai-image/icon/revoke.svg" @click="revoke" />
      </div>
      <div class="mt10 flex align-center justify-center">
        <div class="mr10 brush brush-1" :class="{'brush-active':brushIndex === 1}" @click="brushChange(1)"></div>
        <div class="mr10 brush brush-2" :class="{'brush-active':brushIndex === 2}" @click="brushChange(2)"></div>
        <div class="mr10 brush brush-3" :class="{'brush-active':brushIndex === 3}" @click="brushChange(3)"></div>
      </div>
      <div class="flex flex-wrap justify-around">
        <div class="color-box">
          <div class="mt10 colors colors-1" :class="{'colors-active':colorIndex === 1}" @click="colorChange(1)"></div>
        </div>
        <div class="color-box">
          <div class="mt10 colors colors-2" :class="{'colors-active':colorIndex === 2}" @click="colorChange(2)"></div>
        </div>
        <div class="color-box">
          <div class="mt10 colors colors-3" :class="{'colors-active':colorIndex === 3}" @click="colorChange(3)"></div>
        </div>
        <div class="color-box">
          <div class="mt10 colors colors-4" :class="{'colors-active':colorIndex === 4}" @click="colorChange(4)"></div>
        </div>
        <div class="color-box">
          <div class="mt10 colors colors-5" :class="{'colors-active':colorIndex === 5}" @click="colorChange(5)"></div>
        </div>
        <div class="color-box">
          <div class="mt10 colors colors-6" :class="{'colors-active':colorIndex === 6}" @click="colorChange(6)"></div>
        </div>
      </div>
      <div class="w flex justify-center">
        <div class="close-draw" @click="$emit('closeDraw')"><i class="el-icon-close"></i>关闭画笔</div>
      </div>
      <div class="w flex justify-center">
        <div class="tips-draw">操作其他先关闭画笔</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      colors: ['#EB4025', '#4FB3F9', '#89E847', '#FCEA4F', '#FFFFFF', '#333333'],
      brush: [3, 8, 16],
      context: {},
      canvasMoveUse: false,
      // 存储当前表面状态数组-上一步
      preDrawAry: [],
      // 存储当前表面状态数组-下一步
      nextDrawAry: [],
      // 中间数组
      middleAry: [],
      // 配置参数
      config: {
        lineWidth: 3,
        lineColor: '#EB4025'
      },
      client: {
        height: 0,
        width: 0
      },
      brushIndex: 1,
      colorIndex: 1
    }
  },
  watch: {
    active: {
      immediate: false,
      handler (val) {
        if (val) {
          window.addEventListener('resize', this.resize)
          document.addEventListener('mouseup', this.canvasUp)
          document.addEventListener('touchend', this.canvasUp)
        } else {
          window.removeEventListener('resize', this.resize)
          document.removeEventListener('mouseup', this.canvasUp)
          document.removeEventListener('touchend', this.canvasUp)
        }
      }
    }

  },
  created () {
    this.client.height = document.body.offsetHeight
    this.client.width = document.body.offsetWidth
    this.devicePixelRatio = 1
    if (window.devicePixelRatio) {
      this.devicePixelRatio = window.devicePixelRatio
    }
  },
  mounted () {
    const canvas = document.querySelector('#canvas')
    this.context = canvas.getContext('2d')
    this.initDraw()
    this.setCanvasStyle()
    // window.addEventListener('resize', this.resize)
    // document.addEventListener('mouseup', this.canvasUp)
    // document.addEventListener('touchend', this.canvasUp)
  },
  beforeDestroy () {
    // window.removeEventListener('resize', this.resize)
    // document.removeEventListener('mouseup', this.canvasUp)
    // document.removeEventListener('touchend', this.canvasUp)
  },
  methods: {
    resize () {
      this.client.height = document.body.offsetHeight
      this.client.width = document.body.offsetWidth

      console.log('width', document.getElementById('canvas').width)
      console.log('this.client', this.client)
    },
    close () {
      this.$emit('close')
    },
    // 设置绘画配置
    setCanvasStyle () {
      this.context.lineWidth = this.config.lineWidth
      this.context.strokeStyle = this.config.lineColor
    },
    initDraw () {
      const preData = this.context.getImageData(0, 0, this.client.width, this.client.height)
      // 空绘图表面进栈
      this.middleAry.push(preData)
    },
    // mouseup
    canvasUp (e) {
      console.log('canvasUp')
      const preData = this.context.getImageData(0, 0, this.client.width, this.client.height)
      if (!this.nextDrawAry.length) {
        // 当前绘图表面进栈
        this.middleAry.push(preData)
      } else {
        this.middleAry = []
        this.middleAry = this.middleAry.concat(this.preDrawAry)
        this.middleAry.push(preData)
        this.nextDrawAry = []
      }
      this.canvasMoveUse = false
    },
    // mousedown
    canvasDown (e) {
      console.log('canvasDown')
      this.canvasMoveUse = true
      // client是基于整个页面的坐标
      // offset是cavas距离顶部以及左边的距离
      const canvasX = e.clientX - e.target.parentNode.offsetLeft
      const canvasY = e.clientY - e.target.parentNode.offsetTop
      this.setCanvasStyle()
      // 清除子路径
      this.context.beginPath()
      this.context.moveTo(canvasX, canvasY)
      console.log('moveTo', canvasX, canvasY)
      console.dir(e.target)
      // 当前绘图表面状态
      const preData = this.context.getImageData(0, 0, this.client.width, this.client.height)
      // 当前绘图表面进栈
      this.preDrawAry.push(preData)
    },
    canvasMove (e) {
      if (this.canvasMoveUse) {
        console.log('canvasMove')
        const t = e.target
        let canvasX
        let canvasY
        if (this.isPc()) {
          canvasX = e.clientX - t.parentNode.offsetLeft
          canvasY = e.clientY - t.parentNode.offsetTop
        } else {
          canvasX = e.changedTouches[0].clientX - t.parentNode.offsetLeft
          canvasY = e.changedTouches[0].clientY - t.parentNode.offsetTop
        }
        this.context.lineTo(canvasX, canvasY)
        this.context.stroke()
      }
    },
    isPc () {
      const userAgentInfo = navigator.userAgent
      const Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']
      let flag = true
      for (let v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
          flag = false
          break
        }
      }
      return flag
    },
    brushChange (val) {
      this.brushIndex = val
      this.config.lineWidth = this.brush[val - 1]
    },
    colorChange (val) {
      this.colorIndex = val
      this.config.lineColor = this.colors[val - 1]
    },
    clearDraw () {
      this.context.clearRect(0, 0, this.context.canvas.width, this.context.canvas.height)
      this.preDrawAry = []
      this.nextDrawAry = []
      this.middleAry = [this.middleAry[0]]
    },
    // 撤销
    revoke () {
      if (this.preDrawAry.length) {
        const popData = this.preDrawAry.pop()
        const midData = this.middleAry[this.preDrawAry.length + 1]
        this.nextDrawAry.push(midData)
        this.context.putImageData(popData, 0, 0)
      }
    },
    // 前进
    redo () {
      if (this.nextDrawAry.length) {
        const popData = this.nextDrawAry.pop()
        const midData = this.middleAry[this.middleAry.length - this.nextDrawAry.length - 2]
        this.preDrawAry.push(midData)
        this.context.putImageData(popData, 0, 0)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.draw-box {
  position: absolute;
  z-index: 9999;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .clear-box {
    padding: 5px 15px;
  }
  .tips-draw {
    font-size: 8px;
    font-weight: 400;
    display: flex;
    align-items: center;
  }
  .draw-tools {
    position: absolute;
    z-index: 9999;
    top: 33%;
    right: calc(#{vh2(73)} + 20px);
    background: rgba(255, 255, 255, 0.95);
    border: vh2(1) solid #FFFFFF;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 10px;
    width: 120px;
    // height: 200px;
    padding: 10px 2px;
    box-sizing: border-box;
    color: #000;
    font-size: 12px;
    font-weight: 500;
  }
  .mt10 {
    margin-top: 10px;
  }
  .mr10 {
    margin-right: 10px;
  }

  .close-draw {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 30px;
    margin-top: 20px;
    margin-bottom: 10px;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    i {
      padding-top: 2px;
    }
  }

  .brush-1 {
    width: 10px;
    height: 10px;
  }
  .brush-2 {
    width: 14px;
    height: 14px;
  }
  .brush-3 {
    width: 18px;
    height: 18px;
  }

  .brush {
    border-radius: 50%;
    background-color: #D9D9D9;
    cursor: pointer;
    box-sizing: border-box;
  }

  .brush-active {
    background-color: #333333 !important;
    border: 1px solid #2D9CDB;
  }

  .color-box {
    width: 50%;
    display: flex;
    justify-content: center;
  }
  .colors-1 {
    background: #EB4025;
  }
  .colors-2 {
    background: #4FB3F9;
  }
  .colors-3 {
    background: #89E847;
  }
  .colors-4 {
    background: #FCEA4F;
  }
  .colors-5 {
    background: #FFFFFF;
    border: 1px solid #F3F3F3;
  }
  .colors-6 {
    background: #333333;
  }
  .colors {
    width: 25px;
    height: 25px;
    cursor: pointer;
    box-sizing: border-box;
  }

  .colors-active {
    border: 1px solid #56CCF2 !important;
  }
}
</style>
