<template>
  <div class="translate-container">
    <img src="@/assets/images/ai/ai-translation.png" alt="" />
  </div>
</template>

<style lang='scss' scoped>
.translate-container {
    width: 100%;
    height: 100%;
    position: absolute;
    background: transparent;
    z-index: 3002;
    right: 100%;
    animation-name: TransAni;
    animation-duration: 2s;

    .part {
        width: 100%;
        height: 25%;

        img {
            height: 100%;
            object-fit: contain;
        }
    }
    img {
        height: 100%;
        object-fit: contain;
    }
}

    @keyframes TransAni {
        0%    { right:-100%}
        100%  { right:100%}
    }
</style>
