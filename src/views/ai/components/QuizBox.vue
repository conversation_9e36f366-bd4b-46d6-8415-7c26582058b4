<template>
  <div class="quiz-box">
    <challenge-finish
      v-if="showClgFinsh"
      :clg-suceess="clgSuceess"
      :challenge-score="challengeScore"
      @animateEnd="showClgFinsh = false"
    />
    <!-- 终极挑战引导 -->
    <challenge-guide
      v-if="showChallengeGuide && !showPlayBtn"
      :context="context"
      @close="showChallengeGuide = false"
    />
    <quiz
      v-if="!showChallengeGuide && !showPlayBtn"
      :context="context"
      :secound="secound"
      :caption="caption"
      :challenge-status="challengeStatus"
      :show-challenge-guide="showChallengeGuide"
      :current-section-index="currentSectionIndex"
      :is-portrait="isPortrait"
      :is-mobile="isMobile"
      :is-full-screen-mode="isFullScreenMode"

      @playback="handlePlayback"
      @showQuit="showQuit"
      @answerRight="answerRight"
      @answerWrong="answerWrong"
      @fullScreen="$emit('fullScreen')"
    />
  </div>
</template>

<script>
import bg from '@/assets/images/ai/ai-bg-2.jpeg'
import Quiz from './Quiz'
import bgChanage from '@/assets/images/ai/bg-chanage.png'
import ChallengeFinish from './ChallengeFinish'
import ChallengeGuide from './../components/ChallengeGuide'
export default {
  components: { Quiz, ChallengeFinish, ChallengeGuide },
  props: {
    context: Object,
    backcover: String,
    secound: Number,
    caption: String,
    challengeStatus: String,
    challengeScore: Number,
    showPlayBtn: Boolean,
    currentSectionIndex: Number,
    isPortrait: Boolean,
    isMobile: Boolean,
    isFullScreenMode: Boolean
  },
  data () {
    return {
      bg,
      bgChanage,
      showClgFinsh: false,
      clgSuceess: false,
      studentCourseId: this.$route.params.studentCourseId,
      showChallengeGuide: false
    }
  },
  mounted () {
    console.log('quizBox mounted')
    this._showChallengeGuide()
  },
  methods: {
    handlePlayback (dir) {
      this.$emit('playback', dir)
    },
    handleAnswerWrong (evt) {
      this.$emit('answered', evt)
    },
    async answerRight () {
      this.$emit('answerRight')
      const currentSection = this.context.currentSection
      const currentPartion = this.context.currentPartion
      if (currentSection.stepType === 'CHALLENGE' &&
      +currentSection.id === +currentPartion.data[currentPartion.data.length - 1].id) {
        await this.$emit('getAicourseUnitUser')
        this.showClgFinsh = true
        this.clgSuceess = true
      }
    },
    async answerWrong () {
      this.$emit('answerWrong')
      const currentSection = this.context.currentSection
      if (currentSection.stepType === 'CHALLENGE') {
        await this.$emit('getAicourseUnitUser')
        this.showClgFinsh = true
        this.clgSuceess = false
      }
    },
    showQuit () {
      this.$emit('showQuit')
    },
    _showChallengeGuide () {
      if (this.context.currentSection.stepType === 'CHALLENGE' &&
      +this.context.currentSection.id === +this.context.currentPartion.data[0].id &&
      (!this.context.currentSection.aicourseUnitSectionUser ||
      !this.context.currentSection.aicourseUnitSectionUser.completed)) {
        this.showChallengeGuide = true
      } else {
        this.showChallengeGuide = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.quiz-box {
  box-sizing: border-box;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(109.39deg, #FCECFF -23.25%, #ABF6FF 97.11%);
  padding: vh2(50) vh2(20) vh2(20);
}
</style>
