<template>
  <div class="report-containr flex justify-center">

    <div class="left">
      <!-- 课堂表现 -->
      <div class="performance-box">
        <div class="performance-box-content">
          <div class="report-title">课堂表现</div>
          <div class="flex justify-between">
            <div class="perform-item flex-col align-center">
              <div class="yellow">{{ myPerform.score || '-' }}</div>
              <span>缤果币</span>
            </div>
            <div class="perform-item flex-col align-center">
              <div class="yellow">{{ (wallet && wallet.userLevel && wallet.userLevel.aiScore) || '-' }}</div>
              <span>累计获得缤果币</span>
            </div>
            <div class="perform-item flex-col align-center">
              <div class="blue">{{ myPerform.userAnswerTimes || '-' }}</div>
              <span>参与答题次数</span>
            </div>
            <div class="perform-item flex-col align-center">
              <div class="blue">{{ myPerform.totalCorrect ? `${ myPerform.totalCorrect }%` : '-' }}</div>
              <span>平均答题正确率</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 挑战成绩 -->
<!--      <div class="challenge-box">-->
      <div v-if="myPerform.challengeStatus !== 'NONE'" class="challenge-box">
        <div class="challenge-box-content flex align-center">
          <div class="report-title">挑战成绩</div>
          <div class="clg-rank-text flex align-center">
            超过了
            <font
              class="clg-rank-percent"
              :class="{
                'challenge-green': cupStyle === 1,
                'challenge-purple': cupStyle === 2,
                'challenge-blue': cupStyle === 3
              }"
            >
              {{ myPerform.challengeResult ? `${ myPerform.challengeResult }%` : '-' }}&nbsp;
            </font>
            的班级
          </div>
          <img :src="cupStyle === 1 ? cupGreenShadow : cupStyle === 2 ? cupYellowShadow : cupBlueShadow" />
        </div>
      </div>

      <!-- 课堂收获 -->
      <div
        class="obtain-box"
        :class="[
          myPerform.challengeStatus !== 'NONE'
            ? 'obtain-box-height1'
            : 'obtain-box-height2'
        ]"
      >
        <div class="report-title">课堂收获</div>
        <div v-swipetouch class="harvest-list">
          <template v-if="harvestList.length > 0">
            <div
              v-for="(item,index ) in harvestList"
              :key="`harvest-${index}`"
              class="harvest-item"
              :class="{
                'cude': item.charAt(0) === '#'
              }"
            >{{ item.charAt(0) === '#' ? item.substring(1) :item }}</div>
          </template>
          <div v-else class="empty">
            <img :src="empty1" alt="" />
            <span>暂无数据</span>
          </div>
        </div>
      </div>
    </div>

    <div class="right">
      <!-- 排名 -->
      <div class="rank-box">
        <div class="rank-title-list" style="gap: 22px">
          <div class="rank-title" :class="{'rank-title-select': rankState === 0}" @click="changeRankSelect(0)">分组排行</div>
          <div class="rank-title" :class="{'rank-title-select': rankState === 1}" @click="changeRankSelect(1)">本节排行</div>
          <div class="rank-title" :class="{'rank-title-select': rankState === 2}" @click="changeRankSelect(2)">总排行</div>
        </div>
        <div v-if="rankState === 0" class="group-box">
          <div
            v-for="item in courseHasGroupList"
            :key="item"
            class="group-item"
            :class="{'group-select': item === rankActiveGroupNum}"
            @click="changeRankGroup(item)"
          >分{{ item }}组</div>
        </div>
        <div class="rank-list">
          <div v-if="(rankState === 1 || rankState === 2) && myRank && rankList.length > 0" class="my-rank">
            <div class="my-rank-name">{{ myRank && myRank.userName }}</div>
            <div class="flex align-center">
              <img class="coin-image" :src="coin" alt="" />
              <div class="coin-number">{{ myRank && myRank.value }}</div>
              <div class="ranking">排名：{{ myRank && myRank.ranking || '-' }}位</div>
            </div>
          </div>
          <template v-if="rankList.length > 0">
            <div v-for="(item, index) in rankList" :key="item.ranking" class="rank-item">
              <template v-if="index < 3">
                <img class="rank-image" :src="rankLogoList[index]" alt="" />
              </template>
              <template v-else>
                <div class="rank-image flex-col justify-center">
                  <div class="rank-image-box">
                    {{ index + 1 }}
                  </div>
                </div>
              </template>
              <div class="rank-name">{{ rankState === 0 ? (item.name || '') : (item.userName || '') }}</div>
              <img class="rank-coin-image" :src="coin" alt="" />
              <div class="x">x</div>
              <div class="coin">{{ rankState === 0 ? item.score : item.value }}</div>
            </div>
          </template>
          <div v-else class="empty">
            <img :src="empty2" alt="" />
            <span>暂无数据</span>
          </div>
        </div>
      </div>

      <!-- 按钮 -->
      <div class="btn-box">
        <div class="btn" @click="$emit('showQuit')">返回</div>
        <div class="btn" @click="$emit('again')">再次学习</div>
      </div>
    </div>

  </div>
</template>

<script>
import rank1 from '@/assets/ai-image/ai/rank-1.png'
import rank2 from '@/assets/ai-image/ai/rank-2.png'
import rank3 from '@/assets/ai-image/ai/rank-3.png'
import cupGreen from '@/assets/ai-image/ai/report-cup-green.png'
import cupYellow from '@/assets/ai-image/ai/report-cup-yellow.png'
import cupBlue from '@/assets/ai-image/ai/report-cup-blue.png'
import coin from '@/assets/ai-image/icon/coin.png'
import cupGreenShadow from '@/assets/ai-image/ai/report-cup-green-shadow.png'
import cupYellowShadow from '@/assets/ai-image/ai/report-cup-yellow-shadow.png'
import cupBlueShadow from '@/assets/ai-image/ai/report-cup-blue-shadow.png'
import empty1 from '@/assets/ai-image/ai/empty-1.png'
import empty2 from '@/assets/ai-image/ai/empty-2.png'
import { getAiCourseRank, getUserAiCourseRank, getAicourseUnitUser, getWallet, getLessonGroup, aiLessonRank, getAiCourseUnitInfo } from '@/api/aicourse'

export default {
  props: {
    context: Object,
    unitId: String
  },
  data () {
    return {
      rankLogoList: [rank1, rank2, rank3],
      rankList: [],
      myRank: {},
      myPerform: { 'aicourseUnit': {}},
      wallet: { 'userLevel': {}},
      cupGreen,
      cupYellow,
      cupBlue,
      cupGreenShadow,
      cupYellowShadow,
      cupBlueShadow,
      coin,
      empty1,
      empty2,
      pageSize: 50,
      pageNo: 1,
      harvestList: [],
      studentCourseId: this.$route.params.studentCourseId,
      courseId: this.$route.params.courseId,
      rankState: 0,
      courseHasGroupList: [],
      rankActiveGroupNum: 0
    }
  },
  computed: {
    cupStyle () {
      if (+this.myPerform.challengeResult > 80) return 1
      if (+this.myPerform.challengeResult > 50 && +this.myPerform.challengeResult < 81) return 2
      return 3
    },
    haveHistory () {
      return window.history.length > 1
    }
  },
  created () {
    this._getAicourseUnitUser()
    this._getAiCourseUnitInfo()
    this._getWallet()
    this._getLessonGroup()
  },
  mounted () {
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    resize () {
      const performanceHeight = document.querySelector('.performance-box').offsetHeight
      const obtainEl = document.querySelector('.obtain-box')
      if (this.myPerform.challengeStatus !== 'NONE') {
        const challengeHeight = document.querySelector('.challenge-box').offsetHeight
        obtainEl.style.height = `calc(100% - 20px - ${performanceHeight}px - ${challengeHeight}px)`
      } else {
        obtainEl.style.height = `calc(100% - 10px - ${performanceHeight}px)`
      }
    },
    changeRankSelect (rankState) {
      if (this.rankState === rankState) return
      this.rankState = rankState
      this.rankList = []
      if (rankState === 0) {
        this.rankActiveGroupNum = 0
        this._getLessonGroup()
      } else {
        this.pageNo = 1
        this.myRank = null
        this._getAiCourseRank()
        this._getUserAiCourseRank()
      }
    },
    changeRankGroup (group) {
      this.rankActiveGroupNum = group
      this.rankList = []
      this._aiLessonRank()
    },
    // 获取ai课排名
    async _getAiCourseRank () {
      if (this.pageNo === 1) this.rankList = []
      const params = {
        'pageNo': this.pageNo,
        'pageSize': this.pageSize,
        'aiCourseUnitId': this.rankState === 1 ? this.unitId : ''
      }
      getAiCourseRank(params).then(
        response => {
          this.rankList = this.rankList.concat(response.data.data)
        }
      )
    },
    // 获取我的ai课排名
    async _getUserAiCourseRank () {
      const params = {
        'studentCourseId': this.studentCourseId,
        'aiCourseUnitId': this.rankState === 1 ? this.unitId : ''
      }
      getUserAiCourseRank(params).then(
        response => {
          this.myRank = response.data.data
        }
      )
    },
    // 获取我的ai课表现
    async _getAicourseUnitUser () {
      var response = await getAicourseUnitUser({
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.unitId
      })
      if (+response.data.code === 200) {
        this.myPerform = response.data.data
        this.resize()
        // if (this.myPerform.aicourseUnit && this.myPerform.aicourseUnit.gain) {
        //   this.harvestList = this.myPerform.aicourseUnit.gain.split('\n')
        // }
      }
    },
    // 获取钱包
    async _getWallet () {
      var response = await getWallet()
      if (+response.data.code === 200) {
        this.wallet = response.data.data
      }
    },
    //  获取分组
    async _getLessonGroup () {
      const { data: { data }} = await getLessonGroup({
        studentCourseId: this.studentCourseId,
        lessonId: this.unitId
      })
      const arr = []
      data.forEach(element => {
        arr.push(element.groupNum)
      })
      this.courseHasGroupList = Array.from(new Set(arr))
      this.courseHasGroupList.sort((old, New) => {
        return old - New
      })
      if (this.isFromCheckRank) {
        this.rankActiveGroupNum = this.pkDetailDate.length
        this.isFromCheckRank = false
      } else {
        this.rankActiveGroupNum = Number(this.courseHasGroupList[0]) || -1
      }
      this._aiLessonRank()
    },
    // 获取分组排行
    async _aiLessonRank () {
      const { data: { data }} = await aiLessonRank({
        studentCourseId: this.studentCourseId,
        unitId: this.unitId,
        groupNum: this.rankActiveGroupNum,
        totalSort: 0
      })
      this.rankList = data || []
    },
    async _getAiCourseUnitInfo () {
      var response = await getAiCourseUnitInfo({
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.unitId
      })
      if (+response.data.code === 200 && response.data.data.gain) {
        this.harvestList = response.data.data.gain.split('\n')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";

.report-containr {
    width: 100%;
    height: 100%;
    background: url('~assets/ai-image/ai/bg-report.jpg') center center no-repeat;
    background-size: cover;
    position: relative;

    .left {
      width: vh2(475);
      min-width: 475px;
      margin-right: vw2(10);
      padding-top: vh2(72);
      padding-bottom: vh2(30);
      height: 100%;
      box-sizing: border-box;
    }

    .right {
      width: vh2(340);
      padding-top: vh2(72);
      padding-bottom: vh2(30);
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 5%;
    }

    .report-title {
      font-size: vh2(22);
      color: #333333;
      font-weight: 500;
    }
}

.performance-box {
  width: 100%;
  background: rgba(255, 255, 255, 0.66);
  border: 1px solid #FFFFFF;
  border-radius: 10px;
  margin-bottom: 10px;
  height: vh2(130);

  .performance-box-content {
    padding: vh2(16) vh2(20);
  }

  .report-title {
    margin-bottom: vh2(13)
  }

  .yellow,
  .blue,
  .green {
    font-family: 'Lantinghei SC';
    font-style: normal;
    font-weight: 500;
    font-size: vh2(22);
    line-height: vh2(30);
    text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.25);
    margin-bottom: vh2(10);
  }

  .yellow {
    color: #F2994A;
  }

  .blue {
    color: #2D9CDB;
  }

  .green {
    color: #2FC56E;
  }
}

.challenge-box {
    width: 100%;
    background: rgba(255, 255, 255, 0.66);
    border: 1px solid #FFFFFF;
    border-radius: 10px;
    margin-bottom: 10px;
    height: vh2(80);

    .challenge-box-content {
      padding: vh2(10) vh2(20);
    }

    img {
        width: vh2(70);
        height: vh2(66);
        object-fit: contain;
    }

    .clg-rank-text {
      font-size: vh2(16);
      color: #000000;
      margin: 0 vh2(25);
      font-family: 'PingFang SC';

      .clg-rank-percent {
        font-size: vh2(30);
        margin: 0 vh2(7);
        font-family: 'Lantinghei SC';
        text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        font-weight: 500;
      }
    }

    .challenge-green {
        color: #DFF741;
    }

    .challenge-purple {
        color: #D05FCE;
    }

    .challenge-blue {
        color: #74B9FF;
    }
}

.obtain-box {
  width: 100%;
  background: rgba(255, 255, 255, 0.66);
  border: 1px solid #FFFFFF;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  user-select: none;
  .report-title {
    padding: vh2(8) vh2(20) vh2(13);
  }

  .harvest-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 vh2(6);
    padding: 0 vh2(14);
    box-sizing: border-box;
    gap: 14px;
    overflow: scroll;
    overflow-x: auto;

    &::-webkit-scrollbar-track-piece {
      background-color: transparent;
    }
    &::-webkit-scrollbar {
      width: 8px;
    }
    &::-webkit-scrollbar-thumb {
      background: #FFFFFF;
      border-radius: 18px;
    }

    .harvest-item {
      width: 100%;
      font-family: PingFangSC-Regular;
      font-weight: 500;
      font-size: vh2(14);
      color: #000000;
      margin-bottom: vh2(7);
      word-break: break-all;
      white-space: pre-line;
    }

    .cude {
      font-weight: bold;
    }
  }
}

.obtain-box-height1 {
  //height: vh2(304);
}

.obtain-box-height2 {
  //height: vh2(396);
}

.rank-box {
  width: 100%;
  height: vh2(468);
  background: rgba(255, 255, 255, 0.66);
  border: 1px solid #FFFFFF;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  height: 90% ;

  .rank-title-list {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 22px;
    padding: vh2(18) 0 vh2(12);
  }

  .rank-title {
    color: #333333;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: vh2(20);
    line-height: vh2(28);
    cursor: pointer;
  }

  .rank-title-select {
    font-size: vh2(22);
    line-height: vh2(31);
    font-weight: 500;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      width: vh2(30);
      height: vh2(4);
      background: #F2994A;
      border-radius: 16px;
      transform: translate(-50%, 0);
      bottom: -2px;
      left: 50%;
    }
  }

  .group-box {
    display: flex;
    flex-wrap: wrap;
    gap: vh2(12);
    padding-left: vh2(18);
    margin-bottom: vh2(22);

    .group-item {
      width: vh2(62);
      height: vh2(30);
      background: rgba(255, 255, 255, 0.4);
      border: 1px solid rgba(130, 130, 130, 0.1);
      border-radius: 6px;
      text-align: center;
      line-height: vh2(30);
      font-size: vh2(16);
      cursor: pointer;
    }

    .group-select {
      background: #F2C94C;
    }
  }

  .rank-list {
    flex: 1;
    overflow: scroll;
    overflow-x: hidden;
    padding:0 vh2(10) vh2(12);

    &::-webkit-scrollbar-track-piece {
      background-color: transparent;
    }
    &::-webkit-scrollbar {
      width: 5px;
    }
    &::-webkit-scrollbar-thumb {
      background: #FFFFFF;
      border-radius: 18px;
    }

    .my-rank {
      width: 100%;
      background: rgba(255, 255, 255, 0.4);
      border-radius: 10px;
      margin-bottom: vh2(20);
      padding: vh2(10) 0;

      .my-rank-name {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: vh2(15);
        line-height: vh2(21);
        color: #000000;
        padding: 0 vh2(10) vh2(10);
      }

      .coin-image {
        height: vh2(22);
        width: vh2(22);
        object-fit: contain;
        padding-left: vh2(10);
        padding-right: vh2(3);
      }

      .coin-number {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: vh2(24);
        line-height: vh2(34);
        color: #000000;
        margin-right: auto;
      }

      .ranking {
        padding-right: vh2(10);
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 300;
        font-size: vh2(20);
        line-height: vh2(28);
      }
    }

    .rank-item {
      margin-bottom: vh2(15);
      display: flex;
      align-items: center;

      .rank-image {
        width: vh2(42);
        height: vh2(42);
        object-fit: contain;
      }

      .rank-image-box {
        width: vh2(28);
        height: vh2(28);
        background: #FFE6BF;
        border: 3px solid #F2C94C;
        border-radius: 50%;
        text-align: center;
        line-height: vh2(28);
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: vh2(16);
        margin: 0 auto;
      }

      .rank-name {
        color: #000000;
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: vh2(16);
        line-height: vh2(22);
        flex: 1;
        padding-left: vh2(20);
        padding-right: vh2(10);
        @include ellipsisMore(1);
      }

      .rank-coin-image {
        width: vh2(22);
        height: vh2(22);
        margin-right: vh2(5);
      }

      .x {
        font-weight: 500;
        line-height: vh2(34);
        color: #000000;
        margin-right: vh2(4);
      }

      .coin {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: vh2(24);
        line-height: vh2(34);
        color: #000000;
        min-width: vh2(44);
      }
    }

  }

}

.btn-box {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
  cursor: pointer;
  height: 10%;
  align-items: end;

  .btn {
    height: vh2(50);
    width: vh2(150);
    background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
    background-size: contain;
    text-align: center;
    font-size: vh2(20);
    font-weight: 500;
    line-height: vh2(50);
  }
}

.empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: vh2(7);
  margin-top: vh2(53);
  img {
    width: vh2(106);
    height: vh2(86);
    object-fit: contain;
  }

  span {
    font-weight: 500;
    font-size: vh2(12);
    line-height: vh2(16);
    color: #4F4F4F;
  }
}

</style>
