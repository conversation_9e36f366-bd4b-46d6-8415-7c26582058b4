<template>
  <div class="quiz-box" @click="closePop">
    <audio ref="countAudio" controls="controls" hidden :src="countAu"></audio>
    <audio ref="tipAudio" controls="controls" hidden :src="audioUrl"></audio>
    <div class="quiz-container flex">
      <div class="content">
        <div class="quiz-area">
          <div class="sheet">
            <img v-if="mediaFileUrl" class="think-image" :src="mediaFileUrl" />
            <div class="right-part">
              <div v-if="second > -1" class="count-down">
                <div class="second">{{ second | timeFormate }}</div>
              </div>
            </div>
            <div class="tips-group">
              <div v-if="audioUrl" class="thinking-tips flex align-center justify-center" @click="playAudio">
                <img class="audio" :src="isPlayAudio ? audioPlay : audioClose" alt="" />
              </div>
              <div v-if="tipUrl" class="thinking-tips flex flex-col align-center justify-center" @click="openTip">
                <img :src="tips" alt="" />
                <span>提示</span>
              </div>
              <div v-if="teacherRemark" class="thinking-tips flex flex-col align-center justify-center" @click.stop="changeRemark">
                <img :src="book" alt="" />
                <span>备注</span>
                <div v-if="showRemark" class="remark-box">{{ teacherRemark }}</div>
              </div>
              <div class="thinking-tips flex flex-col align-center justify-center" @click="$emit('fullScreen')">
                <img class="audio" :src="isFullScreenMode ? unfullscreen : fullscreen" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer flex-col">
        <div class="flex align-center justify-center" style="margin-bottom: auto">
          <img class="cross-star" :src="crossStar" alt="" />
          <div class="text-count">{{ tagName }}</div>
          <img class="cross-star" :src="crossStar" alt="" />
        </div>
        <div v-if="preText" class="pre" @click="handleBack">{{ preText }}</div>
        <div v-if="nextText" class="next" @click="handleNext">{{ nextText }}</div>
      </div>
    </div>
    <div v-if="showTip" class="tip-box">
      <div class="title">答案提示</div>
      <img class="tip-image" :src="tipUrl" alt="" />
      <img class="close" :src="thinkTipClose" alt="" @click="closeTip" />
    </div>
  </div>
</template>

<script>
import crossStar from '@/assets/ai-image/ai/cross-star.svg'
import tips from '@/assets/ai-image/ai/tips.svg'
import book from '@/assets/ai-image/ai/book.svg'
import fullscreen from '@/assets/ai-image/ai/fullscreen.svg'
import unfullscreen from '@/assets/ai-image/ai/unfullscreen.svg'
import thinkTipClose from '@/assets/ai-image/ai/think-tip-close.svg'
import audioPlay from '@/assets/ai-image/ai/music.svg'
import audioClose from '@/assets/ai-image/ai/music-no.svg'
import countAu from '@/assets/audio/count-down.mp3'
import { getAiCourseUnitSection } from '@/api/aicourse'
import moment from 'moment'
export default {
  filters: {
    timeFormate (val) {
      if (!val) {
        return 0
      }
      const time = moment.duration(val, 'seconds') // 得到一个对象，里面有对应的时分秒等时间对象值
      const hours = time.hours()
      const minutes = time.minutes()
      const seconds = time.seconds()
      if (hours) {
        return moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss')
      } else if (minutes) {
        return moment({ m: minutes, s: seconds }).format('mm:ss')
      } else {
        return moment({ s: seconds }).format('ss')
      }
    }
  },
  props: {
    context: Object,
    currentSectionIndex: Number,
    isFullScreenMode: Boolean
  },
  data () {
    return {
      tips,
      book,
      fullscreen,
      unfullscreen,
      thinkTipClose,
      audioPlay,
      audioClose,
      countAu,
      crossStar,
      showTip: false,
      showRemark: false,
      mediaFileUrl: undefined,
      tipUrl: undefined,
      teacherRemark: undefined,
      audioUrl: undefined,
      isPlayAudio: false,
      second: -1,
      timer: undefined,
      studentCourseId: this.$route.params.studentCourseId,
      preview: this.$route.query.preview,
      tagName: undefined
    }
  },
  computed: {
    // 向前按钮文字
    preText () {
      var currentSectionIndex = this.context.currentSectionIndex
      if (currentSectionIndex > 0) {
        // var preSectionIndex = currentSectionIndex - 1
        // if (this.context.sectionList[preSectionIndex].step < this.context.sectionList[currentSectionIndex].step) { return '上一环节' }
        // if (this.context.sectionList[preSectionIndex].aiSectionType === 'VIDEO') { return '回看视频' }
        return '上一步'
      }
      return ''
    },
    // 向后按钮文字
    nextText () {
      // var currentSectionIndex = this.context.currentSectionIndex
      // var nextSegmentIndex = currentSectionIndex + 1
      // if (nextSegmentIndex >= this.context.sectionList.length) return ''
      // if (this.context.sectionList[nextSegmentIndex].step > this.context.sectionList[currentSectionIndex].step) { return '下一环节' }
      // if (this.context.sectionList[nextSegmentIndex].aiSectionType === 'VIDEO') { return '下一步' }
      if (+this.preview === 1 && this.context.currentSectionIndex === this.context.sectionList.length - 1) return ''
      return '下一步'
    }
  },
  watch: {
    currentSectionIndex: {
      deep: true,
      immediate: true,
      handler () {
        this.$emit('updateThinkProgress')
        this.mediaFileUrl = undefined
        this.tipUrl = undefined
        this.teacherRemark = undefined
        this.audioUrl = undefined
        this.isPlayAudio = false
        this.second = -1
        this.showTip = false
        this.stopCountAudio()
        this.$nextTick(async () => {
          const audio = this.$refs.tipAudio
          audio.volume = 0.1
          if (!audio.paused) {
            await audio.pause()
          }
        })
        this._getAiCourseUnitSection()
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      const audio = this.$refs.tipAudio
      audio.volume = 0.1
      const that = this
      audio.addEventListener('ended', function () {
        that.isPlayAudio = false
      })
      this.$refs.countAudio.volume = 0.1
    })
  },
  destroyed () {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = undefined
    }
  },
  methods: {
    async _getAiCourseUnitSection () {
      const sectionId = this.context.currentSection.id
      var response = await getAiCourseUnitSection({
        aicourseUnitSectionId: sectionId,
        studentCourseId: this.studentCourseId
      })
      if (+response.data.code === 200) {
        const data = response.data.data
        this.tagName = data.tagName
        this.teacherRemark = data.teacherRemark
        this.mediaFileUrl = data.mediaFile.url
        if (data.resourceList) {
          const tipData = data.resourceList.find(
            item => item.linkType === 'AI_SECTION_TIPS'
          )
          const audioData = data.resourceList.find(
            item => item.linkType === 'AI_SECTION_AUDIO'
          )
          if (tipData) this.tipUrl = tipData.mediaFile.url
          if (audioData) {
            this.audioUrl = audioData.mediaFile.url
            this.$nextTick(() => {
              this.playAudio()
            })
          }
        }
        if (data.time && data.time > 0) {
          this.second = +data.time
          this.timer = setInterval(() => this.subCount(), 1000)
        }
      }
    },
    openTip () {
      this.showTip = true
    },
    closeTip () {
      this.showTip = false
    },
    // 播放提示音乐
    async playAudio () {
      try {
        const audio = this.$refs.tipAudio
        audio.volume = 0.1
        if (this.isPlayAudio) {
          if (!audio.paused) {
            await audio.pause()
            this.isPlayAudio = false
          }
        } else {
          await audio.play()
          this.isPlayAudio = true
        }
      } catch (e) {
        this.$messagee.error('音频播放失败')
        this.isPlayAudio = false
      }
    },
    //  减少时间
    subCount () {
      this.second -= 1
      if (this.second < 4) {
        console.log('奇思妙想倒计时:', this.second)
      }
      if (this.second === 3) {
        this.$refs.countAudio.volume = 0.1
        this.$refs.countAudio.play()
      }
      if (this.second <= 0) {
        this.stopCountAudio()
      }
    },
    stopCountAudio () {
      this.$nextTick(() => {
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = undefined
        }
        if (!this.$refs.countAudio.paused) {
          this.$refs.countAudio.pause()
          this.$refs.countAudio.load()
        }
      })
    },
    handleBack () {
      this.$emit('playback', 'prev')
    },
    handleNext () {
      this.$emit('onThinkEnd')
    },
    changeRemark () {
      this.showRemark = !this.showRemark
    },
    closePop () {
      this.showRemark = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";

.quiz-box {
  box-sizing: border-box;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(109.39deg, #FCECFF -23.25%, #ABF6FF 97.11%);
  padding: vh2(50) vh2(20) vh2(20);
}

.quiz-container {
  position: relative;
  width: 100%;
  height: 100%;
  user-select: none;
}

.header {
  width: 100%;
  height: calc(100vw * 45 / 965);
  // background: url('~assets/images/ai/quiz-top.png') center center no-repeat;
  // background-size: contain;

  img {
    width: calc(100vw * 30 / 965);
    height: calc(100vw * 40 / 965);
    object-fit: cover;
    margin-left: calc(100vw * 32 / 965);
    cursor: pointer;
  }
}

.footer {
  flex: 1;
  gap: vh2(15);
  display: flex;
  flex-direction: column;
  align-items: center;

  .pre,
  .next {
    // z-index: 999;
    font-size: vh2(20);
    font-family: 'PingFang SC';
    font-weight: 500;
    color: #000000;
    line-height: vh2(50);
    text-align: center;
    cursor: pointer;
  }

  .pre {
    height: vh2(50);
    width: vh2(150);
    background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
    background-size: contain;
  }

  .next {
    height: vh2(50);
    width: vh2(150);
    background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
    background-size: contain;
  }

}

img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.cross-star {
  width: vh2(17);
  height: vh2(17);
  object-fit: contain;
}

.text-count {
  font-size: vh2(20);
  font-weight: 400;
  color: #000000;
  text-align: center;
  margin:0 vh2(14);
}

.content {
  position: relative;
  width: 83%;
  height: 100%;

  .quiz-area {
    position: relative;
    width: 100%;
    height: 100%;

    .right-part {
      position: absolute;
      right: vh2(10);
      top: vh2(20);
      z-index: 10;

      .count-down {
        min-width: vh2(57);
        padding: 0 vh2(15);
        height: vh2(54);
        background: linear-gradient(90deg, #FBED96 0%, #ABECD6 100%);
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        border-radius: 17px;
        line-height: vh2(54);

        .second {
          font-size: vh2(26);
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #000000;
          text-align: center;
        }
      }
    }
  }

  .sheet {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid #FFFFFF;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    .think-image {
      max-height: 98%;
      max-width: 98%;
      object-fit: contain;
    }
  }

  .btn-prev {
    width: 120px;
    height: 48px;
    border-radius: 24px;
    border: 2px solid #C32136;
    background: #FFFFFF;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #C32136;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
    margin-top: 15px;
  }

  .btn-next {
    justify-content: center;
    align-items: center;
    line-height: 1;
    width: 120px;
    height: 48px;
    background: #C32136;
    border-radius: 24px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    margin-top: 15px;
  }
}

.tips-group {
  position: absolute;
  right: vh2(10);
  bottom: vh2(10);
  display: flex;
  gap: vw2(10);
}

.thinking-tips {
  width: vh2(45);
  height: vh2(45);
  min-width: 45px;
  min-height: 45px;
  background: linear-gradient(0deg, #E65C00 3.33%, #F9D423 100%);
  box-shadow: 0px 4.73684px 4.73684px rgba(0, 0, 0, 0.25);
  border-radius: 50%;
  cursor: pointer;

  img {
    width: vh2(15);
    height: vh2(15);
    min-width: 15px;
    min-height: 15px;
    object-fit: contain;
    margin-bottom: 2px;
  }

  .audio {
    width: vh2(23);
    height: vh2(23);
    object-fit: contain;
  }

  span {
    font-size: vh2(12);
    color: white;
  }

  .remark-box {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #BDBDBD;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 10px;
    padding: 13px;
    left: vw(-10);
    bottom: 0;
    transform: translate(-100%, 0);
    font-size: vw(12);
    font-weight: 600;
    word-break: keep-all;
    word-wrap: break-word;
    white-space: pre-line;
    max-width: vw(200);
    max-height: vh2(300);
    overflow: scroll;
    overflow-x: hidden;
    @include aiScrollBar;
    &::-webkit-scrollbar-track-piece {
      background-color: transparent;
    }
  }
}

.tip-box {
  position: absolute;
  width: vh2(613);
  height: vh2(400);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #BDBDBD;
  border-radius: 10px;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;

  .title {
    padding: vh2(20) 0;
    color: #000000;
    text-align: center;
    font-size: vh(20);
    font-weight: 600;
  }

  .tip-image {
    height: vh2(303);
    object-fit: contain;
  }

  .close {
    position: absolute;
    width: vh2(20);
    height: vh2(20);
    top: vh2(20);
    right: vh2(14);
    cursor: pointer;
  }
}

.audio-box {
  cursor: pointer;
  width: vh2(45);
  height: vh2(45);

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
