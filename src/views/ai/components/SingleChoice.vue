<template>
  <div class="question-container-outdoor">
    <div v-show="showTrueAni || showFalseAni" class="ani-overlay"></div>
    <div v-show="showScore" class="ani-score">+ {{ score }}</div>
    <lottie v-show="showTrueAni" :width="isMobile ? 600 : 965" :height="isMobile ? 350 : 650" :options="trueAniOptions" class="animation" @animCreated="handleTrueAnimation" />
    <lottie v-show="showFalseAni" :width="isMobile ? 600 : 965" :height="isMobile ? 350 : 650" :options="falseAniOptions" class="animation" @animCreated="handleFalseAnimation" />
    <div class="question-container">
      <audio ref="taudio" controls="controls" hidden :src="trueAu"></audio>
      <audio ref="faudio" controls="controls" hidden :src="falseAu"></audio>
      <div class="question flex justify-center" v-html="question.question">
      </div>

      <div class="answer flex-col justify-between">
        <div v-if="question.mediaUrl && question.mediaUrl.length > 0" class="answer-media flex">
          <img class="queMedia" :src="question.mediaUrl" alt="" />
          <div class="full-h flex-col align-start">
            <div
              v-for="(item, index) in sliceOptionList"
              :key="item.id"
              class="option-media flex align-center"
              :class="[
                {
                  'select-false': trueAnswer && item.id === +answer && +trueAnswer !== item.id,
                  'selectable': !answer && context.currentSection.aicourseUnitSectionUser && context.currentSection.aicourseUnitSectionUser.completed
                },
                ((trueAnswer && item.id !== +answer && +trueAnswer === item.id)
                  || (trueAnswer && item.id === +answer && +trueAnswer === item.id))
                  ? (answer === null || answer === '') ? 'empty-answer' : 'true-answer'
                  : ''
              ]"
              @click="getAnswer(item)"
            >
              <div class="serial">{{ serialDic[index] }}</div>
              <div class="content">
                <div v-html="item.answer"></div>
                <div class="label">
                  <img class="icon-true" :src="trueIcon" alt="" />
                  <img class="icon-false" :src="falseIcon" alt="" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-else-if="sliceOptionList.length > 0 && sliceOptionList[0].answerType === 'IMAGE'"
          class="full-h full-w flex align-start justify-between option-img-list"
        >
          <div
            v-for="(item, index) in sliceOptionList"
            :key="item.id"
            class="option-img flex-col align-center"
            :class="[
              {
                'select-false': trueAnswer && item.id === +answer && +trueAnswer !== item.id,
                'selectable': !answer && context.currentSection.aicourseUnitSectionUser && context.currentSection.aicourseUnitSectionUser.completed
              },
              ((trueAnswer && item.id !== +answer && +trueAnswer === item.id)
                || (trueAnswer && item.id === +answer && +trueAnswer === item.id))
                ? (answer === null || answer === '') ? 'empty-answer' : 'true-answer'
                : ''
            ]"
            @click="getAnswer(item)"
          >
            <div class="content">
              <img class="full-img" :src="item.answer" alt="" />
            </div>
            <div class="serial">{{ serialDic[index] }}</div>
            <div class="label">
              <img class="icon-true" :src="trueIcon" alt="" />
              <img class="icon-false" :src="falseIcon" alt="" />
            </div>
          </div>
        </div>
        <div v-else class="option-normal-list full-h flex-col align-start">
          <div
            v-for="(item, index) in sliceOptionList"
            :key="item.id"
            class="option flex align-center"
            :class="[
              {
                'select-false': trueAnswer && item.id === +answer && +trueAnswer !== item.id,
                'selectable': !answer && context.currentSection.aicourseUnitSectionUser && context.currentSection.aicourseUnitSectionUser.completed
              },
              ((trueAnswer && item.id !== +answer && +trueAnswer === item.id)
                || (trueAnswer && +trueAnswer === item.id))
                ? (answer === null || answer === '') ? 'empty-answer' : 'true-answer'
                : ''
            ]"
            @click="getAnswer(item)"
          >
            <div class="serial">{{ serialDic[index] }}</div>
            <div class="content">
              <div style="flex: 1" v-html="item.answer"></div>
              <div class="label">
                <img class="icon-true" :src="trueIcon" alt="" />
                <img class="icon-false" :src="falseIcon" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { updateAicourseUserAnswer, getUserAnswer } from '@/api/aicourse'
import { mapGetters } from 'vuex'
import lottie from 'vue-lottie'
import trueAni from '@/assets/animate/true.json'
import falseAni from '@/assets/animate/false.json'
import trueAu from '@/assets/audio/true2-au.mp3'
import falseAu from '@/assets/audio/false2-au.mp3'
import trueIcon from '@/assets/ai-image/ai/icon-true.svg'
import falseIcon from '@/assets/ai-image/ai/icon-false.svg'
export default {
  components: { lottie },
  props: {
    context: {
      type: Object,
      require: true,
      default: () => {}
    },
    question: {
      type: Object,
      require: true,
      default: () => {}
    },
    aicourseUnitId: {
      type: Number,
      require: true
    },
    unitUserId: {
      type: Number,
      require: true
    },
    currentSegmentIndex: {
      type: Number,
      require: true
    },
    currentAnsweredIndex: {
      type: Number,
      require: true
    },
    isTimeout: {
      type: Boolean,
      require: false
    },
    challengeStatus: String,
    stepType: String,
    aicourseUnitSectionId: Number,
    isMobile: Boolean
  },
  data () {
    return {
      studentCourseId: this.$route.params.studentCourseId,
      serialDic: ['A', 'B', 'C'],
      answer: '',
      trueAnswer: '',
      trueAniOptions: { animationData: trueAni, loop: false, autoplay: false },
      trueLottie: '',
      falseAniOptions: { animationData: falseAni, loop: false, autoplay: false },
      falseLottie: '',
      showTrueAni: false,
      showFalseAni: false,
      count: 0,
      trueAu,
      falseAu,
      score: 0,
      showScore: false,
      finished: false,
      token: this.$route.query.token,
      shareUserId: this.$route.query.userId,
      preview: this.$route.query.preview,
      trueIcon,
      falseIcon
    }
  },
  watch: {
    question: {
      deep: true,
      immediate: true,
      handler () {
        if (this.needGetAnswer) this._getUserAnswer()
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    sliceOptionList () {
      const optionList = this.question.answerOptionList || []
      const length = optionList.length
      if (length <= 3) {
        return optionList
      } else {
        return optionList.slice(0, 3)
      }
    },
    needGetAnswer () {
      const currentSection = this.context.currentSection
      const currentPartion = this.context.currentPartion
      console.log(currentSection)
      let isNeed = false
      switch (currentSection.stepType) {
        case 'CHALLENGE':
          var arrIndex = currentPartion.data.findIndex(
            item => {
              return item.aicourseUnitSectionUser &&
                      item.aicourseUnitSectionUser.result &&
                      item.aicourseUnitSectionUser.result === 'WRONG'
            }
          )
          isNeed = arrIndex !== -1 ||
                          (arrIndex === -1 &&
                          currentSection.aicourseUnitSectionUser &&
                          currentSection.aicourseUnitSectionUser.completed &&
                          this.unitUserId)
          break
        case 'COMMON':
          isNeed = currentSection.aicourseUnitSectionUser &&
                          currentSection.aicourseUnitSectionUser.completed &&
                          this.unitUserId &&
                          +this.preview !== 1
          break
        default:
          isNeed = false
      }
      return isNeed
    }
  },
  methods: {
    getAnswer (option) {
      if (this.answer) return
      if (this.context.currentSection.aicourseUnitSectionUser &&
      this.context.currentSection.aicourseUnitSectionUser.completed && +this.preview !== 1) return
      if (this.context.currentSection.stepType === 'CHALLENGE') {
        var arrIndex = this.context.currentPartion.data.findIndex(
          item => {
            return item.aicourseUnitSectionUser.result && item.aicourseUnitSectionUser.result === 'WRONG'
          }
        )
        if (arrIndex !== -1) return
      }
      this.$emit('stopSubCount')
      this.answer = option.id.toString()
      console.log(this.answer)
      this._updateAicourseUserAnswer()
    },
    _updateAicourseUserAnswer () {
      const params = {
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.aicourseUnitId,
        questionId: this.question.id,
        answer: this.answer,
        times: this.count,
        token: this.token || null,
        aicourseUnitSectionId: this.aicourseUnitSectionId
      }
      updateAicourseUserAnswer(params).then(response => {
        if (+response.data.code === 200) {
          const data = response.data.data || {}
          console.log(data)
          this.trueAnswer = data.question.answer
          if (this.context.currentSection.stepType === 'CHALLENGE') {
            if (this.answer === this.trueAnswer) {
              this.$emit('answerRight')
            } else {
              this.$emit('answerWrong')
            }
            return
          }
          if (this.answer === this.trueAnswer) {
            console.log('answer true')
            this.showTrueAni = true
            this.playMusic(true)
            this.handlePlayTrueAnimation()
          } else {
            console.log('answer false')
            this.showFalseAni = true
            this.playMusic(false)
            this.handlePlayFalseAnimation()
          }
        } else {
          this.$toast(response.data.message, {
            position: 'center',
            duration: '2000'
          })
        }
      }).catch(err => {
        console.log(err)
      })
    },
    _getUserAnswer () {
      const params = {
        questionId: this.question.id,
        questionSource: 'AI',
        sourceId: this.unitUserId,
        userId: this.shareUserId || this.userInfo.id,
        aiCourseUnitSectionId: this.aicourseUnitSectionId
      }
      getUserAnswer(params).then(response => {
        if (+response.data.code === 200) {
          this.trueAnswer = this.question.answer
          if (!response.data.data) return
          const data = response.data.data || {}
          this.answer = data.answerIds
        } else {
          this.$toast(response.data.message, {
            position: 'center',
            duration: '2000'
          })
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handleTrueAnimation (anim) {
      console.log('anim')
      this.trueLottie = anim
      this.trueLottie.addEventListener('complete', () => {
        console.log('complete')
        this.showTrueAni = false
        this.showScore = false
        this.$emit('answerRight')
      })
    },
    handleFalseAnimation (anim) {
      this.falseLottie = anim
      this.falseLottie.addEventListener('complete', () => {
        console.log('complete')
        this.showFalseAni = false
        this.$emit('answerWrong')
      })
    },
    handlePlayTrueAnimation () {
      this.trueLottie.play()
      this.score = this.question.score || 0
      setTimeout(() => {
        this.showScore = true
      }, 100)
    },
    handlePlayFalseAnimation () {
      this.falseLottie.play()
    },
    playMusic (type) {
      if (type) {
        this.$refs.taudio.currentTime = 0
        this.$refs.taudio.volume = 0.1
        this.$refs.taudio.play()
        setTimeout(() => {
          this.$refs.taudio.pause()
        }, 3000)
      } else {
        this.$refs.faudio.currentTime = 0
        this.$refs.faudio.volume = 0.1
        this.$refs.faudio.play()
        setTimeout(() => {
          this.$refs.faudio.pause()
        }, 3000)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
@import "@/styles/variables";

.question-container-outdoor {
  width: 100%;
  height: 100%;
  position: relative;
}

.question-container {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
  padding: vh2(20) vh2(110) vh2(20) vh2(20);
  overflow: auto;
  @include aiScrollBar;

  * {
    box-sizing: border-box;
  }
}

.question {
  width: 100%;
  font-size: vh2(30);
  color: #000000;
  margin-bottom: vh2(30);
  line-height: vh2(36);
  word-break: break-all;
  white-space: pre-wrap;
}

.answer {
  width: 100%;
}

.animation {
  position: fixed;
  left: 50%;
  top: 50%;
  // width: 1000px !important;
  transform: translate(-50%, -58%);
  z-index: 9998;
}

.ani-overlay {
  background: rgba(0, 0, 0, 0.2) !important;
  z-index: 9997;
}

.ani-score {
  position: fixed;
  left: 50%;
  top: vh2(400);
  font-size: 60px;
  font-weight: bolder;
  color: #333333;
  z-index: 9999;
  transform: translate(-50%,0);
  font-family: DOUYUFONT;
  animation-name: scoreAni;
  animation-duration: 1s;
}

/* 文字动画代码 */
@keyframes scoreAni {
  0%    { left:70%;transform: translate(-50%,0);}
  30%   { left:45%;transform: translate(-50%,0);}
  50%   { left:50%;transform: translate(-50%,0);}
  70%   { left:50%; transform: scale(1.15) translate(-50%,0);}
  90%   { left:50%; transform: scale(1.07) translate(-50%,0);}
  100%  { left:50%;transform: translate(-50%,0);}
}

.option,
.option-img,
.option-media {
  &.select-true {
    position: relative;
    color: green !important;
  }
}

.option {
  width: 100%;
  cursor: pointer;
}

.option-media {

  &.select-true {
    position: relative;
  }

  &.select-false {
    position: relative;
  }
}

.option-media,
.option {
  margin-bottom: vh2(20);

  &:nth-last-child(1) {
    margin-bottom: 0;
  }

  .serial {
    width: vh2(60);
    height: vh2(60);
    font-weight: 500;
    background: #FFFFFF;
    border: 1px solid #000000;
    border-radius: 18px;
    line-height: vh2(60);
    text-align: center;
    font-size: vh2(26);
    font-weight: 400;
    margin-right: vh2(30);
  }

  .content {
    font-family: 'PingFang SC';
    font-size: vh2(30);
    color: #000000;
    flex: 1;
    word-break: break-all;
    white-space: pre-wrap;
    padding: vh2(8) vh2(12);
    min-height: vh2(60);
    display: flex;
    align-items: center;
  }

  .label {
    width: vh2(40);
    height: vh2(40);
    padding-left: vh2(10);

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .icon-true,
    .icon-false {
      display: none;
    }
  }

  &.selectable:hover {
    cursor: pointer;
  }

  &.select-false {
    position: relative;

    .serial {
      background: #EB5757 !important;
      border: 1px solid #BA4D4D;
      color: white;
    }

    .content {
      background: rgba(235, 87, 87, 0.12);
      border: 1px solid #EB5757;
      border-radius: 9px;
      color: #EB5757;
    }

    .icon-false {
      display: block !important;
    }
  }

  &.true-answer,
  &.empty-answer {
    .serial {
      background: #6FCF97 !important;
      color: white;
      border: 1px solid #219653;
    }

    .content {
      background: rgba(33, 150, 83, 0.12);
      border: 1px solid #219653;
      border-radius: 9px;
      color: #219653;
    }

    .icon-true {
      display: block !important;
    }
  }
}

.option-img-list {
  width: 100%;
  .option-img {
    .serial {
      width: vh2(60);
      height: vh2(60);
      background: #FFFFFF;
      border: 1px solid #000000;
      border-radius: 18px;
      margin-top: vh2(30);
      font-size: vh2(26);
      cursor: pointer;
      text-align: center;
      line-height: vh2(60);
    }

    .content {
      width: vh2(140);
      height: vh2(140);
      position: relative;
    }

    .label {
      width: vh2(30);
      height: vh2(30);
      margin-top: vh2(10);

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .icon-true,
      .icon-false {
        display: none;
      }
    }

    &.true-answer,
    &.empty-answer {
      .serial {
        background: #6FCF97 !important;
        color: white;
        border: 1px solid #219653;
      }

      .icon-true {
        display: block !important;
      }
    }

    &.select-false {
      position: relative;

      .serial {
        background: #EB5757 !important;
        border: 1px solid #BA4D4D;
        color: white;
      }

      .icon-false {
        display: block !important;
      }
    }
  }
}

.option-normal-list {
  margin-top: vh2(30);
}

.queMedia {
  width: vh2(280);
  height: vh2(250);
  border-radius: 10px;
  margin-right: 40px;
}

</style>
