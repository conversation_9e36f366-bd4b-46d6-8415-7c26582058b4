<template>

  <div v-if="isH5">
    <div v-if="showH5Menu" class="h5-menu-box">
      <div class="overlay" @click.stop="closeH5Menu"></div>
      <van-popup
        v-model="showH5Menu"
        position="right"
        :style="{ width: '30%',height: '100%', overflowY: 'clip', background:'#130B13' }"
        :overlay="false"
        :close-on-click-overlay="false"
      >
        <div class="h5-menu-container">
          <div class="header" style="color: white">
            <span>目录</span>
            <div class="close" @click="closeH5Menu">
              <img :src="close" alt="" />
            </div>
          </div>
          <div class="menu-list">
            <div
              v-for="segement in segmentNameList"
              :key="segement.step"
              class="menu-item"
            >
              <div
                class="flex align-center"
                @click="handleSeek(segement.partionList[0])"
              >
                <div
                  class="circle"
                  :class="{
                    'selectable': segement.step === context.currentPartion.data[0].step
                  }"
                ></div>
                <div
                  class="h5-segment"
                  :class="{
                    'selectable': segement.step === context.currentPartion.data[0].step
                  }"
                  v-html="h5StepName(segement)"
                ></div>
              </div>
              <div
                v-for="partion in segement.partionList"
                :key="partion.id"
                class="flex align-center"
                style="margin-top: 10px"
                @click="handleSeek(partion)"
              >
                <div
                  class="h5-part"
                  :class="{
                    'selectable': context.currentPartionIndex === partion.index
                  }"
                >{{ partionName(partion.data[0]) }}</div>
                <img v-if="context.currentPartionIndex === partion.index" class="play-icon" :src="playIcon" alt="" />
              </div>
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </div>

  <div v-else class="navigation flex align-center">
    <div v-if="!previewIndex || source === 'prepare'" class="arrow-left-box flex align-center" @click="handleShowQuit">
      <img class="arrow-left" :src="arrowLeft" />
      <div class="back">返回</div>
    </div>
    <div style="flex: 0.97">
      <div class="segment-list flex">
        <div
          v-for="segement in segmentNameList"
          ref="segment"
          :key="segement.step"
          class="segment-item"
          :style="`flex:${segement.flex}`"
        >
          <el-popover
            :key="segement.step"
            placement="bottom-start"
            trigger="hover"
            :content="stepName(segement)"
            :disabled="!segement.ellipse"
          >
            <div
              ref="segmentText"
              slot="reference"
              class="text"
              :class="{
                // 'text-grey': !segement.canSeek,
                'ellipsisMore': segement.ellipse
              }"
            >{{ stepName(segement) }}</div>
          </el-popover>
        </div>
      </div>
      <div class="partion-list flex align-end">
        <div
          v-for="partion in context.partionList"
          :key="partion.index"
          class="partion-item"
          :style="`flex:${partionLong(partion)}`"
          @click="handleSeek(partion)"
        >
          <div
            class="line"
            :class="{
              'line-crude': ['QUESTION', 'THINK', 'SCREEN_INTERACT', 'TRAINING'].indexOf(partion.data[0].aiSectionType) > -1,
              'line-blue': canSeek(partion),
              'line-orange': context.currentPartionIndex === partion.index,
            }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import arrowLeft from '@/assets/images/ai/arrow-left.png'
import close from '@/assets/ai-image/icon/close2.svg'
import playIcon from '@/assets/ai-image/ai/play-icon.gif'
import { Popup } from 'vant'
import 'vant/lib/popup/style'
Vue.use(Popup)

export default {
  props: {
    context: Object,
    isH5: Boolean
  },
  data () {
    return {
      arrowLeft,
      close,
      playIcon,
      segmentNameList: [],
      preview: this.$route.query.preview,
      previewIndex: this.$route.query.previewIndex,
      showH5Menu: false,
      source: this.$route.query.source || ''
    }
  },
  mounted () {
    this.createSegmentList()
  },
  methods: {
    //  片段长度
    partionLong (partion) {
      let duration = 0
      for (var section of partion.data) {
        duration += section.time
      }
      if (+duration < 60) {
        return 1
      } else if (+duration >= 60 && +duration < 180) {
        return 2
      } else if (+duration >= 180 && +duration < 300) {
        return 3
      } else if (+duration >= 300 && +duration < 600) {
        return 4
      } else if (+duration >= 600) {
        return 5
      } else {
        return 1.5
      }
    },
    //  创建阶段名字
    createSegmentList () {
      const partionList = this.context.partionList
      for (var partion of partionList) {
        var arrindex = this.segmentNameList.findIndex(
          (item) => {
            return item.step === partion.data[0].step
          })
        let times = 0
        for (const section of partion.data) {
          times += section.time
        }
        if (arrindex === -1) {
          this.segmentNameList.push({
            'step': partion.data[0].step,
            'stepName': partion.stepName,
            'flex': this.partionLong(partion),
            'canSeek': this.canSeek(partion),
            'time': times,
            'partionList': [partion]
          })
        } else {
          this.segmentNameList[arrindex].flex += this.partionLong(partion)
          this.segmentNameList[arrindex].time += times
          this.segmentNameList[arrindex].partionList.push(partion)
        }
      }
      if (!this.isH5) {
        this.$nextTick(() => {
          for (let index = 0; index < this.segmentNameList.length; index++) {
            const ev_weight = this.$refs.segmentText[index].clientWidth // 文本的实际宽度
            const content_weight = this.$refs.segment[index].clientWidth // 文本容器宽度(父节点)
            if (ev_weight >= content_weight) {
            // 文本宽度 > 实际内容宽度  --> 内容溢出,则显示悬浮
              this.segmentNameList[index].ellipse = true
            } else {
            // 否则为未溢出，不显示悬浮
              this.segmentNameList[index].ellipse = false
            }
          }
          this.$forceUpdate()
        })
      }
    },
    stepName (segment) {
      let stepName = segment.stepName
      if (segment.time > 0) {
        var numReg = /^[0-9]*$/
        var numRe = new RegExp(numReg)
        if (numRe.test(stepName.slice(stepName.length - 1, stepName.length))) {
          stepName += `·${Math.ceil(segment.time / 60)}分钟`
        } else {
          stepName += `${Math.ceil(segment.time / 60)}分钟`
        }
      }
      return stepName
    },
    h5StepName (segment) {
      let stepName = segment.stepName
      if (segment.time > 0) {
        stepName += `&nbsp;&nbsp;${Math.ceil(segment.time / 60)}分钟`
      }
      return stepName
    },
    //  该片段是否能够跳转
    canSeek (partion) {
      let canSeek = false
      if (partion.index === 0) {
        canSeek = true
      } else {
        if (partion.stepType === 'CHALLENGE') {
          const arrIndex = partion.data.findIndex(
            item => {
              return item.aicourseUnitSectionUser && item.aicourseUnitSectionUser.result === 'WRONG'
            }
          )
          if (arrIndex !== -1) {
            canSeek = true
          } else if (
            partion.data[partion.data.length - 1].aicourseUnitSectionUser &&
            partion.data[partion.data.length - 1].aicourseUnitSectionUser.completed
          ) {
            canSeek = true
          }
        } else if (partion.stepType === 'REPORT') {
          const prePartion = this.context.partionList[partion.index - 1]
          if (prePartion.data[prePartion.data.length - 1].aicourseUnitSectionUser &&
          prePartion.data[prePartion.data.length - 1].aicourseUnitSectionUser.completed
          ) {
            canSeek = true
          }
        } else if (
          partion.data[partion.data.length - 1].aicourseUnitSectionUser &&
          partion.data[partion.data.length - 1].aicourseUnitSectionUser.completed
        ) {
          canSeek = true
        }
      }
      return canSeek
    },
    handleSeek (partion) {
      // if (this.canSeek(partion)) {
      var seekIndex = this.context.sectionList.findIndex(
        item => {
          return item.id === partion.data[0].id
        }
      )
      this.$emit('seek', seekIndex)
      // } else {
      //   this.$message.error('未解锁该片段')
      // }
    },
    handleShowQuit () {
      this.$emit('showQuit')
    },
    openH5Menu () {
      this.showH5Menu = true
    },
    closeH5Menu () {
      this.showH5Menu = false
    },
    partionName (section) {
      // switch (section.aiSectionType) {
      //   case 'VIDEO':
      //     return '探索新知'
      //   case 'QUESTION':
      //     return '勤学勤练'
      //   case 'THINK':
      //     return '奇思妙想'
      //   case 'REPORT':
      //     return '课程报告'
      //   case 'PREVIEW':
      //     return '前置课程'
      //   case 'LEARNING_ACHIEVED':
      //     return '课中成果'
      //   case 'AFTER_ACHIEVED':
      //     return '课后成果'
      //   case 'NEXT_PREVIEW':
      //     return '前置课程'
      //   case 'SCREEN_INTERACT':
      //     return '趣味竞赛'
      // }
      return section.tagName
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.navigation {
    position: absolute;
    top: 0;
    width: 100%;
    height: vh(60);
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(51, 51, 51, 0) 100%);
    padding-left: vh(30);
    z-index: 2000;

    .arrow-left-box {
        padding-top: vh(13);
        height: vh(22);
        cursor: pointer;
        margin-right: vh(20);

        .arrow-left{
            width: vh(16);
            height: vh(16);
            object-fit: contain;
        }

        .back {
            font-size: vh(16);
            color: #FFFFFF;
            margin-left: vh(2);
        }
    }

    .segment-list {
        width: 100%;
        height: 20px;
        padding-top: vh(12);

        .segment-item {
            width: 100%;
        }

        .text {
            line-height: 20px;
            display: inline-block;
            color: #FFFFFF;
            font-size: vh(10);
            cursor: default;
        }

        .ellipsisMore {
            @include ellipsisMore(1)
        }

        .text-grey {
            color: #C2C2C2;
        }
    }

    .partion-list {
        width: 100%;
        padding-right: 40px;

        .partion-item {
            height: 100%;
            cursor: pointer;
        }

        .line {
            width: calc(100% - 6px);
            height: 4px;
            background: #FFFFFF;
            margin-top: 2px;
        }

        .line-crude {
            height: 7px;
            margin-top: 1px;
        }

        .line-blue {
            background: rgba(45, 156, 219, 1);
        }

        .line-orange {
            background: rgba(242, 153, 74, 1);
        }
    }
}

.h5-menu-box {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;

  .overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2000;
  }
}

.h5-menu-container {
  height: 100%;
  padding: vw2(20);
  display: flex;
  flex-direction: column;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-weight: 500;
      font-size: vw2(18);
      line-height: vw2(25);
    }

    .close {
      height: vw2(25);
      width: vw2(14);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    img {
      width: vw2(15);
      height: vw2(15);
      object-fit: contain;
    }
  }

  .menu-list {
    padding: vw2(10) 0;
    overflow: scroll;
    overflow-x: auto;
    flex: 1;
    @include scrollBarHidden;

    .menu-item {
      margin-bottom: vw2(15);
      background: transparent !important;
    }

    .circle {
      width: vw2(4);
      height: vw2(4);
      background: white;
      border-radius: 100%;
      margin-right: vw2(2);
    }

    .h5-part,
    .h5-segment {
      color: #FFFFFF;
      font-size: vw2(16);
      line-height: vw2(20);
      margin-right: vw2(4);
      background: transparent !important;
    }

    .play-icon {
      height: vw2(13);
      margin-left: vw2(14);
      object-fit: contain;
    }

    .h5-part {
      margin-left: vw2(6);
    }

    .selectable {
      color: #56CCF2;
      background: #56CCF2;;
    }
  }
}
</style>

<style lang="scss">
.el-popover {
    min-width: 40px;
    text-align: center
}
.el-popover--plain {
    padding: 10px;
}
.el-popper[x-placement^=bottom] .popper__arrow {
    left: 10px !important;
}
</style>
