<template>
  <div class="next-container-bg" :style="{background: `url(${backcover || bg})`}">
    <div class="next-container">
      <div class="tips">即将进入下一个环节</div>
      <div class="count">{{ second }}s</div>
      <div class="row">
        <div class="btn btn-replay" @click="$emit('replay')">
          <span class="icon-refresh"></span>
          <span>重播</span>
        </div>
        <div class="btn btn-next" @click="$emit('next')">
          <span>{{ isLastSegment ? '查看成绩' : '继续学习' }}</span>
          <span class="icon-next"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import bg from '@/assets/images/ai/ai-bg.png'
export default {
  name: 'Next',
  props: {
    context: Object,
    backcover: String
  },
  data () {
    return {
      second: 5,
      timer: null,
      bg
    }
  },
  computed: {
    isLastSegment: function () {
      if (this.context.currentSegment === null) return true
      return this.context.currentSegment.id === this.context.lastSegment.id
    }
  },
  created () {
    this.timer = setInterval(() => this.subCount(), 1000)
  },
  destroyed () {
    clearInterval(this.timer)
  },
  methods: {
    subCount () {
      // this.second -= 1;
      // if(this.second <= 0) {
      //     setTimeout(() => {
      //         this.$emit("next");
      //     }, 100);
      // }
    }
  }
}
</script>

<style lang="scss">
.next-container {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 847px;
    height: 491px;
    background: rgba(255, 255, 255, 0.75);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    backdrop-filter: saturate(180%) blur(20px);
    border-radius: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

.next-container-bg {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100%;
    height: 100%;
    background: url('../../../assets/images/ai/ai-bg.png');
    background-position: center center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    transform: translate(-50%, -50%);
    z-index: 1001;
}

.tips {
    font-size: 40px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #2C304E;
    line-height: 56px;
}

.count {
    font-size: 60px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FF8914;
    line-height: 84px;
    margin-top: 45px;
    margin-bottom: 50px;
}

.row {
    display: flex;
    justify-content: space-between;
    width: 385px;
}

.btn {
   display: flex;
   justify-content: center;
   align-items: center;
   box-sizing: border-box;
   cursor: pointer;
}

.btn-replay {
    width: 169px;
    height: 67px;
    border-radius: 15px;
    border: 3px solid #FF8914;
    text-align: center;
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FF8914;
    line-height: 28px;

    .icon-refresh {
        margin-right: 10px;
        height: 20px;
        width: 20px;
        background: url('../../../assets/images/ai/refresh-primary.png') center center no-repeat;
        background-size: contain;
    }

    &:hover {
        border: none;
        background: #FF8914;
        color: #FFFFFF;

        .icon-refresh {
            margin-right: 10px;
            height: 20px;
            width: 20px;
            background: url('../../../assets/images/ai/refresh-default.png') no-repeat;
            background-size: contain;
        }
    }
}

.btn-next {
    width: 169px;
    height: 67px;
    background: #FF8914;
    border-radius: 15px;
    text-align: center;
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 28px;

    .icon-next {
        margin-left: 10px;
        height: 20px;
        width: 20px;
        background: url('../../../assets/images/ai/next-arrow.png') no-repeat;
        background-size: contain;
    }

    &:hover {
        box-shadow: 0px 3px 10px 0px rgba(255,150,61,74%);
    }
}

</style>
