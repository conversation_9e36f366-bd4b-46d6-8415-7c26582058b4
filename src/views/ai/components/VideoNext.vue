<template>
  <div class="video-next flex align-center">
    <div class="f12 btn mr30" @click="seekSection">上一步</div>
    <div class="f12 mr10">下一环节</div>
    <div class="f12 border mr30">{{ labelName }}</div>
    <!-- <div class="f12 mr30">{{ partionName }}</div> -->
    <div class="f12 btn" @click="onPlayerEnd">立即开始</div>
  </div>
</template>

<script>
export default {
  props: {
    context: Object,
    currentSectionIndex: Number
  },
  computed: {
    labelName () {
      const nextSection = this.context.sectionList[this.currentSectionIndex + 1]
      switch (nextSection.aiSectionType) {
        case 'VIDEO':
          return '视频'
        case 'QUESTION':
          if (nextSection.stepType === 'CHALLENGE') {
            return '挑战'
          } else {
            return '答题'
          }
        case 'THINK':
          return '思考'
        case 'REPORT':
          return '报告'
        case 'PREVIEW':
        case 'LEARNING_ACHIEVED':
        case 'AFTER_ACHIEVED':
        case 'NEXT_PREVIEW':
          return '作业'
        case 'SCREEN_INTERACT':
          return '竞赛'
        default:
          return ''
      }
    },
    partionName () {
      const nextSection = this.context.sectionList[this.currentSectionIndex + 1]
      // switch (nextSection.aiSectionType) {
      //   case 'VIDEO':
      //     return '探索新知'
      //   case 'QUESTION':
      //     if (nextSection.stepType === 'CHALLENGE') {
      //       return '终极挑战'
      //     } else {
      //       return '勤学勤练'
      //     }
      //   case 'THINK':
      //     return '奇思妙想'
      //   case 'REPORT':
      //     return '课程报告'
      // }
      return nextSection.tagName
    }
  },
  methods: {
    onPlayerEnd () {
      this.$emit('onPlayerEnd')
    },
    seekSection () {
      const index = this.context.currentSectionIndex - 1
      this.$emit('seekSection', index)
    }
  }
}
</script>

<style lang="scss" scoped>
@function rem($px) {
  @return $px * 100vh / 650;
}
.video-next {
    position: absolute;
    right: 80px;
    bottom: 40px;
    // min-width: rem(265);
    background: rgba(11, 11, 26, 0.79);
    border-radius: 4px;
    padding: rem(7) rem(10) rem(7) rem(16);
    z-index: 100;
}

.f12 {
    font-size: rem(12);
    color: white;
}

.border {
    background: #484E54;
    border: 1px solid #FFFFFF;
    border-radius: 3px;
    text-align: center;
    padding: rem(0.5) rem(4.5);
}

.btn {
    width: rem(65);
    height: rem(26);
    background: #488DF7;
    border-radius: 4px;
    line-height: rem(26);
    text-align: center;
    cursor: pointer;
}

.mr5 {
    margin-right: rem(5);
}

.mr10 {
    margin-right: rem(10);
}

.mr30 {
    margin-right: rem(30);
}
</style>
