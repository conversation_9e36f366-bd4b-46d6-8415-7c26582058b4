<template>
  <div class="progress-item">
    <!-- 互动答题 -->
    <template v-if="type === 1">
      <div
        class="title-item flex align-center"
        :class="[{ 'title-finished': isFinished , 'title-ongoing':isOngoing() }]"
        @click="handleClick(list[0].indexNo -1)"
      >
        <div class="type-icon">
          <img :src="[isOngoing() ? pencilIcon2 : isFinished ? pencilIcon3 : pencilIcon1]" alt="icon" />
        </div>

        <div class="type-name" :class="{'type-ongoing': isOngoing()}">勤学勤练</div>
      </div>

      <div class="question-list flex">
        <swiper v-if="list && list.length > 0" :ref="'swiper'+index" :options="swiperOption" @slideChange="showSwiperButton">
          <swiper-slide v-for="(item, index) in list" :key="item.id">
            <div
              class="bg-question"
              :class="[item.indexNo - 1 === segmentIndex
                ? 'bg-question-light'
                : 'bg-question-dark']"
              @click="handleClick(item.indexNo - 1)"
            >
              <div class="res-icon">
                <template
                  v-if="
                    item.aicourseUnitSectionUser &&
                      item.aicourseUnitSectionUser.completed
                  "
                >
                  <img v-if="isCorrect(item)" :src="trueIcon" alt="icon" />
                  <img v-else :src="falseIcon" alt="icon" />
                </template>
              </div>
              <div class="question-name" :class="[ item.indexNo - 1 === segmentIndex ? 'question-light' : 'question-dark' ]">{{ index + 1 }}</div>
            </div>
          </swiper-slide>
        </swiper>
        <img v-show="list.length > 3 && showSwpPre" src="@/assets/images/ai/ai-icon-right.png" class="pre-btn" :class="preBtnName" />
        <img v-show="list.length > 3 && !showSwpPre" src="@/assets/images/ai/ai-icon-right-grey.png" class="pre-btn" :class="preBtnName" />
        <img v-show="list.length > 3 && showSwpNext" src="@/assets/images/ai/ai-icon-right.png" class="next-btn" :class="nextBtnName" />
        <img v-show="list.length > 3 && !showSwpNext" src="@/assets/images/ai/ai-icon-right-grey.png" class="next-btn" :class="nextBtnName" />
      </div>
    </template>
    <!-- 互动视频 -->
    <template v-if="type === 0">
      <div
        v-for="item in list"
        :key="item.id"
        class="title-item flex align-center"
        :class="[{ active: active(item.indexNo - 1), playback: playback, 'title-finished': isFinished , 'title-ongoing': isOngoing(item) }]"
        @click="handleClick(item.indexNo - 1)"
      >
        <div class="type-icon">
          <img :src="[isOngoing(item) ? videoIcon2 : isFinished ? videoIcon1 : videoIcon3]" alt="icon" />
        </div>

        <div class="type-name" :class="{'type-ongoing': isOngoing(item)}">学一学</div>

        <div v-if="isOngoing(item)" class="play-icon">
          <img :src="playIcon" alt="icon" />
        </div>
      </div>
    </template>
    <!-- 终极挑战 -->
    <template v-if="type === 2">
      <div
        v-for="(item,index) in list"
        :key="item.id"
      >
        <div
          v-if="index === 0"
          class="title-text"
          :class="[{ active: active(item.indexNo - 1), playback: playback, '': isFinished , '': isOngoing(item) }]"
          @click="handleClick(item.indexNo - 1)"
        >
          <!-- <div class="type-icon">
            <img :src="[isOngoing(item) ? crownIcon2 : isFinished ? crownIcon3 : crownIcon1]" alt="icon" />
          </div> -->

          <div class="type-text" :class="{'type-ongoing': isOngoing(item),'type-text-finished': isFinished}">终极挑战</div>

        </div>
      </div>
    </template>
    <!-- 课程报告 -->
    <template v-if="type === 3">
      <div
        v-for="item in list"
        :key="item.id"
        class="title-text"
        :class="[{ '': isReportFinish , '': isOngoing(item) }]"
        @click="handleClick(item.indexNo - 1)"
      >
        <div
          class="type-text"
          :class="{
            'type-ongoing': isOngoing(item),'type-text-finished': isReportFinish
          }"
        >课程报告</div>
      </div>
    </template>
    <!-- 课前互动 -->
    <template v-if="type === 4">
      <div
        v-for="item in list"
        :key="item.id"
        class="title-text"
        :class="[{ '': isReportFinish , '': isOngoing(item) }]"
        @click="handleClick(item.indexNo - 1)"
      >
        <!-- <div class="type-icon">
          <img :src="[isOngoing(item) ? reportIcon2 : isReportFinish ? reportIcon3 : reportIcon1]" alt="icon" />
        </div> -->

        <div class="type-text type-text-finished">课前互动</div>
      </div>
    </template>
    <!-- 奇思妙想 -->
    <template v-if="type === 5">
      <div
        v-for="item in list"
        :key="item.id"
        class="title-item flex align-center"
        :class="[{ active: active(item.indexNo - 1), playback: playback, 'title-finished': isFinished , 'title-ongoing': isOngoing(item) }]"
        @click="handleClick(item.indexNo - 1)"
      >
        <div class="type-icon">
          <img :src="[isOngoing(item) ? bulbIcon2 : isFinished ? bulbIcon1 : bulbIcon3]" alt="icon" />
        </div>

        <div class="type-name" :class="{'type-ongoing': isOngoing(item)}">奇思妙想</div>
      </div>
    </template>
  </div>
</template>

<script>
import videoIcon1 from '@/assets/images/ai/video-icon-1.png'
import videoIcon2 from '@/assets/images/ai/video-icon-2.png'
import videoIcon3 from '@/assets/images/ai/video-icon-3.png'
import pencilIcon1 from '@/assets/images/ai/pencil.png'
import pencilIcon2 from '@/assets/images/ai/pencil-ongoing.png'
import pencilIcon3 from '@/assets/images/ai/pencil-finished.png'
import crownIcon1 from '@/assets/images/ai/crown-unfinished.png'
import crownIcon2 from '@/assets/images/ai/crown-ongoing.png'
import crownIcon3 from '@/assets/images/ai/crown-finished.png'
import reportIcon1 from '@/assets/images/ai/report-unfinished.png'
import reportIcon2 from '@/assets/images/ai/report-ongoing.png'
import reportIcon3 from '@/assets/images/ai/report-finished.png'
import bulbIcon1 from '@/assets/images/ai/bulb-blue.png'
import bulbIcon2 from '@/assets/images/ai/bulb-yellow.png'
import bulbIcon3 from '@/assets/images/ai/bulb.png'
import trueIcon from '@/assets/images/ai/true-icon.png'
import falseIcon from '@/assets/images/ai/false-icon.png'
import playIcon from '@/assets/images/ai/play-icon.gif'
import arrowIcon from '@/assets/images/ai/ai-section-a-r.png'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    playback: {
      type: Boolean,
      default: false
    },
    currentIndex: {
      type: Number,
      default: 0
    },
    segmentIndex: {
      type: Number,
      default: 0
    },
    stepIndex: {
      type: Number,
      default: 0
    },
    isReportFinish: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: -1
    }
  },
  data () {
    return {
      videoIcon1,
      videoIcon2,
      videoIcon3,
      pencilIcon1,
      pencilIcon2,
      pencilIcon3,
      crownIcon1,
      crownIcon2,
      crownIcon3,
      reportIcon1,
      reportIcon2,
      reportIcon3,
      bulbIcon1,
      bulbIcon2,
      bulbIcon3,
      playIcon,
      arrowIcon,
      trueIcon,
      falseIcon,
      showSwpPre: false,
      showSwpNext: false
    }
  },
  computed: {
    type () {
      this.list.length && console.log(this.list[0].aiSectionType)
      if (this.list[0].stepType === 'WARMUP') {
        return 4
      }
      if (this.list[0].stepType === 'REPORT') {
        return 3
      }
      if (this.list[0].stepType === 'CHALLENGE') {
        return 2
      }
      if (this.list.length && this.list[0].aiSectionType === 'QUESTION') {
        return 1
      }
      if (this.list[0].aiSectionType === 'THINK') {
        return 5
      }
      return 0
    },
    isFinished () {
      var item = this.list[this.list.length - 1]
      return this.currentIndex > item.indexNo - 2
    },
    correctCount () {
      const correctList = this.list.filter((item) => {
        if (
          item.aicourseUnitSectionUser &&
            item.aicourseUnitSectionUser.result
        ) {
          const result = item.aicourseUnitSectionUser.result
          if (result === 'CORRECT') {
            return true
          }
        } else {
          if (
            item.aicourseUnitSectionUser &&
              item.aicourseUnitSectionUser.score
          ) {
            return true
          }
        }
        return false
      })
      return correctList.length
    },
    wrongCount () {
      const wrongList = this.list.filter((item) => {
        if (
          item.aicourseUnitSectionUser &&
            item.aicourseUnitSectionUser.result
        ) {
          const result = item.aicourseUnitSectionUser.result
          if (result === 'WRONG') {
            return true
          }
        } else {
          if (
            item.aicourseUnitSectionUser &&
              item.aicourseUnitSectionUser.completed &&
              !item.aicourseUnitSectionUser.score
          ) {
            return true
          }
        }
        return false
      })
      return wrongList.length
    },
    swiperOption () {
      return {
        slidesPerView: 3,
        spaceBetween: 30,
        slidesPerGroup: 3,
        loop: false,
        loopFillGroupWithBlank: true,
        navigation: {
          nextEl: '.next-btn' + this.index,
          prevEl: '.pre-btn' + this.index
        }
      }
    },
    nextBtnName () {
      return 'next-btn' + this.index
    },
    preBtnName () {
      return 'pre-btn' + this.index
    },
    swiperRef () {
      return 'swiper' + this.index
    }
  },
  mounted () {
    console.log('list:', this.list)
    this.showSwiperButton()
  },
  methods: {
    handleClick (i) {
      this.$emit('click', i)
    },
    active (i) {
      return i <= this.currentIndex || this.playback
    },
    isCorrect (item) {
      if (
        item.aicourseUnitSectionUser &&
          item.aicourseUnitSectionUser.result
      ) {
        const result = item.aicourseUnitSectionUser.result
        if (result === 'CORRECT') {
          return true
        }
      } else {
        if (
          item.aicourseUnitSectionUser &&
            item.aicourseUnitSectionUser.score
        ) {
          return true
        }
      }
      return false
    },
    isOngoing (item) {
      switch (this.type) {
        case 0:
        case 5:
          return item.indexNo - 1 === this.segmentIndex
        case 1:
          return this.list[0].indexNo - 1 <= this.segmentIndex &&
                   this.list[this.list.length - 1].indexNo - 1 >= this.segmentIndex
        default:
          return item.step === this.stepIndex
      }
    },
    showSwiperButton () {
      if (this.list && this.index !== -1) {
        this.$nextTick(function () {
          const swiper = eval('this.$refs.swiper' + this.index)
          if (swiper) {
            this.showSwpNext = !swiper.$swiper.isEnd
            this.showSwpPre = !swiper.$swiper.isBeginning
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@function rem($px) {
  @return $px * 100vh / 650;
}
.progress-item {
  position: relative;
  width: 100%;
  cursor: pointer;
  margin-left: rem(-9);

  .title-item {
    width: 100%;
    height: rem(58);
    background: url("~assets/images/ai/bg-item-3.png") center center no-repeat;
    background-size: contain;
    margin-left: auto;
  }

  .title-finished {
    background: url("~assets/images/ai/bg-item-1.png") center center no-repeat;
    background-size: contain;
  }

  .title-ongoing {
    background: url("~assets/images/ai/bg-item-2.png") center center no-repeat;
    background-size: contain;
  }

  .title-text {
    width: 100%;
    height: rem(58);
    margin-left: rem(9);
  }

  .type-text {
    font-size: rem(20);
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(137, 210, 236, 0.6);
    line-height: rem(58);
  }

  .type-text-finished {
    color: rgba(137, 210, 236, 1);
  }

  .type-icon {
    width: rem(20);
    height: rem(20);
    margin-left: rem(18);
    margin-right: rem(23);
  }

  .type-name {
    font-size: rem(16);
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    line-height: rem(22);
  }

  .type-ongoing {
    color: #FFF4B1;
  }

  .play-icon {
    width: rem(26);
    height: rem(35);
    margin-left: auto;
    margin-right: rem(10);
  }

  .question-list {
      // padding: rem(13) 0 rem(27) 0;
      margin: rem(13) 0 rem(27) rem(21);
      position: relative;
      // overflow-x: scroll;

      .bg-question {
        display: inline-block;
        width: rem(69);
        height: rem(46);
        position: relative;
        margin-right: rem(14);

        .question-name {
          position: absolute;
          right: rem(14);
          bottom: rem(10);
          width: rem(8);
          height: rem(8);
          font-size: rem(8);
          font-family: DouYuFont;
          font-weight: normal;
          line-height: rem(9);
          z-index: 8;
        }

        .question-light {
          color: #132C4B;
        }

        .question-dark {
          color: #1C1C1C;
        }
      }

      .bg-question-light {
          background: url("~assets/images/ai/bg-question-light.png") center center no-repeat;
          background-size: contain;
      }

      .bg-question-dark {
          background: url("~assets/images/ai/bg-question-dark.png") center center no-repeat;
          background-size: contain;
      }

      .res-icon {
        position: absolute;
        width: rem(42);
        height: rem(42);
        z-index: 9;
        left: calc(50% - 21px);
        top: calc(50% - 21px);
      }

      .pre-btn,
      .next-btn {
        width: rem(20);
        height: rem(20);
        object-fit: contain;
        position: absolute;
        transform: translate(-50%,-50%);
        top: 50%;
        z-index: 999;
      }

      .next-btn {
        right: rem(-40);
      }

      .pre-btn {
        transform: translate(-50%,-50%) rotateY(180deg);
        left: rem(-20);
      }

      .swiper-container {
        width: 100%;
      }
  }

  // .question-list::-webkit-scrollbar {display:none}
  /* 设置滚动条的样式 */
    .question-list::-webkit-scrollbar {
        width:100%;
    }
    /* 滚动槽 */
    .question-list::-webkit-scrollbar-track {
    -webkit-box-shadow:inset006pxrgba(0,0,0,0.3);
    border-radius:10px;
    }
    /* 滚动条滑块 */
    .question-list::-webkit-scrollbar-thumb {
      border-radius:10px;
      background:rgba(40, 111, 168,0.7);
      -webkit-box-shadow:inset006pxrgba(0,0,0,0.5);
    }

    .question-list::-webkit-scrollbar-thumb:window-inactive {
      background:rgba(40, 111, 168 , 0.7);
    }
}

img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
