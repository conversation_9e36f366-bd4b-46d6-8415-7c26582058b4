<template>
  <div class="python-main" v-if='show'>
    <div class="header_view">
      {{ this.trainingData ? this.trainingData.trainingName : 'Python 实验' }}
      <div class='option'>
        <div class='option_btn' @click='openDialog'>
          <i class="el-icon-document svgIcon"></i>
          实验说明
        </div>
      </div>
    </div>
    <div class='content_view'>
      <iframe
        id="python-iframe"
        ref="pythonFrame"
        :src="iframeUrl"
        style="border: none"
        width="100%"
        height="100%"
        allowfullscreen
        allow="microphone *; camera *"
        sandbox="allow-same-origin allow-scripts allow-popups allow-modals allow-forms allow-downloads allow-presentation"
      ></iframe>
    </div>
    <ExplanationDialog ref='explanationRef' />
  </div>
</template>

<script>
import ExplanationDialog from '@/components/ExplanationDialog'
export default {
  name: 'PythonView',
  components: {
    ExplanationDialog
  },
  data () {
    return {
      show: false,
      trainingData: null,
      iframeUrl: '/jupyterhub/hub/logout'
    }
  },
  methods: {
    async open (data) {
      this.show = true
      this.trainingData = data
    },
    back () {
      this.show = false
      this.trainingData = null
    },
    openDialog () {
      this.$refs.explanationRef.openDialog(this.trainingData)
    }
  }
}
</script>

<style scoped lang='scss'>
.python-main{
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: white;
  border-radius: 10px;
  overflow: auto;
  box-sizing: border-box;
  overflow: hidden;
  .header_view{
    width: 100%;
    height: 40px;
    font-size: var(--font-size-XXL);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .option{
      position: absolute;
      right: 15px;
      font-size: var(--font-size-XL);
      display: flex;
      .option_btn{
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        font-size: var(--font-size-M);
        margin-left: 10px;
      }
      .svgIcon{
        font-size: 20px;
      }
    }
  }
  .content_view{
    width: 100%;
    height: calc(100% - 40px);
  }
}
</style>
