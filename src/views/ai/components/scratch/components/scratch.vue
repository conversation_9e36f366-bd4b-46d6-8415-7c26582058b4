<template>
  <div class='scratch_main' v-if='showScratch' v-loading="loading">
    <div class='header_view' :class="{'displayNone': isFullScreen, 'displayFlex': !isFullScreen}">
      {{ experimentTitle }}
      <div class='option'>
        <div class='option_btn' @click="openDialog">
          <i class="el-icon-document svgIcon"></i>
          实验说明
        </div>
        <div class='option_btn' @click="handleReset">
          <i class="el-icon-refresh-right svgIcon"></i>
          重置
        </div>
        <div v-if="scratchType === 'class'" class='option_btn' @click="back">
          <i class="el-icon-circle-close svgIcon"></i>
          关闭
        </div>
      </div>
    </div>
    <div class='content_view'>
      <iframe
        id="scratch-iframe"
        ref="scratchFrame"
        :src="scratchUrl"
        style="border: none"
        width="100%"
        height="100%"
        allowfullscreen
        allow="microphone *; camera *"
        sandbox="allow-same-origin allow-scripts allow-popups allow-modals allow-forms allow-downloads allow-presentation"
      ></iframe>
    </div>
    <el-dialog
      v-if="dialogShow"
      :title="experimentTitle"
      width="920px"
      :visible.sync="dialogShow"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="closeDialog"
      :append-to-body="true"
    >
      <div class="explanation_main">
        <div class="explanation_left">
          <video v-if="videoUrl && videoUrl !== ''" :src="videoUrl" style="width: 100%;height: 50%;border-radius: 20px" controls :poster="videoPoster"></video>
          <el-image v-else style="width: 100%;height: 50%;border-radius: 20px" :src="DefaultCover" fit="cover"/>
          <div class='know_btn' @click='dialogShow = false'>知道了</div>
        </div>
        <div class="explanation_right">
          <div class="title">实验说明:</div>
          <div class="explanation_des" v-html="trainingData ? trainingData.description : ''">
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DefaultCover from '@/assets/scratch/explanationDefault.png'

export default {
  props: {
    sectionId: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      DefaultCover,
      showScratch: false,
      scratchFile: '',
      dialogShow: true,
      experimentTitle: '',
      videoUrl: '',
      videoPoster: '',
      trainingData: null,
      loading: false,
      isFullScreen: false,
      scratchType: 'class'
    }
  },
  mounted () {
    // 可以在这里进行数据获取或其他初始化操作
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy () {
    window.removeEventListener('message', this.handleMessage)
  },
  methods: {
    handleMessage (e) {
      if (e.data.type === 'scratchFullScreenEvent') {
        // 处理全屏事件
        this.isFullScreen = e.data.status === 'enterFullScreen'
      }
    },
    async open (data, type = 'class') {
      this.showScratch = true
      this.scratchType = type
      this.dialogShow = this.scratchType === 'class'
      this.trainingData = data
      this.videoUrl = this.trainingData.descriptionVideo
      this.scratchFile = this.trainingData.scratchFile
      this.experimentTitle = this.trainingData.trainingName
    },
    back () {
      this.showScratch = false
      this.trainingData = null
      this.$emit('close')
    },
    closeDialog () {
      this.dialogShow = false
    },
    openDialog () {
      this.dialogShow = true
    },
    handleReset () {
      this.$confirm('确定要重置项目吗？所有更改将丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const iframe = document.getElementById('scratch-iframe')
        iframe.contentWindow.postMessage({
          type: 'RESET_PROJECT',
          timestamp: Date.now()
        }, 'https://scratch-qa.binguoketang.com')
      })
    }
  },
  computed: {
    scratchUrl () {
      return `${process.env.VUE_APP_SCRATCH_URL}?project=${this.scratchFile}`
      // return `http://localhost:8601/?project=${this.scratchFile}`
    }
  }
}
</script>

<style scoped lang='scss'>
.scratch_main{
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  .header_view{
    width: 60%;
    right: 0;
    height: 48px;
    background: #0a1f3d;
    color: white;
    font-size: 16px;
    display: flex;
    align-items: center;
    position: absolute;
    .back{
      position: absolute;
      left: 15px;
      font-size: 14px;
      color: #fff;
      cursor: pointer;
    }
    .option{
      position: absolute;
      right: 15px;
      font-size: 14px;
      color: #fff;
      display: flex;
      .option_btn{
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 12px;
        margin-left: 15px;
        cursor: pointer;
      }
      .svgIcon{
        font-size: 18px;
      }
    }
  }
  .content_view{
    height: 100%;
    padding: 0 15px 15px 15px;
    background: #0a1f3d;
  }
}
.explanation_main{
  width: 100%;
  height: 50vh;
  display: flex;
  .explanation_left{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .know_btn{
      height: 40px;
      width: 50%;
      color: white;
      background: linear-gradient(90deg, #36D1DC 0%, #5B86E5 100%);
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 5px;
      margin-top: 10vh;
    }
  }
  .explanation_right{
    width: 100%;
    height: 100%;
    overflow-y: auto;
    font-size: 16px;
    padding-left: 20px;
    .title{
      font-weight: 600;
      margin-bottom: 5px;
      height: 30px;
    }
    .explanation_des{
      padding-left: 5px;
      overflow-y: auto;
      height: calc(100% - 35px);
    }
  }
}
.displayNone {
  display: none !important;
}

.displayFlex {
  display: flex !important;
}
</style>
