<template>
  <div class="common-main" v-if='show' v-loading="loading">
    <iframe
      id="scratch-iframe"
      ref="scratchFrame"
      :src="url"
      style="border: none"
      width="100%"
      height="100%"
      allowfullscreen
      allow="microphone *; camera *"
      sandbox="allow-same-origin allow-scripts allow-popups allow-modals allow-downloads"
    ></iframe>
  </div>
</template>

<script>
export default {
  props: {
    studentCourseId: String
  },
  data () {
    return {
      show: false,
      loading: false,
      trainingData: null
    }
  },
  mounted () {
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy () {
    window.removeEventListener('message', this.handleMessage)
  },
  methods: {
    handleMessage (e) {
      if (e.data.type === 'iframeOpen') {
        window.open(e.data.url, '_blank', 'noopener,noreferrer')
      }
    },
    async open (data) {
      this.show = true
      this.trainingData = data
    },
    back () {
      this.show = false
      this.trainingData = null
    }
  },
  computed: {
    url () {
      // return `http://localhost:8889/#/doTrainingDetail/${this.trainingData.trainingId}/${this.studentCourseId}?type=iframe`
      // return `https://qa.binguoketang.com/#/doTrainingDetail/${this.trainingData.trainingId}/${this.studentCourseId}?type=iframe`
      // return `${process.env.VUE_APP_WEB_URL}#/doTrainingDetail/${this.trainingData.trainingId}/${this.studentCourseId}?type=iframe${this.$route.query && this.$route.query.token ? `&token=${this.$route.query.token}` : ''}`

      if (this.trainingData && this.trainingData.trainingType === 'FINACE_PRACTICE') {
        return `${process.env.VUE_APP_WEB_URL}#/doExcelTrainingDetail/finacePractice?trainingId=${this.trainingData.trainingId}&studentCourseId=${this.studentCourseId}&type=iframe${this.$route.query && this.$route.query.token ? `&token=${this.$route.query.token}` : ''}`
        // return `${process.env.VUE_APP_WEB_URL}#/aitraining/finacePractice?trainingId=${this.trainingData.trainingId}&studentCourseId=${this.studentCourseId}&type=iframe${this.$route.query && this.$route.query.token ? `&token=${this.$route.query.token}` : ''}`
      } else {
        // 原有的URL
        return `${process.env.VUE_APP_WEB_URL}#/doTrainingDetail/${this.trainingData.trainingId}/${this.studentCourseId}?type=iframe${this.$route.query && this.$route.query.token ? `&token=${this.$route.query.token}` : ''}`
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.common-main{
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: white;
  border-radius: 10px;
  overflow: auto;
  box-sizing: border-box;
  overflow: hidden;
}
</style>
