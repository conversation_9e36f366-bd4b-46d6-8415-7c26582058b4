<template>
  <div class="main_view" v-loading='loading'>
    <audio ref="countAudio" controls="controls" hidden :src="countAu"></audio>
    <div class='content'>
      <div class='img_content'>
        <el-image v-if="sectionData" :src='sectionData.contentImg' fit='contain' style='width: 100%;height: 100%' />
      </div>
      <div class='btn_view' @click="toTraining">去做实验</div>
      <Scratch ref="scratchRef" :section-id="context.currentSection.id"/>
      <Common ref="commonRef" :student-course-id="studentCourseId"/>
      <GameTraining ref='gameRef' :student-course-id="studentCourseId" :is-mobile="isMobile"/>
      <PythonView ref='pythonRef'/>
    </div>
    <div class="right-group">
      <div class="count-down" v-if='second > -1'>
        {{ second | timeFormat }}
      </div>
      <div class="btn-group">
        <div class="pre" @click="handleBack">上一步</div>
        <div class="next" @click="handleNext">下一步</div>
      </div>
    </div>
  </div>
</template>

<script>
import countAu from '@/assets/audio/count-down.mp3'
import GameTraining from '@/views/ai/components/scratch/components/gameTraining'
import Scratch from '@/views/ai/components/scratch/components/scratch'
import Common from '@/views/ai/components/scratch/components/common'
import PythonView from '@/views/ai/components/scratch/components/pythonView'
import { getAiCourseUnitSection, getTrainingPresetFile, updateAicourseUnitUserProgress } from '@/api/aicourse'
import moment from 'moment/moment'
import { mapGetters } from 'vuex'
import Cookies from 'js-cookie'
export default {
  components: {
    Scratch,
    Common,
    GameTraining,
    PythonView
  },
  props: {
    context: Object,
    isMobile: Boolean,
    studentCourseId: String
  },
  data () {
    return {
      countAu,
      loading: false,
      sectionData: null,
      second: -1,
      timer: null
    }
  },
  mounted () {
    this.getTrainingInfo()
  },
  filters: {
    timeFormat (val) {
      if (!val) {
        return 0
      }
      const time = moment.duration(val, 'seconds') // 得到一个对象，里面有对应的时分秒等时间对象值
      const hours = time.hours()
      const minutes = time.minutes()
      const seconds = time.seconds()
      if (hours) {
        return moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss')
      } else if (minutes) {
        return moment({ m: minutes, s: seconds }).format('mm:ss')
      } else {
        return moment({ s: seconds }).format('ss')
      }
    }
  },
  methods: {
    handleBack () {
      this.$emit('playback', 'prev')
    },
    handleNext () {
      this.$emit('playback', 'next')
    },
    toTraining () {
      if (this.sectionData.training.trainingType === 'PYTHON_PRACTICE') {
        this.getTrainingPresetFile()
      } else {
        this.updateProgress()
      }
    },
    async getTrainingPresetFile () {
      const params = {
        trainingId: this.sectionData.training.trainingId,
        userId: Cookies.get('assistant-id')
      }
      await getTrainingPresetFile(params)
        .then(response => {
          if (response.data.code === 200) {
            this.updateProgress()
          } else {
            this.$message.error(response.data.message || '获取文件失败')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('获取文件失败')
        })
    },
    async updateProgress () {
      const params = {
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.sectionData.aiCourseUnitId,
        aicourseUnitSectionId: this.sectionData.id
      }
      await updateAicourseUnitUserProgress(params)
        .then(response => {
          console.log(response)
          this.$emit('toTraining')
          if (this.sectionData.training.trainingType === 'COMM_PRACTICE') {
            this.$refs.commonRef.open(this.sectionData.training)
          } else if (this.sectionData.training.trainingType === 'SCRATCH_PRACTICE') {
            this.$refs.scratchRef.open(this.sectionData.training)
          } else if (this.sectionData.training.trainingType === 'PYTHON_PRACTICE') {
            window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
            // this.$refs.pythonRef.open(this.sectionData.training)
          } else if (this.sectionData.training.trainingType === 'FINACE_PRACTICE') {
            debugger
            this.$refs.commonRef.open(this.sectionData.training)
          } else {
            this.$refs.gameRef.open(this.sectionData)
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    async getTrainingInfo () {
      this.loading = true
      try {
        const { data } = await getAiCourseUnitSection({
          aicourseUnitSectionId: this.context.currentSection.id
        })
        this.sectionData = data.data
        if (data.data.time && data.data.time > 0) {
          this.second = this.sectionData.time
          this.timer = setInterval(() => this.cuntDown(), 1000)
        } else {
          this.second = -1
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    cuntDown () {
      this.second -= 1
      if (this.second === 3) {
        this.$refs.countAudio.volume = 0.1
        this.$refs.countAudio.play()
      }
      if (this.second <= 0) {
        this.stopCountAudio()
      }
    },
    stopCountAudio () {
      this.$nextTick(() => {
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = undefined
        }
        if (!this.$refs.countAudio.paused) {
          this.$refs.countAudio.pause()
          this.$refs.countAudio.load()
        }
      })
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  }
}
</script>

<style scoped lang='scss'>
.main_view{
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  background: linear-gradient(107.15deg, #E9EFFF -0.58%, #ACF6FF 100%);
  display: flex;
  gap: 20px;
  padding: vh2(50) vh2(20) vh2(20);
  .content{
    width: 83%;
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid #FFFFFF;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 60px;
    position: relative;
    .img_content{
      width: 100%;
      height: 70%;
      //background: linear-gradient(90deg, #A8EDEA 0%, #FED6E3 100%);
      margin-bottom: 40px;
    }
    .btn_view{
      width: 200px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      background: linear-gradient(90deg, #36D1DC 0%, #5B86E5 100%);
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
    }
  }
  .right-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    .btn-group {
      flex: 1;
      gap: vh2(5);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;

      .pre,
      .next {
        font-size: vh2(20);
        font-family: 'PingFang SC';
        font-weight: 500;
        color: #000000;
        line-height: vh2(50);
        text-align: center;
        cursor: pointer;
        height: vh2(50);
        width: vh2(120);
      }

      .pre {
        background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
        background-size: contain;
      }

      .next {
        background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
        background-size: contain;
      }
    }
    .count-down {
      min-width: vh2(50);
      padding: 0 vh2(15);
      height: vh2(30);
      background: linear-gradient(90deg, #FBED96 0%, #ABECD6 100%);
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      border-radius: 17px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: vh2(20);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #000000;
    }
  }
}
</style>
