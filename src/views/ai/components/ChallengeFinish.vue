<template>
  <div class="challenge-finish">
    <div class="challenge-box">
      <lottie v-if="clgSuceess" :width="1000" :height="700" :options="trueChallengeAniOptions" class="animation" />
      <lottie v-else :width="1000" :height="700" :options="falseChallengeAniOptions" class="animation" />
      <audio ref="taudio" controls="controls" hidden :src="trueClg"></audio>
      <audio ref="faudio" controls="controls" hidden :src="falseClg"></audio>
      <div class="ani-score">+ {{ challengeScore }}</div>
    </div>
    <!-- <div class="footer flex">
            <div class="left-btn" @click="$emit('checkResult')">查看答题结果</div>
            <div class="right-btn" @click="$emit('checkReport')">查看报告</div>
        </div> -->
    <img v-if="showGif && !clgSuceess" src="@/assets/images/ai/vid_0.gif" alt="挑战失败" />
    <img v-if="showGif && clgSuceess" src="@/assets/images/ai/vid_1.gif" alt="挑战成功" />
    <div class="challenge-overlay"></div>
  </div>
</template>

<script>
import falseAni from '@/assets/animate/challenge-false.json'
import trueAni from '@/assets/animate/challenge-true.json'
import falseClg from '@/assets/audio/false-clg.mp3'
import trueClg from '@/assets/audio/true-clg.mp3'
import lottie from 'vue-lottie'

export default {
  components: { lottie },
  props: {
    clgSuceess: Boolean,
    challengeScore: Number
  },
  data () {
    return {
      falseChallengeAniOptions: { animationData: falseAni, loop: false, autoplay: true },
      trueChallengeAniOptions: { animationData: trueAni, loop: false, autoplay: true },
      showGif: true,
      falseClg,
      trueClg
    }
  },
  mounted () {
    setTimeout(() => {
      this.showGif = false
      this.$emit('animateEnd')
    }, 1150)
    if (this.clgSuceess) {
      this.$refs.taudio.volume = 0.1
      this.$refs.taudio.play()
    } else {
      this.$refs.faudio.volume = 0.1
      this.$refs.faudio.play()
    }
  }
}
</script>

<style lang="scss" scoped>
.challenge-finish {
    position: absolute;
    z-index: 3000;
    width: 100%;
    height: 100%;

    .challenge-box {
        width: 1000px;
        height: 700px;
        position: absolute;
        transform: translate(-50%, -50%);
        left: 50%;
        top: 48%;
        z-index: 10;
    }

    .animation {
        position: absolute;
    }

    .ani-score {
        position: absolute;
        font-size: 24px;
        font-weight: bolder;
        font-family: DOUYUFONT;
        animation-name: scoreAni;
        animation-duration: 0.1s;
        color: #FFFFFF;
        background: linear-gradient(180deg, #FBFDFF 0%, #C2FDFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        left: 600px;
        top: 467px;
    }

    /* 文字动画代码 */
    @keyframes scoreAni {
        0%    { left:1000px;}
        100%  { left:600px;}
    }

    .footer {
        position: absolute;
        transform: translate(-50%,-50%);
        left: 50%;
        top: 85%;
        z-index: 10;

        .left-btn,
        .right-btn {
            width: 200px;
            height: 54px;
            cursor: pointer;
            background: url('~assets/images/ai/bg-btn2.png') center center no-repeat;
            background-size: contain;
            font-size: 18px;
            font-family: DOUYUFont;
            color: #FFFEF5;
            line-height: 54px;
            text-shadow: 0px 1px 0px #5F4E07;
            text-align: center;

            &:hover {
                background: url('~assets/images/ai/bg-btn-yellow2.png') center center no-repeat;
                background-size: contain;
            }
        }

        .left-btn {
            margin-right: 36px;
        }
    }

    img {
        width: 100%;
        height: 100%;
    }

    .challenge-overlay {
        width: 100%;
        height: 100%;
        position: absolute;
        background: rgba(9, 8, 8, 0.77);
        z-index: 9;
    }
}
</style>
