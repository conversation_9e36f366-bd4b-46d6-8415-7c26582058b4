<template>
  <div class="quiz-container flex-col">
    <audio ref="countAudio" controls="controls" hidden :src="countAu" volume="0"></audio>
    <div class="flex h w" style="gap: 23px">
      <div class="content flex justify-center">
        <div v-if="!showChallengeGuide" class="quiz-area">
          <div class="sheet">
            <div class="right-part">
              <div v-if="!isFinished && !isTimeout" class="count-down">
                <div class="second">{{ second | timeFormate }}</div>
              </div>
              <div v-else-if="isTimeout" class="count-down">
                <div class="second">{{ second | timeFormate }}</div>
              </div>
              <div v-else style="height:88px;"></div>
            </div>
            <single-choice
              v-if="question.questionType==='CHOICE' || question.questionType === 'SIMPLE_CHOOSE'"
              :context="context"
              :question="question"
              :aicourse-unit-id="aicourseUnitId"
              :playback="context.playback"
              :unit-user-id="unitUserId"
              :current-segment-index="context.currentSegmentIndex"
              :current-answered-index="context.currentAnsweredIndex"
              :is-timeout="isTimeout"
              :challenge-status="challengeStatus"
              :step-type="context.currentSection.stepType"
              :aicourse-unit-section-id="context.currentSection.id"
              :is-portrait="isPortrait"
              :is-mobile="isMobile"
              @answerWrong="answerWrong"
              @answerRight="answerRight"
              @stopSubCount="stopSubCount"
            />
            <picture-question
              v-else-if="question.questionType==='TEXT_IMAGE_CHOICE'"
              :question="question"
              :aicourse-unit-id="aicourseUnitId"
              :playback="context.playback"
              :unit-user-id="unitUserId"
              :current-segment-index="context.currentSegmentIndex"
              :current-answered-index="context.currentAnsweredIndex"
              @updateUnitUserId="handleUpdateUnitUserId"
            />
            <fill-blank
              v-else-if="question.questionType==='FILL_IN_THE_BLANK'"
              :question="question"
              :aicourse-unit-id="aicourseUnitId"
              :playback="context.playback"
              :unit-user-id="unitUserId"
              :current-segment-index="context.currentSegmentIndex"
              :current-answered-index="context.currentAnsweredIndex"
              @updateUnitUserId="handleUpdateUnitUserId"
            />
            <match-question
              v-else-if="question.questionType==='MATCH'"
              :context="context"
              :question="question"
              :aicourse-unit-id="aicourseUnitId"
              :unit-user-id="unitUserId"
              :current-segment-index="context.currentSegmentIndex"
              :current-answered-index="context.currentAnsweredIndex"
              :is-timeout="isTimeout"
              :challenge-status="challengeStatus"
              :step-type="context.currentSection.stepType"
              :aicourse-unit-section-id="context.currentSection.id"
              :is-portrait="isPortrait"
              :is-mobile="isMobile"
              @answerWrong="answerWrong"
              @answerRight="answerRight"
              @stopSubCount="stopSubCount"
            />
            <div class="expand-icon flex align-center justify-center" @click="$emit('fullScreen')">
              <img :src="isFullScreenMode ? unfullscreen : fullscreen" alt="" />
            </div>
          </div>
        </div>
      </div>
      <div class="footer">
        <div class="flex align-center justify-center" style="margin-bottom: auto">
          <img class="cross-star" :src="crossStar" alt="" />
          <div class="text-count">第{{ currentQuizIndex }}/{{ totalCount }}题<br />{{ getQuestionType }}</div>
          <img class="cross-star" :src="crossStar" alt="" />
        </div>
        <div v-if="showPrevQuestion && preText" class="pre" @click="$emit('playback', 'prev')">{{ preText }}</div>
        <div v-if="showNextQuestion && nextText" class="next" @click="$emit('playback', 'next')">{{ nextText }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import crossStar from '@/assets/ai-image/ai/cross-star.svg'
import SingleChoice from './SingleChoice'
import PictureQuestion from './PictureQuestion'
import FillBlank from './FillBlank'
import MatchQuestion from './MatchQuestion'
import countAu from '@/assets/audio/count-down.mp3'
import { getAiCourseUnitSection } from '@/api/aicourse'
import moment from 'moment'
import fullscreen from '@/assets/ai-image/ai/fullscreen.svg'
import unfullscreen from '@/assets/ai-image/ai/unfullscreen.svg'

export default {
  filters: {
    timeFormate (val) {
      if (!val) {
        return 0
      }
      const time = moment.duration(val, 'seconds') // 得到一个对象，里面有对应的时分秒等时间对象值
      const hours = time.hours()
      const minutes = time.minutes()
      const seconds = time.seconds()
      if (hours) {
        return moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss')
      } else if (minutes) {
        return moment({ m: minutes, s: seconds }).format('mm:ss')
      } else {
        return moment({ s: seconds }).format('ss')
      }
    }
  },
  components: { SingleChoice, PictureQuestion, FillBlank, MatchQuestion },
  props: {
    context: {
      type: Object
    },
    playback: {
      type: Boolean,
      require: true,
      default: () => false
    },
    secound: Number,
    caption: String,
    challengeStatus: String,
    showChallengeGuide: Boolean,
    currentSectionIndex: Number,
    isPortrait: Boolean,
    isMobile: Boolean,
    isFullScreenMode: Boolean
  },
  data () {
    return {
      crossStar,
      countAu,
      fullscreen,
      unfullscreen,
      questionTypeDic: {
        CHOICE: '选择题',
        SIMPLE_CHOOSE: '选择题',
        TEXT_IMAGE_CHOICE: '图文题',
        FILL_IN_THE_BLANK: '填空题',
        MATCH: '连线题'
      },
      questionTipDic: {
        CHOICE: '点击选项自动提交答案',
        SIMPLE_CHOOSE: '点击选项自动提交答案',
        TEXT_IMAGE_CHOICE: '点击选项自动提交答案',
        FILL_IN_THE_BLANK: '按顺序点击选项自动填空，点击已填内容恢复空缺状态',
        MATCH: '点击选项后，按住不放移动至对应答案'
      },
      section: undefined,
      question: {},
      second: 0,
      timer: null,
      timerOut: null,
      aicourseUnitId: null,
      showCountTip: false,
      alwaysShowTip: true,
      isTimeout: false,
      preview: this.$route.query.preview,
      previewIndex: this.$route.query.previewIndex
    }
  },
  computed: {
    unitUserId () {
      if (this.context.unitUserId != null) return this.context.unitUserId
      if (this.context.currentSection &&
      this.context.currentSection.aicourseUnitSectionUser) { return this.context.currentSection.aicourseUnitSectionUser.aicourseUnitUserId }
      return null
    },
    showPrevQuestion () {
      // if (!this.context.currentSection || !this.context.currentSection.aicourseUnitSectionUser) return false
      if (this.context.currentSection.step === 1 && this.context.currentSection.indexNo === 1) return false
      // if (this.context.currentSection.stepType === 'CHALLENGE') {
      //   var arrIndex = this.context.currentPartion.data.findIndex(
      //     item => {
      //       return item.aicourseUnitSectionUser.result && item.aicourseUnitSectionUser.result === 'WRONG'
      //     }
      //   )
      //   if (arrIndex === -1 && !this.context.currentSection.aicourseUnitSectionUser.completed) return false
      // }
      // if (this.context.currentSection.stepType !== 'CHALLENGE' &&
      // !this.context.currentSection.aicourseUnitSectionUser.completed) return false
      return true
    },
    showNextQuestion () {
      // if (!this.context.currentSection.aicourseUnitSectionUser) {
      //   return false
      // }
      // if (this.context.currentSection.stepType === 'CHALLENGE') {
      //   var arrIndex = this.context.currentPartion.data.findIndex(
      //     item => {
      //       return item.aicourseUnitSectionUser.result && item.aicourseUnitSectionUser.result === 'WRONG'
      //     }
      //   )
      //   if (arrIndex === -1 && !this.context.currentSection.aicourseUnitSectionUser.completed) return false
      // }
      // if (this.context.currentSection.stepType !== 'CHALLENGE' &&
      // !this.context.currentSection.aicourseUnitSectionUser.completed) return false
      if (+this.preview === 1 && this.context.currentSectionIndex === this.context.sectionList.length - 1) return false
      return true
    },
    // 获取题型
    getQuestionType () {
      switch (this.question.questionType) {
        case 'CHOICE':
        case 'SIMPLE_CHOOSE':
          return '选择题'
        case 'MATCH':
          return '连线题'
      }
      return ''
    },
    // 向前按钮文字
    preText () {
      var currentSectionIndex = this.context.currentSectionIndex
      if (currentSectionIndex > 0) {
        // var preSectionIndex = currentSectionIndex - 1
        // if (this.context.sectionList[preSectionIndex].step < this.context.sectionList[currentSectionIndex].step) { return '上一环节' }
        // if (this.context.sectionList[preSectionIndex].aiSectionType === 'VIDEO') { return '回看视频' }
        return '上一步'
      }
      return ''
    },
    // 向后按钮文字
    nextText () {
      // var currentSectionIndex = this.context.currentSectionIndex
      // var nextSegmentIndex = currentSectionIndex + 1
      // if (nextSegmentIndex >= this.context.sectionList.length) return ''
      // if (this.context.sectionList[nextSegmentIndex].step > this.context.sectionList[currentSectionIndex].step) { return '下一环节' }
      // if (this.context.sectionList[nextSegmentIndex].aiSectionType === 'VIDEO') { return '下一步' }
      return '下一步'
    },
    //  总共有多少题
    totalCount () {
      return this.context.currentPartion.data.length || 0
    },
    // 这是当前环节的第几题
    currentQuizIndex () {
      const currentPartion = this.context.currentPartion
      const currentSection = this.context.currentSection
      var arrIndex = currentPartion.data.findIndex(
        item => {
          return item.indexNo === currentSection.indexNo
        }
      )
      return arrIndex + 1 || 0
    },
    //  是否完成
    isFinished () {
      const currentPartion = this.context.currentPartion
      const currentSection = this.context.currentSection
      if (currentSection.aicourseUnitSectionUser && currentSection.aicourseUnitSectionUser.completed) {
        return true
      }
      if (currentSection.stepType === 'CHALLENGE') {
        var arrIndex = currentPartion.data.findIndex(
          item => {
            return item.aicourseUnitSectionUser.result && item.aicourseUnitSectionUser.result === 'WRONG'
          }
        )
        if (arrIndex !== -1) return true
      }
      return false
    }
  },
  watch: {
    currentSectionIndex: {
      deep: true,
      immediate: true,
      handler () {
        console.log('watch')
        this._getAiCourseUnitSection()
        this.aicourseUnitId = this.context.currentSection.aiCourseUnitId
      }
    }
  },
  created () {
    if (localStorage.getItem('stopAutoShowTip')) {
      this.alwaysShowTip = false
    }
    this.$nextTick(() => {
      this.$refs.countAudio.volume = 0.1
    })
  },
  beforeDestroy () {
    this.stopSubCount()
  },
  methods: {
    async _getAiCourseUnitSection () {
      const sectionId = this.context.currentSection.id
      var response = await getAiCourseUnitSection({
        aicourseUnitSectionId: sectionId
      })
      if (+response.data.code === 200) {
        this.question = response.data.data.question || {}
        this.second = this.question.time
        if (!this.isFinished && this.second > 0) {
          this.timer = setInterval(() => this.subCount(), 1000)
        }
      }
    },
    subCount () {
      if (this.showChallengeGuide) return
      this.second -= 1
      if (this.second === 3) {
        this.$refs.countAudio.volume = 0.1
        this.$refs.countAudio.play()
      }
      if (this.second <= 0) {
        this.showCountTip = true
        this.stopSubCount()
        return
      }
    },
    //  回答正确
    answerRight () {
      this.$emit('answerRight')
    },
    // 回答错误
    answerWrong () {
      this.$emit('answerWrong')
    },
    // 暂停倒计时
    stopSubCount () {
      if (this.timer !== null) {
        if (!this.$refs.countAudio.paused) this.$refs.countAudio.pause()
        clearInterval(this.timer)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.quiz-container {
  position: relative;
  width: 100%;
  height: 100%;
  user-select: none;
  padding: vh2(20) 0;
}

.hidden-tip {
  position: absolute;
  right: 26px;
  bottom: -26px;
  height: 20px;

  .check-icon {
    height: 14px;
    width: 14px;
    margin-right: 10px;
    cursor: pointer;
  }

  .check-text {
    font-size: 14px;
    color: #979797;
    line-height: 20px;
  }
}

.help-tooltip {
  display: block;
  position: absolute;
  right: 0;
  top: -30px;
  padding: 0 28px;
  height: 68px;
  min-width: 150px;
  border-radius: 36px;
  background: #FFFFFF;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.1));
  font-size: 16px;
  font-weight: 500;
  color: #2C304E;
  line-height: 68px;
  transform: translate(110%, 0);
  text-align: center;
  white-space: nowrap;
  z-index: 10;

  .cor {
    position: absolute;
    left: -16px;
    top: 30px;
    transform: rotate(-116deg);
  }
}

.opTip {
  position: absolute;
  left: 50%;
  top: 90px;
  font-size: 16px;
  font-weight: 500;
  color: #753514;
  line-height: 22px;
  transform: translate(-50%, 0);
}

.count-tip {
  position: absolute;
  top: -16px;
  right: 80px;
  height: 102px;
  width: 444px;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  background: url('../../../assets/images/ai/count-tip.png') center center no-repeat;
  background-size: contain;
  box-sizing: border-box;
  animation: blink 0.5s infinite cubic-bezier(0.075, 0.82, 0.165, 1);
  &:hover {
    animation: blink 0.5s none cubic-bezier(0.075, 0.82, 0.165, 1);
    opacity: 1;
  }
}

.footer {
  flex: 1;
  gap: vh2(15);
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 30px;
    height: 40px;
    object-fit: cover;
    cursor: pointer;
  }

  .pre,
  .next {
    font-size: vh2(18);
    font-family: 'PingFang SC';
    font-weight: 500;
    color: #000000;
    line-height: vh2(50);
    text-align: center;
    cursor: pointer;
  }

  .pre {
    height: vh2(50);
    width: vh2(150);
    background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
    background-size: contain;
  }

  .next {
    height: vh2(50);
    width: vh2(150);
    background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
    background-size: contain;
  }
}

img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.cross-star {
  width: vh2(17) !important;
  height: vh2(17) !important;
  object-fit: contain;
}

.text-count {
  font-size: vh2(20);
  font-weight: 400;
  color: #000000;
  text-align: center;
  margin: 0 vh2(14);
}

.tip-close {
  position: absolute;
  top: 50%;
  left: 34px;
  height: 24px;
  width: 24px;
  margin-right: 16px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  cursor: pointer;
  transform: translate(0, -50%);
}

.score {
  margin-top: 6px;
  padding: 0 10px;
  min-width: 120px;
  height: 48px;
  background: rgba(255, 255, 255, 0.52);
  border-radius: 24px;
  border: 3px solid #FFFFFF;
}

.icon-star {
  margin-right: 14px;
  height: 30px;
  width: 30px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}

.score-value {
  font-size: 30px;
  font-weight: 500;
  color: #FF8914;
}

.content {
  position: relative;
  width: 83%;
  height: 100%;

  .quiz-area {
    position: relative;
    flex-shrink: 0;
    width: 100%;

    .right-part {
      position: absolute;
      right: vh2(10);
      top: vh2(20);
      z-index: 10;
      .count-down {
        min-width: vh2(58);
        padding: 0 vh2(15);
        height: vh2(44);
        background: linear-gradient(90deg, #FBED96 0%, #ABECD6 100%);
        border-radius: 17px;
        text-align: center;

        .second {
          font-size: vh2(24);
          font-family: 'PingFang SC';
          font-weight: 600;
          color: #000000;
          line-height: vh2(44);
        }
      }
    }
  }

  .action {
    padding: 16px 0;
    width: 150px;
    flex-grow: 1;

    .icon-speak {
      height: 48px;
      width: 48px;
    }

    .help-icon {
      position: relative;
      height: 66px;
      width: 66px;
      cursor: pointer;
    }
  }

  .sheet {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid #FFFFFF;
    border-radius: 10px;
  }

  .btn-prev {
    width: 120px;
    height: 48px;
    border-radius: 24px;
    border: 2px solid #C32136;
    background: #FFFFFF;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #C32136;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
    margin-top: 15px;
  }

  .btn-next {
    justify-content: center;
    align-items: center;
    line-height: 1;
    width: 120px;
    height: 48px;
    background: #C32136;
    border-radius: 24px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    margin-top: 15px;
  }

  .expand-icon {
    position: absolute;
    right: vh2(20);
    bottom: vh2(20);
    width: vh2(45);
    height: vh2(45);
    min-width: 45px;
    min-height: 45px;
    background: linear-gradient(0deg, #E65C00 3.33%, #F9D423 100%);
    box-shadow: 0px 4.73684px 4.73684px rgba(0, 0, 0, 0.25);
    border-radius: 50%;
    cursor: pointer;

    img {
      width: vh2(22);
      height: vh2(22);
      min-width: 22px;
      min-height: 22px;
      object-fit: contain;
      margin-bottom: 2px;
    }

  }
}

@keyframes blink {
  0% {
      opacity: 0.9;

  }
  50% {
      opacity: 0.5;
  }
  100% {
      opacity: 0.9;
  }
}
</style>
