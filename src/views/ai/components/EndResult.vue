<template>
<div class="end-result-mask">
    <div class="end-result-container">

        <div class="dialog-body">
          <div class="light-bg">
            <img :src="light" alt="光" />
          </div>
          <div class="bg">
              <img :src="bg" alt="背景" />
          </div>
          <div class="title">
            <img :src="unitResult" alt="本节成绩" />
          </div>
          <div class="avatar-bg">
            <img :src="avatarBg" alt="头像背景" />
          </div>
          <div class="avatar">
            <img :src="userInfo.avatar || userCover" alt="头像" />
          </div>
          <div class="content">
            <div class="user-name">{{userInfo.displayName}}</div>
            <div class="result-score">
              <img :src="iconScore" alt="分数图标" />
              <span>X {{score || 0}}</span>
            </div>
            <div class="tips">
                恭喜你！超越<span>{{rankPercent}}%</span>的同学
            </div>
            <div class="subtips">
                请继续努力哦～
            </div>
            <div class="flex justify-around" style="width: 368px;">
              <div class="btn btn-cancel" @click="$emit('end')">
                关闭
              </div>
              <div class="btn btn-ok" @click="$emit('playback', 'first')">
                回顾
              </div>
            </div>
          </div>
        </div>
    </div>
</div>
</template>
<script>
import iconScore from '../../../assets/images/ai/icon-score.png';
import avatarBg from '../../../assets/images/ai/avatar-bg.png';
import bg from '../../../assets/images/ai/unit-result-bg.png';
import unitResult from '../../../assets/images/ai/unit-result.png';
import light from '../../../assets/images/ai/light.png';
import userCover from "@/assets/images/<EMAIL>";

import { mapGetters } from "vuex";

export default {
  props: {
    score: Number,
    rankPercent: Number
  },
  data() {
    return {
      iconScore,
      avatarBg,
      bg,
      unitResult,
      light,
      userCover
    }
  },
  computed: {
    ...mapGetters(["userInfo"])
  }
}
</script>
<style lang="scss">
.end-result-mask {
    position: absolute;
    z-index: 9999;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0,0,0,0.3);
}
.end-result-container {
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-top: 110px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.dialog-body {
  width: 649px;
  height: 552px;
  position: relative;
  .light-bg {
    position: absolute;
    top: -420px;
    left: -203px;
    width: 1047px;
    height: 1001px;
  }
  .bg {
    line-height: 0;
    position: relative;
  }
  .title {
    position: absolute;
    left: 50%;
    margin-left: -112px;
    top: -29px;
    img {
      width: 224px;
      height: 61px;
    }
  }
  .avatar {
    position: absolute;
    left: 50%;
    margin-left: -56px;
    top: 59px;
    img {
      width: 112px;
      height: 112px;
      border: 3px solid #FFD24C;
      border-radius: 56px;
      box-sizing: border-box;
    }
  }

  .avatar-bg {
    position: absolute;
    left: 50%;
    margin-left: -71px;
    top: 43px;
    img {
      width: 148px;
      height: 128px;
      box-sizing: border-box;
    }
  }

  .content {
    position: absolute;
    top: 183px;
    left: 0;
    right: 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    .result-score {
      height: 31px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 16px;

      span {
        display: inline-block;
        line-height: 42px;
        font-size: 30px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FF8914;
        margin-left: 4px;
      }

      img {
        width: 31px;
        height: 31px;
      }
    }
    .user-name {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #2C304E;
      line-height: 28px;
    }
    .tips {
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #161823;
      line-height: 42px;
      margin-top: 32px;

      span {
        color: #C32136;
        font-size: 40px;
      }
    }
    .subtips {
      margin-top: 27px;
      font-size: 24px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #161823;
      line-height: 33px;
    }
    .btn {
      cursor: pointer;
    }

    .btn-ok {
      width: 169px;
      height: 67px;
      margin-top: 36px;
      background: #C32136;
      border-radius: 15px;
      line-height: 67px;
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
    }

    .btn-cancel {
        width: 169px;
        height: 67px;
        margin-top: 36px;
        border: 1px solid #C32136;
        border-radius: 15px;
        line-height: 67px;
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #C32136;
    }
  }
}
</style>
