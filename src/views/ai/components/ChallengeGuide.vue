<template>
  <div class="challengeGuide">
    <audio ref="saudio" controls="controls" hidden :src="startAu"></audio>
    <div class="content">
      <!-- <div class="text">1. 本次挑战一共有{{ context.currentPartion.length }}道题，全部答对则为闯关成功<br>2. 每道题限时答题，答错或超时未答则闯关失败<br>3. 每答对一道题，将获得相应缤果币<br>4. 闯关成功后缤果币奖励还将翻倍</div> -->
      <div class="text">1. 本次挑战一共有{{ context.currentPartion.data.length }}道题，全部答对则为闯关成功<br />2. 每道题限时答题，答错则闯关失败<br />3. 每答对一道题，将获得相应缤果币<br />4. 闯关成功后缤果币奖励还将翻倍</div>
    </div>
    <div class="btn" @click="start">知道了，开始挑战！</div>
  </div>
</template>

<script>
import startAu from '@/assets/audio/ready-au.mp3'
export default ({
  props: {
    context: {
      type: Object
    }
  },
  data () {
    return {
      startAu
    }
  },
  computed: {
    // 计算有多少挑战题
    totalCount () {
      var total = 0
      if (this.context && this.context.currentSegment.stepType === 'CHALLENGE') {
        for (var item2 of this.context.segments) {
          if (item2.stepType === 'CHALLENGE') total++
        }
        return total
      }
      return 0
    }
  },
  methods: {
    start () {
      const audio = this.$refs.saudio
      audio.volume = 0.1
      audio.play()
      setTimeout(() => {
        audio.pause()
        this.$emit('close')
      }, 2000)
      // this.$emit('close')
    }
  }
})
</script>

<style lang="scss" scoped>
@function UI_h($px) {
  @return $px * 100vh / 650;
}
@function UI_w($px) {
  @return $px * 100vw / 965;
}
.challengeGuide {
    width: 100%;
    height: 100%;
    background: rgba(9, 8, 8, 0.5);
    position: absolute;
    z-index: 1999;

    .content {
        width: 100%;
        height: UI_w(372);
        background: url('~assets/images/ai/bg-challenge.png') center center no-repeat;
        background-size: contain;
        margin-top: 102px;

        .text {
            width: UI_w(500);
            padding-top: UI_w(131);
            font-size: UI_w(22);
            font-weight: 500;
            color: #8DE1FF;
            line-height: UI_w(40);
            margin: 0 auto;
        }
    }

    .btn {
        background: url('~assets/images/ai/bg-btn-long.png') center center no-repeat;
        background-size: contain;
        width: UI_w(266);
        height: UI_w(54);
        margin: 12px auto;
        font-size: UI_w(18);
        font-family: DOUYUFont;
        color: #FFFFFF;
        line-height: UI_w(54);
        text-shadow: 0px 1px 0px #0F3E7A;
        text-align: center;
        cursor: pointer;

        &:hover {
            background: url('~assets/images/ai/bg-btn-yellow-long.png') center center no-repeat;
            background-size: contain;
        }
    }
}
</style>
