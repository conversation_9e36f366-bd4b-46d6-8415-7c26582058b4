<template>
  <div id="svgbox" class="question-container full-h flex justify-center">
    <lottie v-show="showTrueAni" :width="isMobile ? 600 : 965" :height="isMobile ? 350 : 650" :options="trueAniOptions" class="animation" @animCreated="handleTrueAnimation" />
    <lottie v-show="showFalseAni" :width="isMobile ? 600 : 965" :height="isMobile ? 350 : 650" :options="falseAniOptions" class="animation" @animCreated="handleFalseAnimation" />
    <audio ref="taudio" controls="controls" hidden :src="trueAu"></audio>
    <audio ref="faudio" controls="controls" hidden :src="falseAu"></audio>

    <div class="list">
      <div class="left-list flex-col align-center">
        <div
          v-for="(item,index) in leftList"
          :key="item.id"
          ref="leftList"
          :data-id="item.id"
          class="option flex align-center selectable"
          :class="[
            item.answerType==='TEXT'
              ? rightList[index].answerType === 'TEXT'
                ? 'content-text-nobg'
                : 'content-text'
              : 'content-img' ]
          "
        >
          <div
            v-if="item.answerType==='TEXT'"
            class="content flex align-center"
            :class="{
              'click-select': clickDataId === +item.id
            }"
          >
            <span style="padding: 10px" v-html="item.answer"></span>
          </div>
          <div
            v-else
            class="content-left flex justify-center align-center"
            :class="{
              'click-select-img': clickDataId === +item.id
            }"
          >
            <img
              :src="item.answer"
              class="img-fit"
              draggable="false"
            />
          </div>
        </div>
      </div>
      <div class="right-list flex-col">
        <div
          v-for="(item,index) in rightList"
          :key="item.id"
          ref="rightList"
          :data-id="item.id"
          class="option flex align-center selectable"
          :class="[item.answerType==='TEXT'? 'content-text' : 'content-img' ]"
          @click="matchQuiz(index)"
        >
          <div v-if="item.answerType==='TEXT'" class="content flex align-center">
            <div class="content-choice-img">{{ serialDic[index] }}</div>
            <span class="flex align-center">
              <div style="flex: 1" v-html="item.answer"></div>
              <div class="label">
                <img class="icon-true" :src="trueIcon" alt="" />
                <img class="icon-false" :src="falseIcon" alt="" />
              </div>
            </span>
          </div>
          <div v-else class="content-left flex justify-center align-center">
            <img :src="item.answer" class="img-fit" draggable="false" />
          </div>
          <template v-if="showTip && false">
            <div v-if="item.answerType!=='TEXT'" class="answer-tip answer-tip-img"><span>正确答案</span><br /><span>（第{{ leftList.findIndex(el => el.id === findPair(item.id)) + 1 }}个）</span></div>
            <div v-else class="answer-tip answer-tip-text"><span>正确答案</span><span>（第{{ leftList.findIndex(el => el.id === findPair(item.id)) + 1 }}个）</span></div>
          </template>
        </div>
      </div>
    </div>
    <div v-show="showTrueAni || showFalseAni" class="ani-overlay"></div>
    <div v-show="showScore" class="ani-score">+{{ score }}</div>
  </div>
</template>

<script>
import { jsPlumb } from 'jsplumb'
import { updateAicourseUserAnswer, getUserAnswer } from '@/api/aicourse'
import lottie from 'vue-lottie'
import { mapGetters } from 'vuex'
import { removeClass, addClass } from '@/utils/dom'
import trueAni from '@/assets/animate/true.json'
import falseAni from '@/assets/animate/false.json'
import trueAu from '@/assets/audio/true2-au.mp3'
import falseAu from '@/assets/audio/false2-au.mp3'
import trueIcon from '@/assets/ai-image/ai/icon-true.svg'
import falseIcon from '@/assets/ai-image/ai/icon-false.svg'
export default {
  components: { lottie },
  props: {
    context: {
      type: Object,
      require: true,
      default: () => {}
    },
    question: {
      type: Object,
      require: true,
      default: () => {}
    },
    aicourseUnitId: {
      type: Number,
      require: true
    },
    unitUserId: {
      type: Number,
      require: true
    },
    isTimeout: {
      type: Boolean,
      require: false
    },
    stepType: {
      type: String,
      require: true
    },
    challengeStatus: {
      type: String,
      require: true
    },
    aicourseUnitSectionId: Number,
    isPortrait: Boolean,
    isMobile: Boolean
  },
  data () {
    return {
      studentCourseId: this.$route.params.studentCourseId,
      serialDic: ['A', 'B', 'C'],
      leftList: [],
      rightList: [],
      connectionsList: [],
      userAnswerList: [],
      trueAnswerList: [],
      answer: '',
      trueAnswer: '',
      trueAniOptions: { animationData: trueAni, loop: false, autoplay: false },
      trueLottie: '',
      falseAniOptions: { animationData: falseAni, loop: false, autoplay: false },
      falseLottie: '',
      showTrueAni: false,
      showFalseAni: false,
      trueAu,
      falseAu,
      trueIcon,
      falseIcon,
      plumbIns: null,
      count: 0,
      // timer: null,
      score: 0,
      view: false,
      rightAnswerStr: '',
      showTip: false,
      showScore: false,
      token: this.$route.query.token,
      shareUserId: this.$route.query.userId,
      preview: this.$route.query.preview,
      isTextLeft: false,
      clickDataId: undefined
    }
  },
  watch: {
    isPortrait: {
      deep: true,
      immediate: false,
      handler () {
        jsPlumb.repaintEverything()
      }
    }
  },
  created () {
    this.handleOpt()
    this.isTextLeft = this.question.answerOptionList[0] === 'TEXT'
  },
  mounted () {
    this.handleLink()
    if (this.needGetAnswer) this._getUserAnswer()
  },
  computed: {
    ...mapGetters(['userInfo']),
    needGetAnswer () {
      const currentSection = this.context.currentSection
      let needGetAnswer = false
      switch (currentSection.stepType) {
        case 'CHALLENGE':
          var arrIndex = this.context.currentPartion.data.findIndex(
            item => {
              return item.aicourseUnitSectionUser.result && item.aicourseUnitSectionUser.result === 'WRONG'
            }
          )
          needGetAnswer = arrIndex !== -1 ||
                          (arrIndex === -1 &&
                          currentSection.aicourseUnitSectionUser &&
                          currentSection.aicourseUnitSectionUser.completed) &&
                          +this.preview !== 1
          return needGetAnswer
        case 'COMMON':
          needGetAnswer = currentSection.aicourseUnitSectionUser &&
                          currentSection.aicourseUnitSectionUser.completed &&
                          +this.preview !== 1
          return needGetAnswer
        default:
          needGetAnswer = false
          return needGetAnswer
      }
    }
  },
  destroyed () {
    this.deleteEveryConnection()
  },
  methods: {
    deleteEveryConnection () {
      jsPlumb.deleteEveryConnection()
      this.connectionsList = []
      var doms = Array.from(document.getElementsByClassName('jtk-connector'))
      for (var dom of doms) {
        dom.remove()
      }
      jsPlumb.reset()
    },
    connectionsListChanged (newVal) {
      if (this.needGetAnswer || this.showTip) return
      console.log('连接题目:', newVal)
      if (newVal.length === this.leftList.length) {
        const pairList = []
        newVal.forEach(item => {
          const pair = item.join('-')
          pairList.push(pair)
        })
        this.answer = pairList.join(',')
        console.log(this.answer)
        this._updateAicourseUserAnswer()
      }
    },
    handleLink () {
      const that = this
      const refListL = this.$refs.leftList
      const refListR = this.$refs.rightList
      this.plumbIns = jsPlumb.getInstance({
        connectorStyle: { strokeWidth: 3, stroke: '#6A90FF' }
      })
      const commom = this.needGetAnswer || this.view
        ? {
          connectionsDetachable: false,
          maxConnections: 2,
          enabled: false
        }
        : {
          maxConnections: 2,
          connectorStyle: { strokeWidth: 3, stroke: '#6A90FF' }
        }

      this.plumbIns.ready(function () {
        jsPlumb.setContainer('svgbox')

        jsPlumb.bind('beforeDrag', function (params) {
          if (params.source) {
            return that.isPortrait
          }
        })

        refListL.forEach(item => {
          jsPlumb.on(item, 'mousedown', function (event) {
            that.chooseDataId(item.dataset.id)
          })

          jsPlumb.makeSource(item, {
            anchor: 'Right',
            connector: ['Straight'],
            endpoint: 'Blank'
          }, commom)
        })

        refListR.forEach(item => {
          jsPlumb.makeTarget(item, {
            anchor: 'Left',
            connector: ['Straight'],
            endpoint: 'Blank'
          }, commom)
        })

        jsPlumb.bind('connection', connInfo => {
          const connections = jsPlumb.getAllConnections()
          const curConnection = connInfo.connection
          if (!that.needGetAnswer && !that.showTip) {
            // 如果target线已经连过一次，就删除当前连线
            that.connectionsList.forEach(connect => {
              if (connect.indexOf(+curConnection.target.dataset.id) > -1) {
                jsPlumb.deleteConnection(curConnection)
              }
            })
            // 如果source线已经连过一次，就删除上一次的连线
            for (var i = 0; i < that.connectionsList.length; i++) {
              if (that.connectionsList[i].indexOf(+curConnection.source.dataset.id) > -1) {
                jsPlumb.deleteConnection(connections[i])
                break
              }
            }
          }
          // continue
          const connectionsList = []
          connections.forEach(item => {
            const pair = []
            pair.push(item.source.dataset.id)
            pair.push(item.target.dataset.id)
            connectionsList.push(pair.map(Number))
          })
          that.connectionsList = connectionsList
          that.connectionsListChanged(that.connectionsList)
        })
      })
    },
    handleOpt () {
      const optionList = this.question.answerOptionList || []
      const leftList = optionList.filter(item => item.side === 0)
      const rightList = optionList.filter(item => item.side === 1)
      if (leftList.length <= 3) {
        this.leftList = leftList
      } else {
        this.leftList = leftList.slice(0, 3)
      }
      if (rightList.length <= 3) {
        this.rightList = rightList
      } else {
        this.rightList = rightList.slice(0, 3)
      }
    },
    _updateAicourseUserAnswer () {
      this.$emit('stopSubCount')
      const params = {
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.aicourseUnitId,
        questionId: this.question.id,
        answer: this.answer,
        times: this.count,
        token: this.token || null,
        aicourseUnitSectionId: this.aicourseUnitSectionId
      }
      this.complete = true
      updateAicourseUserAnswer(params).then(response => {
        if (+response.data.code === 200) {
          const data = response.data.data || {}
          console.log(data)
          const answerStr = data.question.answer
          const tempList = answerStr.split(',')
          this.rightAnswerStr = data.question.answer
          this.trueAnswer = tempList.map(item => item.split('-').map(Number))
          const refListL = this.$refs.leftList
          const refListR = this.$refs.rightList
          this.view = true
          this.deleteEveryConnection()
          this.handleLink()
          this.showTip = true
          refListL.forEach(item => {
            removeClass(item, 'selectable')
          })
          refListR.forEach(item => {
            removeClass(item, 'selectable')
          })
          if (this.answer !== '') {
            const userAnswerList = this.answer.split(',')
            userAnswerList.forEach(item => {
              const pair = item.split('-').map(Number)
              const sref = refListL.filter(item => +item.dataset.id === pair[0])[0] || refListL.filter(item => +item.dataset.id === pair[1])[0]
              const tref = refListR.filter(item => +item.dataset.id === pair[1])[0] || refListR.filter(item => +item.dataset.id === pair[0])[0]
              console.log('sref', sref.id)
              if (this.verifyMatchQuestionAnswer(item, answerStr)) {
                // addClass(tref, this.rightList[0].answerType === 'TEXT' ? 'select-true' : 'selected')
                // addClass(sref, this.leftList[0].answerType === 'TEXT' ? this.rightList[0].answerType === 'TEXT' ? 'content-text-nobg' : 'select-true' : 'selected')
                addClass(tref, 'select-true')
                addClass(sref, 'select-true')
                jsPlumb.connect({
                  source: sref.id,
                  target: tref.id,
                  paintStyle: { strokeWidth: 3, stroke: '#27AE60' }
                })
              } else {
                // addClass(tref, this.rightList[0].answerType === 'TEXT' ? 'select-false' : 'selected')
                // addClass(sref, this.leftList[0].answerType === 'TEXT' ? this.rightList[0].answerType === 'TEXT' ? 'content-text-nobg' : 'select-false' : 'selected')
                addClass(tref, 'select-false')
                addClass(sref, 'select-false')
                jsPlumb.connect({
                  source: sref.id,
                  target: tref.id,
                  paintStyle: { strokeWidth: 3, stroke: '#FF5B49' }
                })
                const rightPairID = this.findPair(pair[0])
                const tRref = refListR.filter(item => +item.dataset.id === rightPairID)[0]
                jsPlumb.connect({
                  source: sref.id,
                  target: tRref.id,
                  paintStyle: { strokeWidth: 3, stroke: '#27AE60', dashstyle: '2 4' }
                })
              }
            })
            // 如果是终极挑战的话
            if (this.context.currentSection.stepType === 'CHALLENGE') {
              if (this.isArrEqu(this.connectionsList, this.trueAnswer)) {
                this.$emit('answerRight')
              } else {
                this.$emit('answerWrong')
              }
              return
            }
            if (this.isArrEqu(this.connectionsList, this.trueAnswer)) {
              console.log('answer true')
              this.showTrueAni = true
              this.playMusic(true)
              this.handlePlayTrueAnimation()
            } else {
              console.log('answer false')
              this.showFalseAni = true
              this.playMusic(false)
              this.handlePlayFalseAnimation()
            }
          } else {
            console.log('answer false')
            // 如果是终极挑战的话
            if (this.context.currentSection.stepType === 'CHALLENGE') {
              if (this.isArrEqu(this.connectionsList, this.trueAnswer)) {
                this.$emit('answerRight')
              } else {
                this.$emit('answerWrong')
              }
              return
            }
            this.showFalseAni = true
            this.playMusic(false)
            this.handlePlayFalseAnimation()
            this._getUserAnswer()
          }
        } else {
          this.$toast(response.data.message, {
            position: 'center',
            duration: '2000'
          })
        }
      }).catch(err => {
        console.log(err)
      })
    },
    _getUserAnswer () {
      const params = {
        questionId: this.question.id,
        questionSource: 'AI',
        sourceId: this.unitUserId,
        userId: this.shareUserId || this.userInfo.id,
        aiCourseUnitSectionId: this.aicourseUnitSectionId
      }
      getUserAnswer(params).then(response => {
        if (+response.data.code === 200) {
          if (!response.data.data) return
          const data = response.data.data || {}
          console.log(data)
          const userAnswerList = data.answerIds === null ? [] : data.answerIds.split(',')
          this.userAnswerList = userAnswerList
          this.rightAnswerStr = data.question.answer
          const trueAnswer = data.question.answer.split(',')
          this.trueAnswerList = trueAnswer
          console.log('userAnswerList', userAnswerList)
          console.log('trueAnswer', trueAnswer)
          const refListL = this.$refs.leftList
          const refListR = this.$refs.rightList
          this.showTip = true
          refListL.forEach(item => {
            removeClass(item, 'selectable')
          })
          refListR.forEach(item => {
            removeClass(item, 'selectable')
          })
          if (data.answerIds === null || data.answerIds === '') {
            this.trueAnswerList.forEach(item => {
              const pair = item.split('-').map(Number)
              const sref = refListL.filter(item => +item.dataset.id === pair[0])[0] || refListL.filter(item => +item.dataset.id === pair[1])[0]
              const tref = refListR.filter(item => +item.dataset.id === pair[1])[0] || refListR.filter(item => +item.dataset.id === pair[0])[0]
              console.log('sref', sref)
              console.log('tref', tref)
              // addClass(tref, this.rightList[0].answerType === 'TEXT' ? 'select-empty' : 'selected')
              // addClass(sref, this.leftList[0].answerType === 'TEXT' ? this.rightList[0].answerType === 'TEXT' ? 'content-text-nobg' : 'select-empty' : 'selected')
              addClass(tref, 'select-empty')
              addClass(sref, 'select-empty')
              jsPlumb.connect({
                source: sref.id,
                target: tref.id,
                paintStyle: { strokeWidth: 3, stroke: '#27AE60' }
              })
            })
          } else {
            this.userAnswerList.forEach(item => {
              const pair = item.split('-').map(Number)
              const sref = refListL.filter(item => +item.dataset.id === pair[0])[0] || refListL.filter(item => +item.dataset.id === pair[1])[0]
              const tref = refListR.filter(item => +item.dataset.id === pair[1])[0] || refListR.filter(item => +item.dataset.id === pair[0])[0]
              console.log('sref', sref)
              console.log('tref', tref)
              if (this.verifyMatchQuestionAnswer(item, data.question.answer)) {
                addClass(tref, 'select-true')
                // addClass(sref, this.leftList[0].answerType === 'TEXT' ? this.rightList[0].answerType === 'TEXT' ? 'content-text-nobg' : 'select-true' : 'selected')
                addClass(sref, 'select-true')
                jsPlumb.connect({
                  source: sref.id,
                  target: tref.id,
                  paintStyle: { strokeWidth: 3, stroke: '#27AE60' }
                })
              } else {
                addClass(tref, 'select-false')
                // addClass(sref, this.leftList[0].answerType === 'TEXT' ? this.rightList[0].answerType === 'TEXT' ? 'content-text-nobg' : 'select-false' : 'selected')
                addClass(sref, 'select-false')
                jsPlumb.connect({
                  source: sref.id,
                  target: tref.id,
                  paintStyle: { strokeWidth: 3, stroke: '#FF5B49' }
                })
                const rightPairID = this.findPair(pair[0])
                const tRref = refListR.filter(item => +item.dataset.id === rightPairID)[0]
                jsPlumb.connect({
                  source: sref.id,
                  target: tRref.id,
                  paintStyle: { strokeWidth: 3, stroke: '#27AE60', dashstyle: '2 4' }
                })
              }
            })
          }
        } else {
          this.$toast(response.data.message, {
            position: 'center',
            duration: '2000'
          })
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handleTrueAnimation (anim) {
      this.trueLottie = anim
      this.trueLottie.addEventListener('complete', () => {
        console.log('complete')
        this.showTrueAni = false
        this.showScore = false
        this.$emit('answerRight')
      })
    },
    handleFalseAnimation (anim) {
      this.falseLottie = anim
      this.falseLottie.addEventListener('complete', () => {
        console.log('complete')
        this.showFalseAni = false
        this.$emit('answerWrong')
      })
    },
    handlePlayTrueAnimation () {
      this.trueLottie.play()
      this.score = this.question.score || 0
      setTimeout(() => {
        this.showScore = true
      }, 100)
    },
    handlePlayFalseAnimation () {
      this.falseLottie.play()
    },
    playMusic (type) {
      if (type) {
        this.$refs.taudio.currentTime = 0
        this.$refs.taudio.volume = 0.1
        this.$refs.taudio.play()
        setTimeout(() => {
          this.$refs.taudio.pause()
        }, 3000)
      } else {
        this.$refs.faudio.currentTime = 0
        this.$refs.faudio.volume = 0.1
        this.$refs.faudio.play()
        setTimeout(() => {
          this.$refs.faudio.pause()
        }, 3000)
      }
    },
    isArrEqu (aArray, bArray) {
      const aArr = [...aArray]
      const bArr = [...bArray]
      console.log('aArr' + aArr.toString())
      console.log('bArr' + bArr.toString())
      const aSet = new Set(aArr.map(item => item.sort((a, b) => a - b).toString()))
      const bSet = new Set(bArr.map(item => item.sort((a, b) => a - b).toString()))
      console.log(aSet.size)
      console.log(bSet.size)
      if (aSet.size !== bSet.size) return false
      for (const item of aSet) {
        if (!bSet.has(item)) {
          return false
        }
      }
      return true
    },
    verifyMatchQuestionAnswer (userAnswerIds, rightAnswer) {
      if (!userAnswerIds || !rightAnswer) {
        return false
      }
      const rightAnswerArr = rightAnswer.split(',')
      const rightAnswerMap = new Map()
      for (const rightAnswerP of rightAnswerArr) {
        const rightAnswerParr = rightAnswerP.split('-')
        rightAnswerMap.set(rightAnswerParr[0], rightAnswerParr[1])
        rightAnswerMap.set(rightAnswerParr[1], rightAnswerParr[0])
      }
      console.log(rightAnswerMap)
      const userAnswerParr = userAnswerIds.split('-')
      const userAnswerKey = userAnswerParr[0]
      const userAnswerValue = userAnswerParr[1]
      const rightAnswerValue = rightAnswerMap.get(userAnswerKey)
      if (userAnswerValue !== rightAnswerValue) {
        console.log(userAnswerValue, rightAnswerValue)
        return false
      }
      return true
    },
    findPair (answerId) {
      const rightAnswer = this.rightAnswerStr
      if (!answerId || !rightAnswer) {
        return false
      }
      const rightAnswerArr = rightAnswer.split(',')
      const rightAnswerMap = new Map()
      for (const rightAnswerP of rightAnswerArr) {
        const rightAnswerParr = rightAnswerP.split('-').map(Number)
        rightAnswerMap.set(rightAnswerParr[0], rightAnswerParr[1])
        rightAnswerMap.set(rightAnswerParr[1], rightAnswerParr[0])
      }
      console.log(rightAnswerMap)
      const rightAnswerValue = rightAnswerMap.get(answerId)
      return rightAnswerValue
    },
    chooseDataId (dataId) {
      if (this.needGetAnswer || this.showTip) {
        return
      }
      const connects = []
      this.connectionsList.forEach(item => {
        item.forEach(item2 => {
          connects.push(item2)
        })
      })
      this.clickDataId = +dataId
    },
    matchQuiz (index) {
      const refListL = this.$refs.leftList
      const refListR = this.$refs.rightList
      const lref = refListL.filter(item => +item.dataset.id === this.clickDataId)[0]
      const tref = refListR[index]
      const connects = []
      this.connectionsList.forEach(item => {
        item.forEach(item2 => {
          connects.push(item2)
        })
      })
      if (!this.clickDataId) return
      if (connects.indexOf(+tref.dataset.id) > -1) return
      jsPlumb.connect({
        source: lref.id,
        target: tref.id,
        paintStyle: { strokeWidth: 3, stroke: '#27AE60' }
      })
      this.clickDataId = undefined
      console.log('this.connectionsList', this.connectionsList)
    }
  }
}
</script>

<style lang="scss" scoped>

.question-container {
  position: relative;
  box-sizing: border-box;
  width: 100%;

  * {
    box-sizing: border-box;
  }
}

.list {
  width: 100%;
  height: 100%;
  // padding: vh2(30) vh2(10);
  gap: vh2(90);
  display: flex;
  justify-content: center;
  padding: vh2(20) vh2(110) vh2(20) vh2(20);
}

.left-list,
.right-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  max-width: 45%;
  margin: 0 auto;
}

.animation {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -58%);
  z-index: 9999;
}

.ani-overlay {
  background: rgba(0, 0, 0, 0.2) !important;
  z-index: 9997;
}

.ani-score {
  position: fixed;
  left: 50%;
  top: vh2(400);
  font-size: 60px;
  font-weight: bolder;
  color: #333333;
  z-index: 9999;
  transform: translate(-50%,0);
  font-family: DOUYUFONT;
  animation-name: scoreAni;
  animation-duration: 1s;
}

/* 文字动画代码 */
@keyframes scoreAni {
  0%    { left:70%;transform: translate(-50%,0);}
  30%   { left:45%;transform: translate(-50%,0);}
  50%   { left:50%;transform: translate(-50%,0);}
  70%   { left:50%; transform: scale(1.15) translate(-50%,0);}
  90%   { left:50%; transform: scale(1.07) translate(-50%,0);}
  100%  { left:50%;transform: translate(-50%,0);}
}

.left-list {
  .content {
    margin-left: auto;
  }
}

.right-list {
  .content {
    span {
      min-height: vh2(60);
      padding: vh2(8) vh2(12);
      border-radius: 9px;
    }
  }

  .option {

    .label {
      width: vh2(40);
      height: vh2(40);
      padding-left: vh2(10);

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .icon-true,
      .icon-false {
        display: none;
      }
    }

    &.select-true,
    &.select-empty {
      position: relative;

      .content-choice-img {
        background: #6FCF97;
        border: 1px solid #219653;
        color: white;
      }

      span {
        background: rgba(33, 150, 83, 0.12);
        border: 1px solid #219653;
      }

      .icon-true {
        display: block;
      }

    }

    &.select-false {
        position: relative;

        .content-choice-img {
          background: #EB5757;
          border: 1px solid #BA4D4D;
          color: white;
        }

        span {
          background: rgba(235, 87, 87, 0.12);
          border: 1px solid #EB5757;
        }

        .icon-false {
          display: block;
        }
    }
  }
}

.option {
  position: relative;
  background-size: contain;
  user-select: none;

  &.content-img {
    padding: 0;

    .content {
      padding: 0;
      min-width: 10px;
      height: 100%;
    }
  }

  .answer-tip-img {
    position: absolute;
    left: -100px;
    top: 50%;
    color: #6A90FF;
    font-size: 18px;
    font-weight: 500;
    transform: translateY(-50%);
    text-align: center;
    z-index: 9998;
  }

  .answer-tip-text {
    position: absolute;
    left: 16px;
    top: -32px;
    color: #6A90FF;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    z-index: 9998;
  }

  .content-left {
    width:  vh2(80);
    height:  vh2(80);
    position: relative;

    .img-fit {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }

  &.content-text {
    min-height: vh2(60);
  }

  &.content-text-nobg {
    min-height: vh2(60);
  }

  .content-choice-img {
    width: vh2(60);
    height: vh2(60);
    font-weight: 500;
    border: 1px solid #000000;
    border-radius: 18px;
    background: white;
    line-height: vh2(60);
    margin-right: vh2(10);
  }

  &:nth-child(1) {
    // margin-top: 0;
  }

  &:nth-last-child(1) {
    margin-bottom: 0;
  }

  .content {
    text-align: center;
    font-size: vh2(26);
    color: #000000;
    line-height: vh2(36);

    span {
      flex: 1;
      text-align: start;
      word-break: break-all;
      white-space: pre-line;
    }
  }

  &.selectable:hover {
    cursor: pointer;
  }

  &.true-answer {
    position: relative;
    background: #27AE60;
    border: 3px solid #6A90FF;
    box-shadow: 0px 2px 5px 0px rgba(0, 52, 205, 0.32);

    .serial {
      background: #FFFFFF;
      color: #6A90FF;
    }

    .content {
      color: #FFFFFF;
    }

    &::after {
      position: absolute;
      left: -50px;
      top: 50%;
      content: '正确\A答案';
      white-space: pre;
      color: #6A90FF;
      font-size: 18px;
      font-weight: 500;
      transform: translateY(-50%);
    }
  }

  .click-select {
    background: rgba(47, 128, 237, 0.12);
    border: 1px solid #6A90FF;
    border-radius: 9px;
  }

  .click-select-img {
    border: 4px solid #6A90FF;
  }
}
</style>
