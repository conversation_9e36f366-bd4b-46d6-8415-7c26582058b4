<template>
  <div class="progress-icon" :class="[{active:active, playback: playback}]" @click="$emit('click')">
    <template v-if="active">
      <img v-if="type === 0" :src="playAc" />
      <img v-else :src="pencilAc" />
    </template>
    <template v-else>
      <img v-if="type === 0" :src="playDf" />
      <img v-else :src="pencilDf" />
    </template>
  </div>
</template>

<script>
import playAc from '@/assets/images/ai/play-ac.png'
import playDf from '@/assets/images/ai/play-df.png'
import pencilAc from '@/assets/images/ai/pencil-ac.png'
import pencilDf from '@/assets/images/ai/pencil-df.png'

export default {
  props: {
    active: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 0
    },
    playback: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      playAc,
      playDf,
      pencilAc,
      pencilDf
    }
  }
}
</script>

<style lang="scss" scoped>
.progress-icon {
  padding: 10px;
  width: 44px;
  height: 44px;
  background: #EEEEEE;
  border-radius: 17px;
  box-sizing: border-box;

  &.active {
    background: linear-gradient(156deg, #E34566 0%, #C32136 100%);
    cursor: pointer;
  }

  &.playback {
    cursor: pointer;
  }
}

img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
