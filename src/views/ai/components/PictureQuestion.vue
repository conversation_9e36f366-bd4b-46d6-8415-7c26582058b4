<template>
  <div class="question-container">
    <lottie v-show="showTrueAni" :width="1000" :height="700" :options="trueAniOptions" class="animation" @animCreated="handleTrueAnimation" />
    <lottie v-show="showFalseAni" :width="1000" :height="700" :options="falseAniOptions" class="animation" @animCreated="handleFalseAnimation" />
    <audio ref="taudio" controls="controls" hidden :src="trueAu"></audio>
    <audio ref="faudio" controls="controls" hidden :src="falseAu"></audio>
    <div class="question flex align-center justify-center">
      {{ question.question }}
    </div>

    <div class="answer flex align-start justify-between">
      <div class="picture full-h">
        <img :src="question.mediaUrl" class="fit-img" />
      </div>
      <div class="full-h flex-col align-start" style="margin-right:64px;">
        <div
          v-for="(item, index) in sliceOptionList"
          :key="item.id"
          class="option flex align-center"
          :class="{
            'select-true': trueAnswer && item.id === +answer && +trueAnswer === item.id,
            'select-false': trueAnswer && item.id === +answer && +trueAnswer !== item.id,
            'true-answer': trueAnswer && item.id !== +answer && +trueAnswer === item.id,
            'selectable': !answer && !playback && currentSegmentIndex >= currentAnsweredIndex
          }"
          @click="getAnswer(item)"
        >
          <div class="serial">{{ serialDic[index] }}</div>
          <div class="content">{{ item.answer }}</div>
        </div>
      </div>
    </div>
    <div v-show="showTrueAni || showFalseAni" class="ani-overlay"></div>
    <div v-show="showScore" class="ani-score">+{{ score }}</div>
  </div>
</template>

<script>
import { updateAicourseUserAnswer, getUserAnswer } from '@/api/aicourse'
import { mapGetters } from 'vuex'
import lottie from 'vue-lottie'
import trueAni from '@/assets/images/ai/true.json'
import falseAni from '@/assets/images/ai/false.json'
import trueAu from '@/assets/audio/true-au.mp3'
import falseAu from '@/assets/audio/false-au.mp3'
export default {
  components: { lottie },
  props: {
    playback: {
      type: Boolean,
      require: true,
      default: () => false
    },
    unitUserId: {
      type: Number,
      require: true
    },
    question: {
      type: Object,
      require: true,
      default: () => {}
    },
    aicourseUnitId: {
      type: Number,
      require: true
    },
    currentSegmentIndex: {
      type: Number,
      require: true
    },
    currentAnsweredIndex: {
      type: Number,
      require: true
    }
  },
  data () {
    return {
      studentCourseId: this.$route.params.studentCourseId,
      serialDic: ['A', 'B', 'C'],
      answer: '',
      trueAnswer: '',
      trueAniOptions: { animationData: trueAni, loop: false, autoplay: false },
      trueLottie: '',
      falseAniOptions: { animationData: falseAni, loop: false, autoplay: false },
      falseLottie: '',
      showTrueAni: false,
      showFalseAni: false,
      count: 0,
      timer: null,
      trueAu,
      falseAu,
      score: 0,
      showScore: false,
      token: this.$route.query.token,
      shareUserId: this.$route.query.userId
    }
  },
  mounted () {
    this.timer = setInterval(() => this.subCount(), 1000)
    if (this.playback || this.currentSegmentIndex < this.currentAnsweredIndex) {
      this._getUserAnswer()
    }
  },
  destroyed () {
    clearInterval(this.timer)
  },
  computed: {
    ...mapGetters(['userInfo']),
    sliceOptionList () {
      const optionList = this.question.answerOptionList || []
      const length = optionList.length
      if (length <= 3) {
        return optionList
      } else {
        return optionList.slice(0, 3)
      }
    }
  },
  methods: {
    getAnswer (option) {
      if (this.playback || this.currentSegmentIndex < this.currentAnsweredIndex) return
      if (this.answer) return
      this.answer = option.id.toString()
      console.log(this.answer)
      this._updateAicourseUserAnswer()
    },
    _updateAicourseUserAnswer () {
      const params = {
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.aicourseUnitId,
        questionId: this.question.id,
        answer: this.answer,
        times: this.count,
        token: this.token || null
      }
      updateAicourseUserAnswer(params).then(response => {
        if (+response.data.code === 200) {
          const data = response.data.data || {}
          this.$emit('updateUnitUserId', data.sourceId)
          console.log(data)
          this.trueAnswer = data.question.answer
          if (this.answer === this.trueAnswer) {
            console.log('answer true')
            this.showTrueAni = true
            this.playMusic(true)
            this.handlePlayTrueAnimation()
          } else {
            console.log('answer false')
            this.showFalseAni = true
            this.playMusic(false)
            this.handlePlayFalseAnimation()
          }
        } else {
          this.$toast(response.data.message, {
            position: 'center',
            duration: '2000'
          })
          setTimeout(() => {
            this.handleAnswered(this.answer)
          }, 2000)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    _getUserAnswer () {
      const params = {
        questionId: this.question.id,
        questionSource: 'AI',
        sourceId: this.unitUserId,
        userId: this.shareUserId || this.userInfo.id
      }
      getUserAnswer(params).then(response => {
        if (+response.data.code === 200) {
          if (!response.data.data) return
          const data = response.data.data || {}
          console.log(data)
          this.answer = data.answerIds
          this.trueAnswer = data.question.answer
        } else {
          this.$toast(response.data.message, {
            position: 'center',
            duration: '2000'
          })
          setTimeout(() => {
            this.handleAnswered(this.answer)
          }, 2000)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handleAnswered (evt) {
      this.$emit('answered', evt)
    },
    handleTrueAnimation (anim) {
      this.trueLottie = anim
      this.trueLottie.addEventListener('complete', () => {
        console.log('complete')
        this.showTrueAni = false
        this.showScore = false
        // / TODO: 进入下一个环节
        this.handleAnswered(this.answer)
        // this.$emit('answered', this.answer);
      })
    },
    handleFalseAnimation (anim) {
      this.falseLottie = anim
      this.falseLottie.addEventListener('complete', () => {
        console.log('complete')
        this.showFalseAni = false
        // / TODO: 进入下一个环节
        this.handleAnswered(this.answer)
        // this.$emit('answered', this.answer);
      })
    },
    handlePlayTrueAnimation () {
      this.trueLottie.play()
      const score = this.question.score || 0
      this.score = this.question.score || 0
      this.$emit('getScore', score)
      setTimeout(() => {
        this.showScore = true
      }, 2000)
    },
    handlePlayFalseAnimation () {
      this.falseLottie.play()
      this.$emit('getScore', 0)
    },
    playMusic (type) {
      if (type) {
        this.$refs.taudio.currentTime = 0
        this.$refs.taudio.volume = 0.1
        this.$refs.taudio.play()
        setTimeout(() => {
          this.$refs.taudio.pause()
        }, 3000)
      } else {
        this.$refs.faudio.currentTime = 0
        this.$refs.faudio.volume = 0.1
        this.$refs.faudio.play()
        setTimeout(() => {
          this.$refs.faudio.pause()
        }, 3000)
      }
    },
    subCount () {
      this.count += 1
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
@import "@/styles/variables";

.question-container {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;

  * {
    box-sizing: border-box;
  }
}

.question {
  padding: 16px;
  width: 100%;
  height: 150px;
  font-size: 35px;
  font-weight: 500;
  color: #2C304E;
  line-height: 42px;
}

.answer {
  padding: 24px 16px 10px;
  width: 100%;
  height: calc(100% - 150px);
}

.animation {
  position: absolute;
  left: 50%;
  top: 50%;
  // width: 600px !important;
  transform: translate(-50%, -58%);
  z-index: 9999;
}

.ani-overlay {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 200vw;
  height: 200vh;
  background: rgba(0, 0, 0, 0.7);
  transform: translate(-50%, -50%);
  z-index: 9998;
}

.ani-score {
  position: absolute;
  left: 480px;
  top: 250px;
  font-size: 80px;
  font-weight: bolder;
  color: #FEDD46;
  z-index: 9999;
}

.option {
  margin-bottom: 16px;
  padding: 0 10px 0 8px;
  height: 78px;
  background: #FFEFDB;
  border-radius: 40px;
  border: 3px solid transparent;

  &:nth-last-child(1) {
    margin-bottom: 0;
  }

  .serial {
    width: 62px;
    height: 62px;
    background: #FA9D1F;
    border-radius: 32px;
    font-size: 30px;
    font-weight: 500;
    text-align: center;
    color: #FFFFFF;
    line-height: 62px;
  }

  .content {
    padding: 0 16px;
    min-width: 250px;
    font-size: 30px;
    font-weight: 500;
    color: #2C304E;
    line-height: 42px;
  }

  &.selectable:hover {
    cursor: pointer;
    border: 3px solid #6A90FF;
  }

  &.select-true {
    position: relative;
    border: 3px solid #6A90FF;

    &::after {
      position: absolute;
      top: -2px;
      right: -2px;
      content: '';
      height: 40px;
      width: 64px;
      background: url('../../../assets/images/ai/select-true.png') center center no-repeat;
      background-size: cover;
    }
  }

  &.select-false {
    position: relative;
    border: 3px solid #FF5B49;

    &::after {
      position: absolute;
      top: -2px;
      right: -2px;
      content: '';
      height: 40px;
      width: 64px;
      background: url('../../../assets/images/ai/select-false.png') center center no-repeat;
      background-size: cover;
    }
  }

  &.true-answer {
    position: relative;
    background: #6A90FF;
    border: 3px solid #6A90FF;
    box-shadow: 0px 2px 5px 0px rgba(0, 52, 205, 0.32);

    .serial {
      background: #FFFFFF;
      color: #6A90FF;
    }

    .content {
      color: #FFFFFF;
    }

    &::after {
      position: absolute;
      right: -50px;
      top: 50%;
      content: '正确\A答案';
      white-space: pre;
      color: #6A90FF;
      font-size: 18px;
      font-weight: 500;
      transform: translateY(-50%);
    }
  }
}

.picture {
  width: 42%;
}
</style>
