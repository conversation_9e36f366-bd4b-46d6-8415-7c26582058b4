<template>
  <div class="work-box">
    <audio ref="countAudio" controls="controls" hidden :src="countAu"></audio>

    <div v-if="workInfo" class="flex">
      <div class="left">
        <div class="title-box">
          <div class="title">{{ titleJson[workInfo.aiSectionType] }}</div>
          <div class="subtitle">{{ subtitleJson[workInfo.aiSectionType] }}</div>
          <div v-if="standradImg" class="standard" @click="showStandard = true">评分标准</div>
          <img v-if="standradImg" class="standard-img" :src="exclamatory" alt="" @click="showStandard = true" />
        </div>
        <div class="content-box">
          <div class="content" v-html="workInfo.advice"></div>
          <div class="flex-col align-center">
            <img
              v-for="img in imgs"
              :key="img.mediaFileId"
              class="content-img"
              :src="img.mediaFile.url"
            />
          </div>
        </div>
      </div>

      <div class="right">
        <div v-if="second > 0" class="cutdown">
          <div class="second">{{ second | timeFormate }}</div>
        </div>
        <div id="qrcode" class="qrcode"></div>
        <div class="work-require">{{ workInfo.aiSectionType === 'NEXT_PREVIEW' && !workInfo.nextPreview ? '暂未配置' : '拍照或截图分享' }}</div>
      </div>
    </div>

    <div v-if="workInfo" class="footer flex justify-center">
      <div v-if="preText" class="pre" @click="handleBack">{{ preText }}</div>
      <div v-if="nextText" class="next" @click="handleNext">{{ nextText }}</div>
    </div>

    <div v-if="workInfo && showStandard" class="dialog-box">
      <div class="dialog">
        <div class="dialog-title">评分标准</div>
        <img class="close" :src="close" alt="" @click="showStandard = false" />
        <img class="dialog-img" :src="standradImg" />
      </div>
    </div>
  </div>
</template>

<script>
import exclamatory from '@/assets/ai-image/icon/exclamatory.svg'
import close from '@/assets/ai-image/icon/close.svg'
import QRCode from 'qrcodejs2'
import { getAiCourseUnitSection } from '@/api/aicourse'
import { mapGetters } from 'vuex'
import countAu from '@/assets/audio/count-down.mp3'
import moment from 'moment'
export default {
  filters: {
    timeFormate (val) {
      if (!val) {
        return 0
      }
      const time = moment.duration(val, 'seconds') // 得到一个对象，里面有对应的时分秒等时间对象值
      const hours = time.hours()
      const minutes = time.minutes()
      const seconds = time.seconds()
      if (hours) {
        return moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss')
      } else if (minutes) {
        return moment({ m: minutes, s: seconds }).format('mm:ss')
      } else {
        return moment({ s: seconds }).format('ss')
      }
    }
  },
  props: {
    context: Object,
    currentSectionIndex: Number
  },
  data () {
    return {
      exclamatory,
      close,
      workInfo: undefined,
      imgs: [],
      standradImg: undefined,
      titleJson: {
        'PREVIEW': '前置课程',
        'LEARNING_ACHIEVED': '课中成果',
        'AFTER_ACHIEVED': '课后成果',
        'NEXT_PREVIEW': '前置课程'
      },
      subtitleJson: {
        'PREVIEW': '（课前准备）',
        'LEARNING_ACHIEVED': '（课中任务）',
        'AFTER_ACHIEVED': '（课后任务）',
        'NEXT_PREVIEW': '（下一节课准备）'
      },
      showStandard: false,
      courseId: this.$route.params.courseId,
      studentCourseId: this.$route.params.studentCourseId,
      unitId: this.$route.params.unitId,
      second: 0,
      timer: null,
      countAu,
      preview: this.$route.query.preview
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    // 向前按钮文字
    preText () {
      var currentSectionIndex = this.context.currentSectionIndex
      if (currentSectionIndex > 0) {
        // var preSectionIndex = currentSectionIndex - 1
        // if (this.context.sectionList[preSectionIndex].step < this.context.sectionList[currentSectionIndex].step) { return '上一环节' }
        // if (this.context.sectionList[preSectionIndex].aiSectionType === 'VIDEO') { return '回看视频' }
        return '上一步'
      }
      return ''
    },
    // 向后按钮文字
    nextText () {
      // var currentSectionIndex = this.context.currentSectionIndex
      // var nextSegmentIndex = currentSectionIndex + 1
      // if (nextSegmentIndex >= this.context.sectionList.length) return ''
      // if (this.context.sectionList[nextSegmentIndex].step > this.context.sectionList[currentSectionIndex].step) { return '下一环节' }
      // if (this.context.sectionList[nextSegmentIndex].aiSectionType === 'VIDEO') { return '下一步' }
      if (+this.preview === 1 && this.context.currentSectionIndex === this.context.sectionList.length - 1) return ''
      return '下一步'
    }
  },
  watch: {
    currentSectionIndex: {
      deep: true,
      immediate: true,
      handler () {
        console.log('进入作业')
        this.$emit('updateProgress')
        this.workInfo = undefined
        this.imgs = []
        this.standradImg = undefined
        this.stopSubCount()
        this._getAiCourseUnitSection()
      }
    }
  },
  created () {
    this.$nextTick(() => {
      this.$refs.countAudio.volume = 0.1
    })
  },
  destroyed () {
    this.stopSubCount()
  },
  methods: {
    creatQrCode () {
      this.$nextTick(() => {
        var codeHtml = document.getElementById('qrcode')
        if (this.workInfo.aiSectionType === 'NEXT_PREVIEW' && !this.workInfo.nextPreview) {
          codeHtml.style.cssText = 'display: none'
          return
        }
        if (codeHtml && codeHtml.innerHTML) codeHtml.innerHTML = ''
        const { VUE_APP_CONFIG } = process.env
        const unitId = this.workInfo.aiSectionType === 'NEXT_PREVIEW' ? this.workInfo.nextPreview.aiCourseUnitId : this.unitId
        const sectionId = this.workInfo.aiSectionType === 'NEXT_PREVIEW' ? this.workInfo.nextPreview.id : this.context.currentSection.id
        const classId = this.userInfo ? this.userInfo.id : 0
        const host = VUE_APP_CONFIG === 'online'
          ? `https://binguoketang.com/#/parent/course/homework/${this.courseId}/${this.studentCourseId}/${unitId}/${sectionId}/${classId}?isScan=1`
          : `https://qa.binguoketang.com/#/parent/course/homework/${this.courseId}/${this.studentCourseId}/${unitId}/${sectionId}/${classId}?isScan=1`
        new QRCode(
          codeHtml,
          {
            text: host,
            width: 90,
            height: 90,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          }
        )
        codeHtml.removeAttribute('title')
      })
    },
    async _getAiCourseUnitSection () {
      const sectionId = this.context.currentSection.id
      var response = await getAiCourseUnitSection({
        aicourseUnitSectionId: sectionId
      })
      if (+response.data.code === 200) {
        this.workInfo = response.data.data
        if (this.workInfo.resourceList && this.workInfo.resourceList.length > 0) {
          this.imgs = this.workInfo.resourceList.filter(item => item.linkType === 'AI_WORK_IMG')
          const standardList = this.workInfo.resourceList.filter(item => item.linkType === 'AI_WORK_SCORE_STANDARD')
          if (standardList.length > 0) this.standradImg = standardList[0].mediaFile.url
        }
        this.creatQrCode()
        this.second = this.workInfo.time
        if (this.second > 0) {
          this.timer = setInterval(() => this.subCount(), 1000)
        }
      }
    },
    subCount () {
      this.second -= 1
      if (this.second < 4) {
        console.log('作业倒计时:', this.second)
      }
      if (this.second === 3) {
        this.$refs.countAudio.play()
      }
      if (this.second <= 0) {
        this.showCountTip = true
        this.stopSubCount()
        return
      }
    },
    // 暂停倒计时
    stopSubCount () {
      if (this.timer !== null) {
        if (this.$refs.countAudio && !this.$refs.countAudio.ended) this.$refs.countAudio.pause()
        clearInterval(this.timer)
        this.timer = null
      }
    },
    handleBack () {
      this.$emit('prev')
    },
    handleNext () {
      this.$emit('next')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin";
.work-box {
    width: 100%;
    height: 100%;
    background: linear-gradient(109.39deg, #FCECFF -23.25%, #ABF6FF 97.11%);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cutdown {
  position: absolute;
  right: vh2(10);
  top: - vh2(35);
  padding: 0 vh2(10);
  min-width: vh2(57);
  height: vh2(30);
  background: linear-gradient(90deg, #FBED96 0%, #ABECD6 100%);
  border-radius: 17px;
  text-align: center;
  .second {
    font-size: vh2(27);
    font-family: 'PingFang SC';
    font-weight: 600;
    color: #000000;
    line-height: vh2(30);
  }
}

.left,
.right {
  margin-top: vh2(80);
  margin-bottom: vh2(20);
  position: relative;
}

.left {
    width: vh2(552);
    height: vh2(455);
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid #FFFFFF;
    border-radius: 10px;
    margin-right: vh2(10);
    display: flex;
    flex-direction: column;

    .title-box {
        padding: vh2(7) vh2(24) vh2(5);
        display: flex;
        align-items: center;
    }

    .title {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 400;
        font-size: vh2(30);
        line-height: vh2(42);
        color: #000000;
    }

    .subtitle {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 400;
        font-size: vh2(14);
        line-height: vh2(20);
        color: #000000;
        margin-right: auto;
    }

    .standard {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 600;
        font-size: vh2(10);
        line-height: vh2(14);
        text-decoration-line: underline;
        color: #F2994A;
        cursor: pointer;
    }

    .standard-img {
      width: vh2(11);
      height: vh2(11);
      object-fit: contain;
      cursor: pointer;
    }

    .content-box {
      flex: 1;
      width: 100%;
      overflow: auto;
      @include scrollBar;
    }

    .content {
        margin: 0 auto;
        width: vh2(500);
        line-height: vh2(28);
        word-break: break-all;
        white-space: pre-line;
        font-size: vh2(20);
    }

    .content-img {
        max-height: vh2(240);
        max-width: 90%;
        object-fit: contain;
        margin-bottom: vh2(10);

        &:first-child {
            margin-top: vh2(10);
        }
    }
}

.right {
    width: vh2(223);
    height: vh2(455);
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid #FFFFFF;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .qrcode {
        width: vh2(166);
        height: vh2(166);
        margin: 0 auto vh2(10);
    }

    .work-require {
        // width: vh2(210);
        font-family: 'PingFang SC';
        font-size: vh2(14);
        line-height: vh2(20);
        color: #000000;
        word-break: break-all;
        white-space: pre-line;
    }
}

.footer {
  width: 100%;
  gap: vh2(20);

  .pre,
  .next {
    font-size: vh2(20);
    font-family: 'PingFang SC';
    font-weight: 500;
    color: #000000;
    line-height: vh2(50);
    text-align: center;
    cursor: pointer;
  }

  .pre {
    height: vh2(50);
    width: vh2(150);
    background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
    background-size: contain;
  }

  .next {
    height: vh2(50);
    width: vh2(150);
    background: url('~assets/ai-image/ai/bg-btn.png') center center no-repeat;
    background-size: contain;
  }

}

.dialog-box {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba($color: #000000, $alpha: 0.7);
}

.dialog {
    width: vh2(502);
    height: vh2(300);
    background: #FFFFFF;
    border-radius: 5px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;

    .dialog-title {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 600;
        font-size: vh2(24);
        line-height: vh2(35);
        color: #000000;
        text-align: center;
        padding: vh2(18) 0 vh2(2);
    }

    .close {
        width: vh2(20);
        height: vh2(20);
        object-fit: contain;
        position: absolute;
        top: vh2(15);
        right: vh2(18);
    }

    img {
        height: vh2(225);
        object-fit: contain;
    }
}
</style>

<style lang="scss">

.qrcode {
  img {
      margin: auto;
      width: 100%;
      height: 100%;
  }
}
</style>
