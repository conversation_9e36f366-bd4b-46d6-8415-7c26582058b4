import {
  dologin,
  doregister,
  doDatalogin,
  doDatalogout
} from '@/api/login'
import {
  fetchUserInfo,
  getUserInfo
} from '@/api/user'
import {
  getToken,
  setToken,
  removeToken,
  setAdminToken,
  removeAdminToken
} from '@/utils/auth'
const user = {
  state: {
    token: '',
    userInfo: {},
    loginStatus: false,
    loginType: 1,
    loginPath: '',
    loginFade: false,
    loadedInfo: false,
    year: {},
    term: {},
    organization: {},
    school: {}
  },

  mutations: {
    SET_USERINFO: (state, userInfo) => {
      state.userInfo = userInfo
    },
    SET_LOGINFADE: (state, loginFade) => {
      state.loginFade = loginFade
    },
    SET_LOGINTYPE: (state, loginType) => {
      state.loginType = loginType
    },
    SET_LOGINPATH: (state, loginPath) => {
      state.loginPath = loginPath
    },
    SET_LOGINSTATUS: (state, loginStatus) => {
      state.loginStatus = loginStatus
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_LOADEDINFO: (state, loadedInfo) => {
      state.loadedInfo = loadedInfo
    },
    SET_YEAR: (state, year) => {
      state.year = year
    },
    SET_TERM: (state, term) => {
      state.term = term
    },
    SET_ORGANIZATION: (state, organization) => {
      state.organization = organization
    },
    SET_SCHOOL: (state, school) => {
      state.school = school
    }
  },

  actions: {
    setLoginFadeAtion ({
      commit
    }, param) {
      commit('SET_LOGINFADE', param.flag)
      commit('SET_LOGINTYPE', param.type || 1)
      commit('SET_LOGINPATH', param.path || '')
    },
    LoginByPass ({
      commit
    }, userInfo) {
      return new Promise((resolve, reject) => {
        dologin(userInfo).then(response => {
          if (+response.data.code === 200) {
            commit('SET_LOGINSTATUS', true)
            commit('SET_TOKEN', 'Bearer ' + response.data.data.access_token)
            setToken('Bearer ' + response.data.data.access_token)
            commit('SET_LOADEDINFO', true)
          }
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    doRegister ({
      commit
    }, userInfo) {
      return new Promise((resolve, reject) => {
        doregister(userInfo).then(response => {
          if (+response.data.code === 200) {
            commit('SET_LOGINSTATUS', true)
            commit('SET_TOKEN', 'Bearer ' + response.data.data.access_token)
            setToken('Bearer ' + response.data.data.access_token)
            commit('SET_LOADEDINFO', true)
          }
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    GetUserInfo ({
      commit
    }) {
      return new Promise((resolve, reject) => {
        fetchUserInfo({ 'userType': 'STUDENT' }).then(response => {
          if (+response.data.code === 200) {
            commit('SET_USERINFO', response.data.data)
            commit('SET_TOKEN', getToken())
            commit('SET_LOGINSTATUS', true)
          }
          commit('SET_LOADEDINFO', true)
          resolve(response)
        }).catch(error => {
          commit('SET_LOADEDINFO', true)
          reject(error)
        })
      })
    },
    saveToken ({ commit }, token) {
      return new Promise((resolve) => {
        commit('SET_LOGINSTATUS', true)
        commit('SET_TOKEN', token)
        setToken(token)
        commit('SET_LOADEDINFO', true)
        resolve()
      })
    },
    LogOut ({
      commit
    }) {
      commit('SET_LOADEDINFO', true)
      commit('SET_LOGINSTATUS', false)
      commit('SET_TOKEN', '')
      removeToken()
    },
    //  数据中心
    LoginByData ({
      commit
    }, userInfo) {
      return new Promise((resolve, reject) => {
        doDatalogin(userInfo).then(response => {
          if (+response.data.code === 200) {
            commit('SET_TOKEN', response.data.data.token)
            setAdminToken(response.data.data.token)
          }
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    LogoutByData ({
      commit
    }) {
      return new Promise((resolve, reject) => {
        doDatalogout().then(response => {
          if (+response.data.code === 200) {
            commit('SET_TOKEN', '')
            removeAdminToken()
          }
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    GetDataUserInfo ({
      commit
    }) {
      return new Promise((resolve, reject) => {
        getUserInfo().then(response => {
          if (+response.data.code === 200) {
            commit('SET_USERINFO', response.data.data)
          }
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    SaveYear ({ commit }, data) {
      commit('SET_YEAR', data)
    },
    SaveTerm ({ commit }, data) {
      commit('SET_TERM', data)
    },
    SaveOrganization ({ commit }, data) {
      commit('SET_ORGANIZATION', data)
    },
    SaveSchool ({ commit }, data) {
      commit('SET_SCHOOL', data)
    }
  }
}

export default user
