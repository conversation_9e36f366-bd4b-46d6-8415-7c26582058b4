<template>
  <div class="debug-center"></div>
</template>
<script>
export default {
  data() {
    return {};
  },
  methods: {},
  watch: {
    $route(to, from) {
      this.debug = +this.$route.query.debug || 0;
      sessionStorage.setItem("debug", this.debug);
    }
  }
};
</script>
<style lang="scss">
@import "@/styles/mixin";
.debug-center {
  display: none;
  width: 0;
  height: 0;
  position: fixed;
  z-index: -9999;
}
</style>
