<template>
  <div class="video-anti-recording">
    <!-- 黑屏遮罩层 -->
    <div v-if="isBlocked" class="recording-block-mask">
      <div class="block-content">
        <div class="warning-icon">⚠️</div>
        <div class="warning-text">检测到录屏行为</div>
        <div class="warning-subtitle">视频已暂停保护</div>
      </div>
    </div>
  </div>
</template>

<script>
import Mousetrap from 'mousetrap'

export default {
  name: 'VideoAntiRecording',
  props: {
    // 是否启用防录屏
    enabled: {
      type: Boolean,
      default: true
    },
    // 视频播放器引用
    videoPlayerRef: {
      type: Object,
      default: null
    }
  },
  
  data() {
    return {
      isBlocked: false,
      blockTimer: null,
      
      // 检测相关
      keydownHandler: null,
      isElectron: false,
      
      // 页面可见性检测
      visibilityChangeHandler: null,
      
      // 窗口焦点检测
      windowBlurHandler: null,
      windowFocusHandler: null
    }
  },
  
  mounted() {
    if (this.enabled) {
      this.initAntiRecording()
    }
  },
  
  beforeDestroy() {
    this.cleanup()
  },
  
  watch: {
    enabled(newVal) {
      if (newVal) {
        this.initAntiRecording()
      } else {
        this.cleanup()
      }
    }
  },
  
  methods: {
    initAntiRecording() {
      this.isElectron = window.navigator.userAgent.includes('Electron')
      
      // 1. 键盘快捷键检测
      this.setupKeyboardDetection()
      
      // 2. 页面可见性检测
      this.setupVisibilityDetection()
      
      // 3. 窗口焦点检测
      this.setupFocusDetection()
      
      // 4. Electron IPC 检测（如果在 Electron 环境）
      if (this.isElectron && window.ipc) {
        window.ipc.on('screenshot-attempt-detected', this.handleRecordingDetected)
      }
      
      console.log('防录屏保护已启用')
    },
    
    setupKeyboardDetection() {
      // 原生键盘事件监听
      this.keydownHandler = (e) => {
        const key = e.key?.toLowerCase()
        const keyCode = e.keyCode || e.which
        
        // Print Screen 键
        if (keyCode === 44 || key === 'printscreen') {
          e.preventDefault()
          this.handleRecordingDetected('keyboard-printscreen')
          return false
        }
      }
      
      document.addEventListener('keydown', this.keydownHandler)
      
      // Mousetrap 快捷键监听
      const recordingKeys = [
        'printscreen',
        'ctrl+shift+s', 'control+shift+s', 
        'command+shift+s', 'meta+shift+s', 'cmd+shift+s',
        'command+shift+3', 'meta+shift+3', 'cmd+shift+3',
        'command+shift+4', 'meta+shift+4', 'cmd+shift+4',
        'command+shift+5', 'meta+shift+5', 'cmd+shift+5',
        'f12', // 开发者工具
        'ctrl+shift+i', 'command+option+i' // 开发者工具
      ]
      
      Mousetrap.bind(recordingKeys, (e) => {
        e.preventDefault()
        this.handleRecordingDetected('keyboard-shortcut')
        return false
      })
    },
    
    setupVisibilityDetection() {
      this.visibilityChangeHandler = () => {
        if (document.hidden) {
          // 页面被隐藏，可能在录屏
          this.handleRecordingDetected('page-hidden')
        }
      }
      
      document.addEventListener('visibilitychange', this.visibilityChangeHandler)
    },
    
    setupFocusDetection() {
      this.windowBlurHandler = () => {
        // 窗口失去焦点，可能在使用录屏软件
        this.handleRecordingDetected('window-blur')
      }
      
      this.windowFocusHandler = () => {
        // 窗口重新获得焦点，延迟恢复
        setTimeout(() => {
          this.unblockVideo()
        }, 2000)
      }
      
      window.addEventListener('blur', this.windowBlurHandler)
      window.addEventListener('focus', this.windowFocusHandler)
    },
    
    handleRecordingDetected(source = 'unknown') {
      console.log(`检测到录屏行为: ${source}`)
      
      // 暂停视频
      this.pauseVideo()
      
      // 显示黑屏遮罩
      this.blockVideo()
      
      // 触发事件
      this.$emit('recording-detected', {
        source,
        timestamp: Date.now()
      })
      
      // 设置自动恢复（可选）
      this.setAutoRecover()
    },
    
    pauseVideo() {
      // 通过 ref 暂停视频
      if (this.videoPlayerRef && this.videoPlayerRef.pause) {
        this.videoPlayerRef.pause()
      }
      
      // 通过事件通知父组件暂停视频
      this.$emit('request-pause-video')
      
      // 如果是 BingoVideo 组件
      if (this.videoPlayerRef && this.videoPlayerRef.player) {
        this.videoPlayerRef.player.pause()
      }
    },
    
    blockVideo() {
      this.isBlocked = true
      
      // 添加全局样式，隐藏视频内容
      document.body.classList.add('video-recording-blocked')
    },
    
    unblockVideo() {
      this.isBlocked = false
      
      // 移除全局样式
      document.body.classList.remove('video-recording-blocked')
      
      // 清除定时器
      if (this.blockTimer) {
        clearTimeout(this.blockTimer)
        this.blockTimer = null
      }
      
      // 触发恢复事件
      this.$emit('recording-block-removed')
    },
    
    setAutoRecover() {
      // 清除之前的定时器
      if (this.blockTimer) {
        clearTimeout(this.blockTimer)
      }
      
      // 5秒后自动恢复（可配置）
      this.blockTimer = setTimeout(() => {
        this.unblockVideo()
      }, 5000)
    },
    
    // 手动恢复方法
    manualRecover() {
      this.unblockVideo()
    },
    
    cleanup() {
      // 清理键盘监听
      if (this.keydownHandler) {
        document.removeEventListener('keydown', this.keydownHandler)
      }
      
      // 清理 Mousetrap
      Mousetrap.reset()
      
      // 清理页面可见性监听
      if (this.visibilityChangeHandler) {
        document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      }
      
      // 清理窗口焦点监听
      if (this.windowBlurHandler) {
        window.removeEventListener('blur', this.windowBlurHandler)
      }
      if (this.windowFocusHandler) {
        window.removeEventListener('focus', this.windowFocusHandler)
      }
      
      // 清理 Electron IPC
      if (this.isElectron && window.ipc) {
        window.ipc.removeAllListeners('screenshot-attempt-detected')
      }
      
      // 清理定时器
      if (this.blockTimer) {
        clearTimeout(this.blockTimer)
      }
      
      // 移除样式
      document.body.classList.remove('video-recording-blocked')
      
      console.log('防录屏保护已清理')
    }
  }
}
</script>

<style scoped lang="scss">
.video-anti-recording {
  position: relative;
}

.recording-block-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  user-select: none;
}

.block-content {
  text-align: center;
  color: #fff;
}

.warning-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.warning-text {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.warning-subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}
</style>

<style>
/* 全局样式：当检测到录屏时隐藏视频内容 */
body.video-recording-blocked .video-player,
body.video-recording-blocked .bingo-video,
body.video-recording-blocked .video-player-box {
  visibility: hidden !important;
}

body.video-recording-blocked .video-player video,
body.video-recording-blocked .bingo-video video {
  background: #000 !important;
}
</style>
