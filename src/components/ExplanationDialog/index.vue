<template>
  <el-dialog
    :title="experimentTitle"
    :visible.sync='dialogVisible'
    :close-on-click-modal='false'>
    <div class='explanation-body' v-loading='loading'>
      <div class="explanation_left ">
        <video v-if="videoUrl && videoUrl !== ''" :src="videoUrl" style="width: 100%;height: 50%;border-radius: 20px" controls></video>
        <el-image v-else class="image-view" :src="DefaultCover" fit="cover"/>
        <div class='know_btn' @click='dialogVisible = false'>知道了</div>
      </div>
      <div class="explanation_right">
        <div class="title">实验说明:</div>
        <div class="des" v-html="trainingData ? trainingData.description : ''">
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import DefaultCover from '@/assets/scratch/explanationDefault.png'
export default {
  data () {
    return {
      DefaultCover,
      dialogVisible: false,
      trainingId: null,
      studentCourseId: null,
      loading: false,
      experimentTitle: '',
      videoUrl: '',
      trainingData: null
    }
  },
  methods: {
    openDialog (data) {
      this.dialogVisible = true
      this.trainingData = data
      this.videoUrl = data.descriptionVideo
      this.experimentTitle = data.trainingName
    }
  }
}
</script>

<style scoped lang='scss'>
.explanation-body{
  width: 100%;
  height: 50vh;
  display: flex;
  .explanation_left{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .image-view{
      width: 100%;
      height: 50%;
      border-radius: 20px
    }
    .know_btn{
      height: 40px;
      width: 50%;
      color: white;
      background: linear-gradient(90deg, #36D1DC 0%, #5B86E5 100%);
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 5px;
      margin-top: 10vh;
    }
  }
  .explanation_right{
    width: 100%;
    height: 100%;
    font-size: 14px;
    padding-left: 20px;
    .title{
      font-weight: 600;
      margin-bottom: 5px;
      height: 30px;
    }
    .des{
      padding-left: 5px;
      overflow-y: auto;
      width: 100%;
      height: calc(100% - 35px);
    }
  }
}
::v-deep .el-dialog{
  border-radius: 10px;
  width: 850px;
}
</style>

<style lang='scss'>
.v-modal{
  display: none;
}
</style>
