<template>
  <div class="anti-recording-protection">
    <!-- 动态干扰层 -->
    <div v-if="isProtectionActive" class="interference-layer">
      <div 
        v-for="(particle, index) in particles" 
        :key="index"
        class="interference-particle"
        :style="particle.style"
      ></div>
    </div>
    
    <!-- 页面失焦遮罩 -->
    <div v-if="showBlurMask" class="blur-mask">
      <div class="blur-content">
        <div class="blur-icon">👁️</div>
        <div class="blur-text">请专注学习，检测到窗口失焦</div>
        <div class="blur-subtitle">点击继续观看</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AntiRecordingProtection',
  props: {
    // 是否启用保护
    enabled: {
      type: Boolean,
      default: true
    },
    // 视频是否正在播放
    isVideoPlaying: {
      type: Boolean,
      default: false
    },
    // 干扰强度 (1-5)
    interferenceLevel: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      isProtectionActive: false,
      showBlurMask: false,
      particles: [],
      blurTimer: null,
      focusCheckInterval: null,
      lastActiveTime: Date.now(),
      
      // 页面可见性检测
      isPageVisible: true,
      visibilityChangeHandler: null,
      
      // 鼠标活动检测
      mouseActivityTimer: null,
      lastMouseActivity: Date.now()
    }
  },
  
  mounted() {
    this.initProtection()
  },
  
  beforeDestroy() {
    this.cleanupProtection()
  },
  
  watch: {
    isVideoPlaying(newVal) {
      if (newVal && this.enabled) {
        this.startProtection()
      } else {
        this.stopProtection()
      }
    },
    
    enabled(newVal) {
      if (newVal && this.isVideoPlaying) {
        this.startProtection()
      } else {
        this.stopProtection()
      }
    }
  },
  
  methods: {
    initProtection() {
      // 页面可见性检测
      this.visibilityChangeHandler = () => {
        this.isPageVisible = !document.hidden
        if (!this.isPageVisible && this.isVideoPlaying) {
          this.handlePageBlur()
        } else if (this.isPageVisible) {
          this.handlePageFocus()
        }
      }
      
      document.addEventListener('visibilitychange', this.visibilityChangeHandler)
      
      // 窗口焦点检测
      window.addEventListener('blur', this.handleWindowBlur)
      window.addEventListener('focus', this.handleWindowFocus)
      
      // 鼠标活动检测
      document.addEventListener('mousemove', this.handleMouseActivity)
      document.addEventListener('click', this.handleMouseActivity)
    },
    
    startProtection() {
      if (!this.enabled) return
      
      this.isProtectionActive = true
      this.generateInterferenceParticles()
      
      // 开始焦点检查
      this.focusCheckInterval = setInterval(() => {
        this.checkUserActivity()
      }, 1000)
      
      this.$emit('protection-started')
    },
    
    stopProtection() {
      this.isProtectionActive = false
      this.showBlurMask = false
      this.particles = []
      
      if (this.focusCheckInterval) {
        clearInterval(this.focusCheckInterval)
        this.focusCheckInterval = null
      }
      
      if (this.blurTimer) {
        clearTimeout(this.blurTimer)
        this.blurTimer = null
      }
      
      this.$emit('protection-stopped')
    },
    
    generateInterferenceParticles() {
      const particleCount = this.interferenceLevel * 3
      this.particles = []
      
      for (let i = 0; i < particleCount; i++) {
        this.particles.push({
          id: i,
          style: {
            left: Math.random() * 100 + '%',
            top: Math.random() * 100 + '%',
            animationDelay: Math.random() * 2 + 's',
            animationDuration: (2 + Math.random() * 3) + 's'
          }
        })
      }
      
      // 定期更新粒子位置
      setTimeout(() => {
        if (this.isProtectionActive) {
          this.generateInterferenceParticles()
        }
      }, 3000)
    },
    
    handlePageBlur() {
      this.showBlurProtection()
      this.$emit('page-blur-detected')
    },
    
    handlePageFocus() {
      this.hideBlurProtection()
      this.$emit('page-focus-restored')
    },
    
    handleWindowBlur() {
      if (this.isVideoPlaying) {
        this.showBlurProtection()
        this.$emit('window-blur-detected')
      }
    },
    
    handleWindowFocus() {
      this.hideBlurProtection()
      this.$emit('window-focus-restored')
    },
    
    handleMouseActivity() {
      this.lastMouseActivity = Date.now()
      this.lastActiveTime = Date.now()
    },
    
    checkUserActivity() {
      const now = Date.now()
      const timeSinceLastActivity = now - this.lastActiveTime
      
      // 如果用户长时间无活动，可能在录屏
      if (timeSinceLastActivity > 30000) { // 30秒无活动
        this.showBlurProtection()
        this.$emit('suspicious-activity-detected', { 
          type: 'no-activity', 
          duration: timeSinceLastActivity 
        })
      }
    },
    
    showBlurProtection() {
      if (!this.showBlurMask) {
        this.showBlurMask = true
        
        // 暂停视频播放
        this.$emit('request-pause-video')
        
        // 设置自动恢复定时器
        this.blurTimer = setTimeout(() => {
          this.hideBlurProtection()
        }, 10000) // 10秒后自动恢复
      }
    },
    
    hideBlurProtection() {
      this.showBlurMask = false
      this.lastActiveTime = Date.now()
      
      if (this.blurTimer) {
        clearTimeout(this.blurTimer)
        this.blurTimer = null
      }
      
      // 可以恢复视频播放
      this.$emit('request-resume-video')
    },
    
    cleanupProtection() {
      this.stopProtection()
      
      if (this.visibilityChangeHandler) {
        document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      }
      
      window.removeEventListener('blur', this.handleWindowBlur)
      window.removeEventListener('focus', this.handleWindowFocus)
      document.removeEventListener('mousemove', this.handleMouseActivity)
      document.removeEventListener('click', this.handleMouseActivity)
      
      if (this.mouseActivityTimer) {
        clearTimeout(this.mouseActivityTimer)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.anti-recording-protection {
  position: relative;
  pointer-events: none;
}

.interference-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.interference-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float linear infinite;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

.blur-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  pointer-events: auto;
  cursor: pointer;
}

.blur-content {
  text-align: center;
  color: white;
  user-select: none;
}

.blur-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.blur-text {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.blur-subtitle {
  font-size: 1rem;
  opacity: 0.8;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
