<template>
  <div class="login-center" v-if="loginFade" @touchmove.prevent>
    <div class="login-box">
      <div class="login-bg"></div>
      <div class="login-close" @click="closeLoginPop"></div>
      <div class="login-form">
        <template v-if="+loginType === 1">
          <h1>
            登录
            <!-- <span class="login-wechat" @click="onopen"><i></i>微信登录</span> -->
          </h1>
          <el-form
            :model="loginRuleForm"
            :rules="loginRules"
            ref="loginRuleForm"
          >
            <div class="form-group">
              <i class="icon-user"></i>
              <el-form-item prop="username">
                <el-input
                  type="text"
                  v-model="loginRuleForm.username"
                  placeholder="请输入手机号"
                  @keyup.enter.native="doLogin()"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-group">
              <i class="icon-password"></i>
              <el-form-item prop="password">
                <el-input
                  type="password"
                  v-model="loginRuleForm.password"
                  placeholder="请输入密码"
                  class="inputs"
                  @keyup.enter.native="doLogin()"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
          <div class="form-btn" @click="doLogin()">登录</div>
          <div class="other-set">
            <span class="gotoRegister" @click="changeLoginType(2)"
              >没有账号？立即注册</span
            >
            <span class="gotoForgot" @click="changeLoginType(3)"
              >忘记密码？</span
            >
          </div>
        </template>
        <template v-else-if="+loginType === 2">
          <h1>
            注册
            <span class="gotologin" @click="changeLoginType(1)"
              ><i></i>有账号 去登录</span
            >
          </h1>
          <el-form
            :model="registerRuleForm"
            :rules="registerRules"
            ref="registerRuleForm"
          >
            <div class="form-group mb-15">
              <el-form-item prop="username">
                <el-input
                  type="text"
                  v-model="registerRuleForm.username"
                  placeholder="请输入手机号"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-group mb-15">
              <el-form-item prop="">
                <el-input
                  type="text"
                  v-model="registerRuleForm.code"
                  placeholder="请输入验证码"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
              <div
                class="send-code"
                @click="handleSliderWrapperClick"
                :class="smsDisabled && 'disabled'"
              >
                {{ smsDisabled ? countdown + "秒" : "获取验证码" }}
              </div>
            </div>
            <div class="form-group mb-15">
              <el-form-item prop="password">
                <el-input
                  type="password"
                  v-model="registerRuleForm.password"
                  placeholder="请输入密码"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-group mb-15">
              <el-form-item prop="checkpassword">
                <el-input
                  type="password"
                  v-model="registerRuleForm.checkpassword"
                  placeholder="请输入确认密码"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-btn" @click="doRegister()">下一步</div>
          </el-form>
        </template>
        <template v-else-if="+loginType === 3">
          <h1>
            忘记密码
            <span class="gotologin" @click="changeLoginType(1)"
              ><i></i>有账号 去登录</span
            >
          </h1>
          <el-form
            :model="forgetRuleForm"
            :rules="forgetRules"
            ref="forgetRuleForm"
          >
            <div class="form-group mb-15">
              <el-form-item prop="username">
                <el-input
                  type="text"
                  v-model="forgetRuleForm.username"
                  placeholder="请输入手机号"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-group mb-15">
              <el-form-item prop="">
                <el-input
                  type="text"
                  v-model="forgetRuleForm.code"
                  placeholder="请输入验证码"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
              <div
                class="send-code"
                @click="handleSliderWrapperClick"
                :class="smsDisabled && 'disabled'"
              >
                {{ smsDisabled ? countdown + "秒" : "获取验证码" }}
              </div>
            </div>
            <div class="form-group mb-15">
              <el-form-item prop="password">
                <el-input
                  type="password"
                  v-model="forgetRuleForm.password"
                  placeholder="请输入新密码"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-group mb-15">
              <el-form-item prop="checkpassword">
                <el-input
                  type="password"
                  v-model="forgetRuleForm.checkpassword"
                  placeholder="请输入确认密码"
                  class="inputs"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-btn" @click="recoverPass">提交</div>
          </el-form>
        </template>
        <template v-else>
          <h1>
            完善信息
            <span class="gotologin" @click="closeLoginPop()"><i></i>跳过</span>
          </h1>
          <el-form>
            <div class="form-group mb-15">
              <el-form-item>
                <el-input
                  type="text"
                  placeholder="请输入昵称"
                  class="inputs"
                  auto-complete="off"
                  v-model.trim="displayName"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-group mb-15">
              <el-form-item>
                <el-input
                  type="text"
                  placeholder="请输入课程兑换码 (选填)"
                  class="inputs"
                  auto-complete="off"
                  v-model.trim="CDkey"
                ></el-input>
              </el-form-item>
            </div>
            <div class="form-btn" @click="_updateUserInfo()">完成</div>
          </el-form>
        </template>
      </div>
    </div>
    <el-dialog
      title="滑动获取验证码"
      :show-close="false"
      :center="true"
      :width="'350px'"
      :top="'45vh'"
      :visible.sync="dialogSliderVisible"
    >
      <div class="code-slider">
        <nocaptcha @callback="slideToGetCode" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import nocaptcha from "./nocaptcha";
import { slideToGetSmsCode, forgetPassword } from "@/api/login.js";
import { updateUserInfo } from "@/api/user";
import { exchangeCourse } from "@/api/lesson";

export default {
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入确认密码"));
      } else if (value !== this.registerRuleForm.password) {
        callback(new Error("两次输入密码不一致"));
        this.registerRuleForm.checkpassword = "";
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入确认密码"));
      } else if (value !== this.forgetRuleForm.password) {
        callback(new Error("两次输入密码不一致"));
        this.forgetRuleForm.checkpassword = "";
      } else {
        callback();
      }
    };
    return {
      displayName: null,
      CDkey: null,
      userinfo: {
        username: "",
        code: ""
      },
      dialogSliderVisible: false,
      countdown: 60,
      smsDisabled: false,
      loginRuleForm: {
        username: "",
        password: ""
      },
      loginRules: {
        username: [
          {
            required: true,
            message: "请输入手机号",
            trigger: ["blur", "change"]
          }
        ],
        password: [
          { required: true, message: "请输入密码", trigger: ["blur", "change"] }
        ]
      },
      registerRuleForm: {
        username: "",
        code: "",
        password: "",
        checkpassword: ""
      },
      registerRules: {
        username: [
          {
            required: true,
            message: "请输入手机号",
            trigger: ["blur", "change"]
          }
        ],
        password: [
          { required: true, message: "请输入密码", trigger: ["blur", "change"] }
        ],
        checkpassword: [{ validator: validatePass, trigger: ["blur"] }]
      },
      forgetRuleForm: {
        username: "",
        code: "",
        password: "",
        checkpassword: ""
      },
      forgetRules: {
        username: [
          {
            required: true,
            message: "请输入手机号",
            trigger: ["blur", "change"]
          }
        ],
        password: [
          {
            required: true,
            message: "请输入新密码",
            trigger: ["blur", "change"]
          }
        ],
        checkpassword: [{ validator: validatePass2, trigger: ["blur"] }]
      },
      smsCodeType: null,
      isUpdate: true,
      isexchange: true
    };
  },
  created() {},
  methods: {
    _updateUserInfo() {
      console.log(this.displayName);
      console.log(this.CDkey);
      if (this.displayName) {
        updateUserInfo({
          displayName: this.displayName
        })
          .then(response => {
            if (+response.data.code === 200) {
              this.isUpdate = true;
            } else {
              this.isUpdate = false;
            }
            this.$store.dispatch("GetUserInfo");
          })
          .catch(err => {
            this.$toast(err, {
              position: "center",
              duration: "2000"
            });
          });
      }
      if (this.CDkey) {
        exchangeCourse({
          exchangeCode: this.CDkey
        })
          .then(response => {
            if (+response.data.code === 200) {
              this.isexchange = true;
            } else {
              this.$toast(response.data.message, {
                position: "center",
                duration: "2000"
              });
              this.isexchange = false;
            }
          })
          .catch(err => {
            this.$toast(err, {
              position: "center",
              duration: "2000"
            });
            this.pageLoaded = true;
          });
      }
      this.closeLoginPop();
    },
    onopen() {
      this.$toast("敬请期待", {
        position: "center",
        duration: "2000"
      });
    },
    slideToGetCode(data) {
      let mobile = "";
      if (this.loginType === 2) {
        mobile = this.registerRuleForm.username;
        this.smsCodeType = "RIGESTER";
      } else if (this.loginType === 3) {
        mobile = this.forgetRuleForm.username;
        this.smsCodeType = "FORGET_PW";
      }
      if (mobile === "") {
        this.$toast("请输入手机号");
        data.nc.reset();
        return false;
      } else if (!/^\d+$/.test(mobile)) {
        this.$toast("请输入手机号");
        data.nc.reset();
        return false;
      }
      slideToGetSmsCode({
        sessionId: data.csessionid,
        token: data.token,
        scene: data.scene,
        sig: data.sig,
        mobile: mobile,
        smsCodeType: this.smsCodeType
      })
        .then(response => {
          if (response.status !== 200) {
            data.nc.reset();
            this.list = [];
            this.$message.error(response.data.message);
          } else {
            if (+response.data.code === 200) {
              this.countdown = 60;
              this.smsDisabled = true;
              setTimeout(this.tick, 1000);
              this.dialogSliderVisible = false;
              data.nc.reset();
            } else {
              data.nc.reset();
              this.$toast(response.data.message);
            }
          }
        })
        .catch(e => {
          console.log(e);
        });
    },
    handleSliderWrapperClick() {
      let mobile = "";
      if (this.loginType === 2) {
        mobile = this.registerRuleForm.username;
      } else if (this.loginType === 3) {
        mobile = this.forgetRuleForm.username;
      }
      if (mobile === "") {
        this.$toast("请输入手机号");
        return false;
      } else if (!/^\d+$/.test(mobile)) {
        this.$toast("请输入手机号");
        return false;
      }
      if (!this.smsDisabled) {
        this.dialogSliderVisible = true;
      }
    },
    closeLoginPop(type) {
      this.loginRuleForm.username = "";
      this.loginRuleForm.password = "";
      this.registerRuleForm.username = "";
      this.registerRuleForm.code = "";
      this.registerRuleForm.password = "";
      this.registerRuleForm.checkpassword = "";
      this.forgetRuleForm.username = "";
      this.forgetRuleForm.code = "";
      this.forgetRuleForm.password = "";
      this.forgetRuleForm.checkpassword = "";
      this.smsDisabled = false;
      this.countdown = 60;
      this.dialogSliderVisible = false;
      let data = {
        flag: false,
        type: type || 1,
        path: ""
      };
      this.$store.dispatch("setLoginFadeAtion", data);
    },
    changeLoginType(type) {
      this.$store.dispatch("setLoginFadeAtion", { flag: true, type: type });
      if (type === 1) {
        this.$nextTick(() => {
          this.$refs["loginRuleForm"].resetFields();
        });
      } else if (type === 2) {
        this.$nextTick(() => {
          this.$refs["registerRuleForm"].resetFields();
        });
      } else if (type === 3) {
        this.$nextTick(() => {
          this.$refs["forgetRuleForm"].resetFields();
        });
      }
      this.smsDisabled = false;
      this.countdown = 60;
      this.dialogSliderVisible = false;
    },
    doLogin() {
      this.$refs["loginRuleForm"].validate(valid => {
        if (valid) {
          this.$store
            .dispatch("LoginByPass", {
              mobileOrEamil: this.loginRuleForm.username,
              password: this.loginRuleForm.password
            })
            .then(response => {
              if (response.data.code === 200) {
                if (this.loginPath) {
                  this.$router.push({
                    path: this.loginPath
                  });
                }
                this.closeLoginPop();
              } else {
                this.$toast(response.data.message);
              }
            })
            .catch(err => {
              console.log(err);
            });
        } else {
          return false;
        }
      });
    },
    doRegister() {
      this.$refs["registerRuleForm"].validate(valid => {
        if (valid) {
          this.$store
            .dispatch("doRegister", {
              mobileOrEmail: this.registerRuleForm.username,
              verifyCode: this.registerRuleForm.code,
              password: this.registerRuleForm.password
            })
            .then(response => {
              if (response.data.code === 200) {
                this.$store.dispatch("setLoginFadeAtion", {
                  flag: true,
                  type: 4
                });
              } else {
                this.$toast(response.data.message);
              }
            })
            .catch(err => {
              console.log(err);
            });
        } else {
          return false;
        }
      });
    },
    recoverPass() {
      this.$refs["forgetRuleForm"].validate(valid => {
        if (valid) {
          forgetPassword({
            mobile: this.forgetRuleForm.username,
            verifyCode: this.forgetRuleForm.code,
            newPassword: this.forgetRuleForm.password
          })
            .then(response => {
              if (+response.data.code === 200) {
                this.$toast("修改成功");
                this.$store.dispatch("setLoginFadeAtion", {
                  flag: true,
                  type: 1
                });
              } else {
                this.$toast(response.data.message);
              }
            })
            .catch(err => {
              this.$toast(err, {
                position: "center",
                duration: "2000"
              });
            });
        } else {
          return false;
        }
      });
    },
    tick() {
      if (this.countdown <= 1) {
        this.smsDisabled = false;
      } else {
        this.countdown--;
        setTimeout(this.tick, 1000);
      }
    }
  },
  components: {
    nocaptcha
  },
  watch: {},
  computed: {
    ...mapGetters(["loginFade", "loginStatus", "loginType", "loginPath"])
  }
};
</script>
<style lang="scss">
@import "@/styles/mixin";
@import "@/styles/variables";

.login-center {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  .login-box {
    border-radius: 40px;
    width: 370px;
    height: 497px;
    background: #fafafc;
    position: relative;
    padding-left: 595px;
    padding-top: 80px;
    .login-bg {
      width: 577px;
      height: 577px;
      position: absolute;
      left: 0px;
      top: 0px;
      @include bg-image("login-bg");
      background-size: cover;
    }
    .login-close {
      width: 40px;
      height: 40px;
      @include bg-image("login-close");
      background-size: cover;
      right: 20px;
      top: 20px;
      position: absolute;
      cursor: pointer;
    }
    .login-form {
      padding: 0px 30px;
      position: relative;
      height: 100%;
      h1 {
        font-size: 28px;
        color: #161823;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 40px;
        .login-wechat {
          cursor: pointer;
          color: #4cbf00;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          i {
            width: 28px;
            height: 28px;
            @include bg-image("icon-wechat");
            background-size: cover;
            margin-right: 5px;
          }
        }
        .gotologin {
          cursor: pointer;
          color: #ff8914;
          font-size: 14px;
        }
      }
      .form-group {
        height: 57px;
        background: #ffffff;
        box-shadow: 0px 3px 12px 2px rgba(0, 0, 0, 0.07);
        border-radius: 29px;
        margin-bottom: 25px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0px 20px;
        .el-form-item {
          width: 100%;
          margin: 0;
          .el-form-item__error {
            color: $bc;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            padding: 0;
            right: 20px;
            left: unset;
          }
          .inputs {
            input {
              display: block;
              width: 100%;
              height: 37px;
              line-height: 37px;
              padding: 0px;
              border: 0;
              background: transparent;
              &::placeholder {
                font-size: 14px;
                color: #a1a1a1;
              }
            }
          }
          i {
            width: 28px;
            height: 28px;
            margin-right: 20px;
            &.icon-user {
              @include bg-image("icon-user");
              background-size: cover;
            }
            &.icon-password {
              @include bg-image("icon-password");
              background-size: cover;
            }
          }
        }
        &.mb-15 {
          margin-bottom: 15px;
        }

        .send-code {
          background: #c32136;
          box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.16);
          border-radius: 21px;
          font-size: 12px;
          color: #fff;
          width: 120px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          cursor: pointer;
          box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0);

          &.disabled {
            cursor: not-allowed;
            box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.16);
            background: #fff;
            color: #bdbdbd;
          }
        }
      }
      .form-btn {
        width: 177px;
        height: 57px;
        background: linear-gradient(270deg, #eb5d6f 0%, #c32136 100%);
        border-radius: 29px;
        line-height: 57px;
        text-align: center;
        color: #fff;
        font-size: 16px;
        margin: 40px 0px 20px;
        cursor: pointer;
      }
      .other-set {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: absolute;
        bottom: 40px;
        left: 30px;
        right: 30px;
        .gotoRegister {
          color: #48baff;
          font-size: 14px;
          cursor: pointer;
        }
        .gotoForgot {
          color: #bdbdbd;
          font-size: 14px;
          cursor: pointer;
        }
      }
    }
  }
}
.code-slider {
  width: 100%;
  margin: 0 auto;
  display: block;
  .nc-container .nc_scale span {
    height: 34px !important;
    line-height: 34px !important;
  }
}
</style>
