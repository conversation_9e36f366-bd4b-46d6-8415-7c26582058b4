<template>
  <div class="nocaptcha">
    <div id="no-captcha" class="nc-container"></div>
  </div>
</template>

<script>
import VS2 from 'vue-script2'

export default {
  data () {
    return {
      appKey: 'FFFF0N0000000000662A',
      lang: 'cn',
      nc_scene: 'nc_login_h5',
      NC_Opt: {},
      nc: null
    }
  },
  computed: {
    nc_token() {
      return `${this.appKey}:${(new Date()).getTime()}:${Math.random()}`
    }
  },
  created () {
    this.init()
  },
  methods: {
    init() {
      const token = this.nc_token
      this.NC_Opt = {
        renderTo: '#no-captcha',
        appkey: this.appKey,
        scene: this.nc_scene,
        token: token,
        customWidth: 300,
        trans: { key1: 'code0' },
        language: this.lang,
        isEnabled: true,
        timeout: 3000,
        times: 5,
        callback: (data) => {
          this.$emit('callback', {
            csessionid: data.csessionid,
            sig: data.sig,
            token: token,
            scene: this.nc_scene,
            nc: this.nc
          })
        }
      }
      VS2.load('//g.alicdn.com/sd/ncpc/nc.js?t=2015052012').then(() => {
        // eslint-disable-next-line
        this.nc = new noCaptcha(this.NC_Opt)
        this.nc.upLang('cn', {
          _startTEXT: '划到最右边哦！',
          _yesTEXT: "成功啦！",
          _error300: "出错啦，请<a href=\"javascript:showCaptcha()\">刷新</a>使用图形验证码！",
          _errorNetwork: "网络不给力，请<a href=\"javascript:__nc.reset()\">点击刷新</a>"
        })
      })
    }
  }
}
</script>

<style lang="scss">
</style>
