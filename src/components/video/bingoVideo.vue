<template>
  <div class="bingo-video">
    <video-player
      ref="videoPlayer"
      class="video-player"
      :options="playerOptions"
      playsinline
      webkit-playsinline
      x5-video-player-type="h5"
      x5-playsinline="true"
      x5-video-orientation="portraint"
      @ready="playerReadied"
      @canplay="onPlayerCanplay($event)"
      @play="onPlayerPlay($event)"
      @pause="onPlayerPause($event)"
      @ended="onPlayerEnd($event)"
      @waiting="onPlayerWaiting($event)"
      @timeupdate="onPlayerTimeUpdate($event)"
    />
    <!-- <lottie
      v-if="isLoading"
      :width="1920"
      :height="1080"
      :options="loadingAniOptions"
      class="video-animation"
    /> -->
  </div>
</template>

<script>
import loadingAni from '@/assets/animate/loading.json'
// import lottie from 'vue-lottie'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
import { videoPlayer } from 'vue-video-player'
window.videojs = require('video.js')
// require('video.js/dist/lang/zh-CN')

import StreamToBlob from 'stream-to-blob'
import { Readable } from 'stream'

export default {
  components: { videoPlayer },
  props: {
    sources: Array,
    autoplay: {
      type: Boolean,
      require: false,
      default: true
    },
    hasFull: {
      type: Boolean,
      require: false,
      default: true
    }
  },
  data () {
    return {
      onError: false,
      errorTime: 0,
      sourceUrl: null,
      playerOptions: {
        language: 'zh-CN',
        // controls: true,
        autoplay: this.autoplay,
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'progressControl' }, // 播放进度条
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'timeDivider' },
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            {
              name: 'playbackRateMenuButton',
              playbackRates: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
            }
            // { name: 'FullscreenToggle' } // 全屏
          ]
        },
        sources: this.sources,
        hls: true,
        // fluid: true,
        // aspectRatio: '16:9',
        notSupportedMessage: '此视频暂无法播放,请稍后再试'
      },
      loadingAniOptions: {
        animationData: loadingAni,
        loop: true,
        autoplay: true
      },
      isLoading: true,
      duration: 0,
      lockTouch: false,
      touchObj: {
        beginX: null,
        positionX: null,
        beginClientX: null,
        beginY: null,
        positionY: null,
        beginClientY: null,
        sliderLong: null
      },
      videoDom: null,
      fullBtn: null
    }
  },
  computed: {
    player () {
      return this.$refs.videoPlayer.player
    }
  },
  watch: {
    sources: {
      handler (val) {
        console.log(this.sources[0])
        this.playerOptions.poster = `${this.sources[0].src}?x-oss-process=video/snapshot,t_1000,m_fast`
        // if (window.ipc && this.sources[0].size < 100000) {
        //   this.loadVideo(this.sources[0].src)
        // }
      },
      deep: true
    }
  },
  mounted () {
    document
      .querySelector('.bingo-video')
      .addEventListener('contextmenu', (event) => {
        event.preventDefault()
      })
    // if (window.ipc && this.sources[0].size < 100000) {
    //   this.loadVideo(this.sources[0].src)
    // }
  },
  beforeDestroy () {
    this.videoDom.removeEventListener('touchstart', this.mousedown)
    this.videoDom.removeEventListener('touchmove', this.mousemove)
    this.videoDom.removeEventListener('touchend', this.mouseup)
    const evt = 'onorientationchange' in window ? 'orientationchange' : 'resize'
    window.removeEventListener(evt, this.resize, false)
  },
  methods: {
    creatFullBtn () {
      this.fullBtn = this.player.getChild('ControlBar').addChild('button')
      this.fullBtn.addClass('f2em')
      if (this.isFullScreen()) {
        this.fullBtn.addClass('vjs-icon-fullscreen-exit')
        this.fullBtn.removeClass('vjs-icon-fullscreen-enter')
      } else {
        this.fullBtn.addClass('vjs-icon-fullscreen-enter')
        this.fullBtn.removeClass('vjs-icon-fullscreen-exit')
      }
      this.fullBtn.on('click', this.triggerFullScreen)
      this.fullBtn.on('touchstart', this.triggerFullScreen)
    },
    triggerFullScreen () {
      if (this.isFullScreen()) {
        this.exitFullscreen()
        this.fullBtn.addClass('vjs-icon-fullscreen-enter')
        this.fullBtn.removeClass('vjs-icon-fullscreen-exit')
      } else {
        this.requestFullscreen()
        this.fullBtn.addClass('vjs-icon-fullscreen-exit')
        this.fullBtn.removeClass('vjs-icon-fullscreen-enter')
      }
    },
    playerReadied () {
      // console.log('playerReadied')
      this.$emit('playerReadied')
      this.isLoading = true
      // const playButton = document.getElementsByClassName('vjs-big-play-button')[0]
      // console.log(playButton)
      // playButton.style.cssText = 'display: none'
      if (window.orientation === 180 || window.orientation === 0) {
        this.player.controlBar.ProgressControl.disable()
      } else {
        this.player.controlBar.ProgressControl.enable()
      }
      this.player.controlBar.ProgressControl.on('touchstart', this.fn)
      this.player.controlBar.ProgressControl.on('touchmove', this.fn)
      this.player.controlBar.ProgressControl.on('touchend', this.fn)
      this.player.controlBar.el_.style.position = 'fixed'
      const videoId = this.player.id()
      this.videoDom = document.getElementById(videoId)
      // this.videoDom.addEventListener('mousedown', this.mousedown)
      this.videoDom.addEventListener('touchstart', this.mousedown)
      // this.videoDom.addEventListener('mousemove', this.mousemove)
      this.videoDom.addEventListener('touchmove', this.mousemove)
      // this.videoDom.addEventListener('mouseup', this.mouseup)
      this.videoDom.addEventListener('touchend', this.mouseup)
      const evt = 'onorientationchange' in window ? 'orientationchange' : 'resize'
      window.addEventListener(evt, this.resize, false)

      // 自定义全屏
      this.creatFullBtn()
    },
    _isMobile () {
      const flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
      return flag
    },
    // 是否全屏
    isFullScreen () {
      return document.fullScreen || document.webkitIsFullScreen || document.mozFullScreen
    },
    // 全屏
    requestFullscreen (el) {
      const isFullScreen = this.isFullScreen()
      const ele = el || document.documentElement
      const rfs = ele.requestFullscreen || ele.webkitRequestFullscreen || ele.mozRequestFullScreen || ele.msRequestFullscreen

      // 如果全屏，返回
      if (isFullScreen) return

      if (rfs) {
        rfs.call(ele)
      } else if (typeof window.ActiveXObject !== 'undefined') {
        const wscript = new window.ActiveXObject('WScript.Shell')
        if (wscript) {
          wscript.SendKeys('{F11}')
        }
      }
    },
    // 退出全屏
    exitFullscreen () {
      const isFullScreen = this.isFullScreen()
      const ele = document
      const efs = ele.exitFullscreen || ele.webkitExitFullscreen || ele.mozCancelFullScreen

      // 如果不是全屏，返回
      if (!isFullScreen) return

      if (efs) {
        efs.call(ele)
      } else if (typeof window.ActiveXObject !== 'undefined') {
        const wscript = new window.ActiveXObject('WScript.Shell')
        if (wscript) {
          wscript.SendKeys('{F11}')
        }
      }
    },
    fn () {},
    resize () {
      if (window.orientation === 180 || window.orientation === 0) {
        this.videoDom.addEventListener('touchstart', this.mousedown)
        this.videoDom.addEventListener('touchmove', this.mousemove)
        this.videoDom.addEventListener('touchend', this.mouseup)
      } else {
        this.videoDom.removeEventListener('touchstart', this.mousedown)
        this.videoDom.removeEventListener('touchmove', this.mousemove)
        this.videoDom.removeEventListener('touchend', this.mouseup)
        this.player.controlBar.ProgressControl.off('touchstart', this.fn)
        this.player.controlBar.ProgressControl.off('touchmove', this.fn)
        this.player.controlBar.ProgressControl.off('touchend', this.fn)
      }
    },
    mousedown (e) {
      e.preventDefault()
      if (window.orientation === 180 || window.orientation === 0) {
        if (e.touches && e.touches.length > 0) {
          this.lockTouch = true
          this.touchObj.beginX = e.touches[0].pageX
          this.touchObj.beginY = e.touches[0].pageY
          this.touchObj.beginWidth = this.player.controlBar.ProgressControl.seekBar.playProgressBar.width()
          // this.player.pause()
        }
      }
    },
    mousemove (e) {
      e.preventDefault()
      if (window.orientation === 180 || window.orientation === 0) {
        // 是否竖屏
        if (e.touches && e.touches.length > 0) {
          const x = e.touches[0].pageX
          const y = e.touches[0].pageY
          this.touchObj.movX = x - this.touchObj.beginX
          this.touchObj.movY = y - this.touchObj.beginY

          const progressWidth = this.player.controlBar.ProgressControl.width()
          let proWidth = 0
          const ml = this.touchObj.beginWidth + this.touchObj.movY
          if (ml <= 0) {
            // 进度条长度最小和最大值的界定
            proWidth = 0
          } else if (ml >= progressWidth) {
            proWidth = progressWidth
          } else {
            proWidth = ml
            this.touchObj.lastWidth = ml
          }
          const duration = this.player.duration()
          const time = (proWidth / progressWidth) * duration
          this.player.currentTime(Math.floor(time))
        }
      }
    },
    mouseup (e) {
      e.preventDefault()
      if (window.orientation === 180 || window.orientation === 0) {
        // const width = this.player.controlBar.ProgressControl.seekBar.playProgressBar.width()
        // console.log(this.touchObj.beginWidth, '--------')
        // console.log(this.player.controlBar.ProgressControl.seekBar.playProgressBar.width())
        // console.log(this.player.paused())
        // if (this.touchObj.beginWidth === width || Math.abs(this.touchObj.beginWidth - width) < 10) {
        //   if (this.player.paused()) {
        //     console.log('paly')
        //     this.player.play()
        //   } else {
        //     this.player.pause()
        //     console.log('pause')
        //   }
        // }
        // 重置
        this.touchObj = {
          beginX: null,
          positionX: null,
          beginClientX: null,
          beginY: null,
          positionY: null,
          beginClientY: null,
          sliderLong: null,
          beginWidth: null
        }
      }
      // this.player.play()
    },
    getStyle (obj, styleName) { // 获取元素样式的方法
      if (obj.currentStyle) {
        return obj.currentStyle[styleName]
      } else {
        return getComputedStyle(obj, null)[styleName]
      }
    },
    onPlayerCanplay (player) {
      // console.log('onPlayerCanplay', player)
      const ua = navigator.userAgent.toLowerCase()
      if (ua.match(/MicroMessenger/i) === 'micromessenger') {
        player.controlBar.ProgressControl.disable()
      }

      this.isLoading = false
      // const playButton = document.getElementsByClassName('vjs-big-play-button')['0']
      // playButton.style.cssText = 'display: block'
      this.$emit('onPlayerCanplay')
    },
    onPlayerPlay (player) {
      if (this.onError) {
        this.onError = false
        this.player.currentTime(this.errorTime)
        this.errorTime = 0
        return
      }
      if (player.error_ && player.error_.code === 3) {
        this.errorTime = this.player.currentTime()
        this.onError = true
        this.player.src([{
          src: this.sources[0].src,
          type: 'video/mp4'
        }])
        this.play()
      }
      console.log('onPlayerPlay', player)

      // this.$refs.videoPlayer.player.removeClass('vjs-custom-waiting')
      // const playButton = document.getElementsByClassName('vjs-big-play-button')['0']
      // playButton.style.cssText = 'display: none'
      this.$emit('onPlayerPlay')
    },
    onPlayerPause (player) {
      console.log('onPlayerPause', player)
      if (player.error_ && player.error_.code === 3) {
        this.errorTime = this.player.currentTime()
        this.onError = true
        this.player.src([{
          src: this.sources[0].src,
          type: 'video/mp4'
        }])
        this.play()
      }
      player.bigPlayButton.show()
      var button = document.getElementsByClassName('vjs-big-play-button')
      button[0].title = '继续学习'
      this.$emit('onPlayerPause')
    },
    onPlayerEnd (player) {
      // console.log('onPlayerEnd', player)
      if (player.isFullscreen()) {
        player.exitFullscreen()
      }
      this.$emit('onPlayerEnd')
    },
    onPlayerWaiting (player) {
      // console.log('onPlayerWaiting', player)
      this.$emit('onPlayerWaiting')
    },
    onPlayerTimeUpdate (player) {
      const remainingTime = Math.round(
        player.cache_.duration - player.cache_.currentTime
      )
      this.$emit('onPlayerTimeUpdate', remainingTime)
    },
    play () {
      this.player.play()
    },
    pause () {
      this.player.pause()
    },
    loadVideo (url) {
      const fileName = url.split('/').reverse()[0].split('.')[0]
      window.ipc.invoke('loadLocalVideo', fileName).then(buffer => {
        // console.log(buffer)
        const stream = this.bufferToStream(buffer)
        let fileUrl // blob对象
        StreamToBlob(stream)
          .then(res => {
            fileUrl = res
            // console.log(fileUrl);

            // 将blob对象转成blob链接
            if (this.sourceUrl) {
              window.URL.revokeObjectURL(this.sourceUrl)
              this.sourceUrl = null
            }
            this.sourceUrl = window.URL.createObjectURL(fileUrl)
            fileUrl = null
            // window.URL.revokeObjectURL(fileUrl)
            // console.log(filePath);
            if (this.player) {
              // 动态设置播放地址
              this.player.src([{
                src: this.sourceUrl,
                type: 'video/mp4'
              }])
            }
          })
          .catch(err => {
            console.log(err)
          })
      })
    },
    bufferToStream (binary) {
      const readableInstanceStream = new Readable({
        read () {
          this.push(binary)
          this.push(null)
        }
      })
      return readableInstanceStream
    }

  }
}
</script>

<style lang='scss' scoped>
.bingo-video {
  position: relative;
  width: 100%;
  height: 100%;
  .video-player {
    width: 100%;
    height: 100%;
  }

  .video-animation {
    pointer-events: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 50;
  }

  ::v-deep .video-js .vjs-tech {
    position: fixed !important;
  }
}
</style>

<style lang='scss'>

.navbar-video {
  .bingo-video {
    .video-js .vjs-big-play-button {
      &::after {
        background: transparent;
      }
    }
  }
}
.bingo-video {

  .video-js {
    width: 100%;
    height: 100%;
  }

  .video-js .vjs-big-play-button {
    // display: none;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: none;
    z-index: 10;

    &::after {
      display: block;
      position: absolute;
      margin-top: 0 !important;
      content: '';
      left: 50%;
      top: 50%;
      min-width: 1180px;
      min-height: calc(1180px * 9 / 16);
      width: 100vw;
      height: calc(100vw * 9 / 16);
      background: rgba(0, 0, 0, 0.3);
      transform: translate(-50%, -50%);
      z-index: -1;
    }

    .vjs-icon-placeholder:before {
      content: '';
      height: 100%;
      width: 100%;
      background: url('../../assets/images/ai/ai-play-btn.png') center center
        no-repeat;
      background-size: contain;
      // z-index: 20;
    }
  }

  .vjs-has-started.vjs-paused .vjs-big-play-button {
    display: block !important;
  }

  .video-js .vjs-slider {
    background: rgba(255, 255, 255, 0.2);
  }

  .video-js .vjs-load-progress div {
    background: rgba(255, 255, 255, 0.5);
  }

  .video-js .vjs-control-bar {
    height: 6em;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 20;

    .vjs-play-control {
      .vjs-icon-placeholder:before {
        font-size: 2.2em;
        height: initial;
        top: 50%;
        transform: translate(0, -50%) !important;
      }
    }

    .vjs-time-control {
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 6em;

      &.vjs-current-time {
        padding-right: 0;
      }

      &.vjs-time-divider {
        min-width: 0;
        padding: 0 4px;
      }

      &.vjs-duration {
        padding-left: 0;
      }
    }

    .vjs-control.vjs-button {
      .vjs-icon-placeholder:before {
        font-size: 2em;
        height: initial;
        top: 50%;
        transform: translate(0, -50%) !important;
      }
    }

    .vjs-fullscreen-control .vjs-icon-placeholder:before {
      color: transparent;
      background: url('../../assets/images/ai/ai-fullscreen.png') center center
        no-repeat;
      background-size: 16px 16px;
    }

    .vjs-fullscreen-control .vjs-icon-placeholder:before {
      color: transparent;
      background: url('../../assets/images/ai/ai-fullscreen.png') center center
        no-repeat;
      background-size: 16px 16px;
    }
  }

  .video-js.vjs-fullscreen {
    .vjs-fullscreen-control .vjs-icon-placeholder:before {
      color: transparent;
      background: url('../../assets/images/ai/ai-miniscreen.png') center center
        no-repeat;
      background-size: 16px 16px;
    }
  }
  .vjs-error .vjs-error-display .vjs-modal-dialog-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .vjs-icon-play,
  .video-js .vjs-big-play-button .vjs-icon-placeholder:before {
    font-size: 30px;
    margin-top: 5px;
  }
  .video-js .vjs-play-progress:before {
    top: 50%;
    transform: translateY(-50%);
  }
  .video-js .vjs-volume-vertical {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 1em;
  }
  .vjs-slider-vertical .vjs-volume-level:before {
    left: 50%;
    transform: translateX(-50%);
  }

  .vjs-custom-waiting .vjs-loading-spinner {
    display: block;
    visibility: visible;
  }
  .video-js.vjs-custom-waiting .vjs-loading-spinner:before,
  .video-js.vjs-custom-waiting .vjs-loading-spinner:after {
    /* I just copied the same animation as in the default css file */
    -webkit-animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8)
        infinite,
      vjs-spinner-fade 1.1s linear infinite;
    animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8) infinite,
      vjs-spinner-fade 1.1s linear infinite;
  }

  .vjs-playback-rate .vjs-playback-rate-value {
    line-height: 4em;
  }
  .vjs-menu-button-popup .vjs-menu {
    margin-bottom: 3em;
  }
  .f2em {
    font-size: 2em;
  }
}
</style>
