import moment from 'moment'
export default {
  formatTime (s) {
    moment.locale('en')
    const timestamp = new Date().getTime()
    const timeDifference = Math.round(timestamp - s) / 1000
    if (timeDifference > 259200) {
      return moment(s).format('YYYY-MM-DD')
    } else if (timeDifference > 86400) {
      return `${Math.round(timeDifference / 86400)}天前`
    } else if (timeDifference > 3600) {
      return `${Math.round(timeDifference / 3600)}小时前`
    } else if (timeDifference > 60) {
      return `${Math.round(timeDifference / 60)}分钟前`
    } else if (timeDifference > 0) {
      return `${Math.round(timeDifference)}秒前`
    }
  },
  formatNextDayYYYYMMDD (s, index) {
    if (!s) {
      return
    }
    return moment(s).add('days', index).format('MM月DD日')
  },
  getWeekSubscribe (s, index) { // 参数时间戳
    const timestamp = moment(s).add('days', index).valueOf()
    const week = moment(timestamp).day()
    switch (week) {
      case 1:
        return '周一'
      case 2:
        return '周二'
      case 3:
        return '周三'
      case 4:
        return '周四'
      case 5:
        return '周五'
      case 6:
        return '周六'
      case 0:
        return '周日'
    }
  },
  formatWeek (s) {
    if (s) {
      const weekTime = parseInt(moment().endOf('week').add(1, 'day').format('x'))
      if (s <= weekTime) {
        return '本周'
      } else if (s > weekTime && s <= (weekTime + 604800000)) {
        return '下周'
      } else if (s > (weekTime + 604800000) && s <= (weekTime + 604800000 * 2)) {
        return '第三周'
      } else if (s > (weekTime + 604800000 * 2) && s <= (weekTime + 604800000 * 3)) {
        return '第四周'
      } else if (s > (weekTime + 604800000 * 3) && s <= (weekTime + 604800000 * 4)) {
        return '第五周'
      } else if (s > (weekTime + 604800000 * 4) && s <= (weekTime + 604800000 * 5)) {
        return '第六周'
      }
    }
  },
  formatYYYYMMDD (s) {
    return moment(s).format('YYYY-MM-DD')
  },
  formatYYYYMMDDs (s) {
    if (s) {
      return moment(s).format('YYYY.MM.DD')
    } else {
      return
    }
  },
  formatMMDDs (s) {
    return moment(s).format('MM.DD')
  },
  formathhmmss (s) {
    return moment(s).format('HH:mm:ss')
  },
  formathhmm (s) {
    return moment(s).format('HH:mm')
  },
  formatMonth (s) {
    moment.locale('zh-cn')
    return moment(s).format('MMM')
  },
  getWeek (s) { // 参数时间戳
    const week = moment(s).day()
    switch (week) {
      case 1:
        return '周一'
      case 2:
        return '周二'
      case 3:
        return '周三'
      case 4:
        return '周四'
      case 5:
        return '周五'
      case 6:
        return '周六'
      case 0:
        return '周日'
    }
  },
  getWeek2 (s) { // 参数时间戳
    const week = moment(s).day()
    switch (week) {
      case 1:
        return '星期一'
      case 2:
        return '星期二'
      case 3:
        return '星期三'
      case 4:
        return '星期四'
      case 5:
        return '星期五'
      case 6:
        return '星期六'
      case 0:
        return '星期日'
    }
  },
  formatYYYYMMDD2 (s) {
    return moment(s).format('YYYY/MM/DD')
  },
  formatDay (s) {
    moment.locale('en')
    return moment(s).get('date')
  }

}
