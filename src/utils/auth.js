import cookie from 'js-cookie'

const TokenKey = 'token'

const AdminTokenKey = 'admin-token'

const AssistantId<PERSON><PERSON> = 'assistant-id'

export function getToken () {
  return cookie.get(TokenKey)
}
//
export function setToken (token) {
  return cookie.set(To<PERSON><PERSON><PERSON>, token, {
    expires: 28
  })
}
//
export function removeToken () {
  return cookie.remove(TokenKey)
}

export function getAdminToken () {
  return cookie.get(AdminTokenKey)
}
//
export function setAdminToken (token) {
  return cookie.set(AdminTokenKey, token, {
    expires: 28
  })
}
//
export function removeAdminToken () {
  return cookie.remove(AdminTokenKey)
}

export function getAssistantId () {
  return cookie.get(AssistantIdKey)
}
//
export function setAssistantId (token) {
  return cookie.set(AssistantIdK<PERSON>, token, {
    expires: 28
  })
}
//
export function removeAssistantId () {
  return cookie.remove(AssistantId<PERSON><PERSON>)
}
