import axios from 'axios'
// 创建axios实例
import baseUrl from '@/config'
import { removeAdminToken } from '@/utils/auth'
const service = axios.create({
  timeout: 10000
})

// request拦截器
service.interceptors.request.use(config => {
  var reg = new RegExp('(^|&)preview=([^&]*)(&|$)')
  var r = window.location.search.substr(1).match(reg)
  if (r && r[2] === '1') {
    config.headers['Authorization'] = ''
  }
  switch (config.urlType) {
    case 'api':
      config.url = baseUrl.default.BASE_API_URL + config.url
      if (!config.url.includes('getLastAppVersion')) {
        config.headers['app'] = 'Tradition'
        config.headers['appType'] = 'WEB'
        config.headers['appVersion'] = '1.0.0'
      }
      break
    default:
      config.url = baseUrl.default.BASE_ADMIN_URL + config.url
  }

  return config
}, error => {
  Promise.reject(error)
})

axios.defaults.withCredentials = true

// respone拦截器
service.interceptors.response.use(
  response => {
    switch (response.config.urlType) {
      case 'admin':
        if (+response.data.code === 600) {
          removeAdminToken()
          location.href = '/datacenter/login'
          return false
        }
        return response
      default:
        return response
    }
  },
  error => {
    console.log('err' + error)
    return Promise.reject(error)
  })
export default service
