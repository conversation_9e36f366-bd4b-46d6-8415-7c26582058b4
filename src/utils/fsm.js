export class IState {
    constructor(name, tag) {
        this.name = name;
        this.tag = tag;
        this.transitions = {};
        this._parent = null;
    }
    
    onEnter(prev) {
        if (prev) console.log("IState.onEnter", prev.name);
        else console.log("IState.onEnter: null");
    }

    onExit(next) {
        console.log("IState.onExit", next.name);
    }
    
    onUpdate(transition) {
        transition.fromState = this;
        if(transition.check()) {
            this._parent.setCurTransition(transition);
            this._parent.setCurState(transition.toState);
        }
    }

    addTransition(transition) {
        if(transition == null || transition.name == null || transition.name == "") return;
        this.transitions[transition.name] = transition;
    }

    setStateMachine(stateMachine) {
        this._parent = stateMachine;
    }
}

export class IStateMachine {
    constructor() {
        this._curState = null;
        this._states = {};
        this._curTransition = null;
        
    }

    get curState() {
        return this._curState;
    }

    get curTransition() {
        return this._curTransition;
    }

    addState(state) {
        if(state == null || state.name == null || state.name == "") return;
        this._states[state.name] = state;
        state.setStateMachine(this);
    }

    removeState(state) {
        if(state == null || state.name == null || state.name == "") return;
        this._states[state.name] = null;
        delete this._states[state.name];
    }

    setCurState(state) {
        if(state == null || state.name == null || state.name == "") return;
        if(!this._states[state.name]) return;
        state.setStateMachine(this);
        this._curState?.onExit(state);
        state.onEnter(this._curState);
        this._curState = state;
    }

    setCurTransition(transition) {
        this._curTransition = transition;
    }

    getStateWithTag(tag) {
        return Object.values(this._states).find(item=> item.tag == tag);
    }

    getStateWithName(name) {
        return Object.values(this._states).find(item=> item.name == name);
    }

    updateState(transition) {
        if(this._curState != null) {
            this._curState.onUpdate(transition);
            transition.onCompleteCallback();
        }
    }
}

export class ITransition {
    constructor(name) {
        this.name = name;
        this.toState = null;
        this.fromState = null;
    }

    check() {
        console.log("ITransition.check");
        return true;
    }

    onCompleteCallback() {
        console.log("ITransition.onCompleteCallback");
        return true;
    }
}