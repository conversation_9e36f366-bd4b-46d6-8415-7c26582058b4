import Vue from "vue";
import { IState, IStateMachine, ITransition } from "./fsm";

export class AIFSM extends IStateMachine {

    static eventBus = new Vue();
    static DataChanged = "DataChanged";
    static ActionEvent = "AIFSM";
    
    constructor(key) {
        super();
        this.eventKey = key || AIFSM.ActionEvent;
        this.context = new SegmentContext();
    }

    dispose() {
      AIFSM.eventBus.$off(this.eventKey, this.handleEvent);
    }

    updateSegmentList(segments = [], playback = true) {
      this.context.segments = segments;
      this.context.playback = playback;
      AIFSM.eventBus.$off(this.eventKey, this.handleEvent);
      AIFSM.eventBus.$on(this.eventKey, this.handleEvent)
    }

    init(segments = [], playback = false) {
      this.context.segments = segments;
      this.context.playback = playback;
      AIFSM.eventBus.$off(this.eventKey, this.handleEvent);
      AIFSM.eventBus.$on(this.eventKey, this.handleEvent)

      /// ========================> 注册所有的状态  <====================
      /// 1. 暂停状态
      var idleState = new IdleState();
      idleState.addTransition(new StartTransition()) /// 开始
      this.addState(idleState);

      /// 2. 视频播放状态
      var videoPlayState = new VideoPlayState();
      videoPlayState.addTransition(new VideoEndTransition()); /// 视频结束
      videoPlayState.addTransition(new EndTransition()); /// 结束
      this.addState(videoPlayState);

      /// 3. 视频播放过后提示阶段
      // var questionResultState = new VideoEndState();
      // questionResultState.addTransition(new VideoReplayTransition()); /// 重新播放
      // questionResultState.addTransition(new NextSegmentTransition()); /// 继续学习
      // questionResultState.addTransition(new EndResultTransition()); /// 查看课程结果
      // this.addState(questionResultState)
      
      /// 3. 问题回答阶段
      var questionShowState  = new QuestionShowState();
      questionShowState.addTransition(new AnswerTransition()); // 答题完成
      this.addState(questionShowState);

      /// 4. 章节过度阶段
      // var nextState = new NextState();
      // nextState.addTransition(new VideoEndTransition()); /// 视频结束
      // nextState.addTransition(new AnswerTransition()); // 答题完成
      // this.addState(nextState);

      /// 整个课程结束
      var endResultState = new EndResultState();
      endResultState.addTransition(new EndTransition()); /// 结束
      this.addState(endResultState);
      
      /// 设置当前状态
      this.setCurState(new IdleState());
    }

    /// 获取当前进度 - 恢复当前进度
    restore(index, segment) {
      console.log('aifsm===========>','restore')
      this.context.currentSegmentIndex = index;
      this.context.currentSegment = segment;
      this.context.calPrevVideo();
      if(this.context.currentSegment.aiSectionType == "VIDEO" || this.context.currentSegment.aiSectionType == "REPORT") {
        let videoState = new VideoPlayState(this.context);
        this.updateState(new StartTransition(videoState));
      } else {
        let questionShowState = new QuestionShowState(this.context);
        this.updateState(new StartTransition(questionShowState));
      }
    }

    updateState(transition) {
      super.updateState(transition);
      console.log('aifsm===========>','updateState')
      console.log(this);
      AIFSM.eventBus.$emit(AIFSM.DataChanged, this);
    }

    handleEvent = (event)=> {
      console.log(event);
      if(this.context.segments == null || this.context.segments.length == 0) return;
      /// 事件处理
      switch (event.key) {
        case "Start": 
          {
            let context = this.context.saveFirst();
            if(!context) return;
            context.currentAnsweredIndex = 0;
            context.currentProgressIndex = 0;
            if(context.currentSegment.aiSectionType == 'VIDEO') {
              let videoState = new VideoPlayState(context);
              this.updateState(new StartTransition(videoState));
            } else {
              let questionShowState = new QuestionShowState(context);
              this.updateState(new StartTransition(questionShowState));
            }
          }
          break;
        case "Skip":
          {
            var index = event.index;
            var segment = this.context.segments[index];
            this.restore(index, segment)
          }
          break;
        case "UpdateUnitUserId":
          this.context.unitUserId = event.unitUserId;
          AIFSM.eventBus.$emit(AIFSM.DataChanged, this);
          break;
        case "UpdateIndex":
          if (event.index > this.context.currentAnsweredIndex)
            this.context.currentAnsweredIndex = event.index;
          AIFSM.eventBus.$emit(AIFSM.DataChanged, this);
          break;
        case "Playback":
          {
            var dir = event.dir;
            if(dir == "first") {
              this.context.playback = true;
              let context = this.context.saveFirst();
              if(!context) return;
              if(context.currentSegment.aiSectionType == 'VIDEO') {
                let videoState = new VideoPlayState(context);
                this.updateState(new StartTransition(videoState));
              } else {
                let questionShowState = new QuestionShowState(context);
                this.updateState(new StartTransition(questionShowState));
              }
            }
            else if(dir == "prev") {
              let context = this.context.prevSegment();
              if(context.currentSegment.aiSectionType == "VIDEO") {
                let videoState = new VideoPlayState(context);
                this.updateState(new NextSegmentTransition(videoState));
              } else {
                let questionShowState = new QuestionShowState(context);
                this.updateState(new NextSegmentTransition(questionShowState));
              }
            } else {
              let context = this.context.nextSegment();
              if(!context) {
                let endResultSate = new EndResultState(context);
                this.updateState(new EndResultTransition(endResultSate))
                return;
              }
              if(context.currentSegment.aiSectionType == "VIDEO") {
                let videoState = new VideoPlayState(context);
                this.updateState(new NextSegmentTransition(videoState));
              } else {
                let questionShowState = new QuestionShowState(context);
                this.updateState(new NextSegmentTransition(questionShowState));
              }
            }
          }
          break;
        case "Answered":
          {
            let context = this.context.saveAnswer(event.answer);
            if(!context) return;
            // if(context.currentSegment.id === context.lastSegment.id) {
            //   let endResultSate = new EndResultState(context);
            //   this.updateState(new EndResultTransition(endResultSate))
            //   return;
            // }
            // var nextSegment = context.segments[context.currentSegmentIndex +1];
            // var preSegment = context.segments[context.currentSegmentIndex];
            let nextContext = this.context.nextSegment();
            nextContext.currentAnsweredIndex = nextContext.currentSegmentIndex;
            nextContext.currentProgressIndex = nextContext.currentSegmentIndex;
            // if (preSegment.step < nextContext.currentSegment.step) {
            //   let nextState = new NextState(this.context);
            //   this.updateState(new NextSegmentTransition(nextState))
            //   return
            // }
            // if(nextContext.currentSegment && nextContext.currentSegment.aiSectionType === "QUESTION") {
            //   let questionShowState = new QuestionShowState(nextContext);
            //   this.updateState(new AnswerTransition(questionShowState));
            // } else {
            //   let videoPlayState = new VideoPlayState(nextContext);
            //   this.updateState(new AnswerTransition(videoPlayState));
            // }
            if (nextContext.currentSegment) {
              switch (nextContext.currentSegment.aiSectionType) {
                case "QUESTION":
                  this.updateState(new AnswerTransition(new QuestionShowState(nextContext)));
                  break;
                case "VIDEO":
                  this.updateState(new AnswerTransition(new VideoPlayState(nextContext)));
                  break;
                default:
                  console.log('更新状态失败')
              }
            }
          }
          break;
        case "VideoEnd":
          {
            // if(this.context.lastSegment.id === this.context.currentSegment.id) {
            //   let endResultSate = new EndResultState(this.context);
            //   this.updateState(new EndResultTransition(endResultSate))
            //   return;
            // }
            let nextSegment = this.context.nextSegment();
            if(this.context.playback) {
              /// 回放阶段直接进入下一个环节
              if(nextSegment.currentSegment.aiSectionType == "VIDEO") {
                let videoState = new VideoPlayState(nextSegment);
                this.updateState(new NextSegmentTransition(videoState));
              } else {
                let questionShowState = new QuestionShowState(nextSegment);
                this.updateState(new NextSegmentTransition(questionShowState));
              }
              return;
            }
            if(this.context.currentProgressIndex > this.context.currentSegmentIndex 
              || this.context.currentSegment.step === nextSegment.currentSegment.step) {
                if(nextSegment.currentSegment.aiSectionType == "VIDEO") {
                  let videoState = new VideoPlayState(nextSegment);
                  this.updateState(new NextSegmentTransition(videoState));
                } else {
                  let questionShowState = new QuestionShowState(nextSegment);
                  this.updateState(new NextSegmentTransition(questionShowState));
                }
            } else {
              // let videoEndState = new VideoEndState(this.context);
              // this.updateState(new VideoEndTransition(videoEndState))
              // let nextState = new NextState(this.context);
              // this.updateState(new NextSegmentTransition(nextState))
            }
          }
          break;
        case "Replay":
          {
            let context = this.context.replayVideo();
            if(!context) return;
            if(context.currentSegment.aiSectionType == 'VIDEO') {
              let videoState = new VideoPlayState(context);
              this.updateState(new VideoReplayTransition(videoState));
            }
          }
          break;
        case "Next":
          {
            let context = this.context.nextSegment();
            // if(!context) {
            //   let endResultSate = new EndResultState(context);
            //   this.updateState(new EndResultTransition(endResultSate))
            //   return;
            // }
            context.currentAnsweredIndex = context.currentSegmentIndex;
            context.currentProgressIndex = context.currentSegmentIndex;
            if(context.currentSegment.aiSectionType == "VIDEO") {
              let videoState = new VideoPlayState(context);
              this.updateState(new NextSegmentTransition(videoState));
            } else if (context.currentSegment.aiSectionType == "QUESTION") {
              let questionShowState = new QuestionShowState(context);
              this.updateState(new NextSegmentTransition(questionShowState));
            } else if (context.currentSegment.aiSectionType == "REPORT") {
              let endResultState = new EndResultState(context);
              this.updateState(new NextSegmentTransition(endResultState));
            }
          }
          break;
        case "End":
          {
            this.context.reset();
            var idleState = new IdleState(this.context);
            this.updateState(new EndTransition(idleState));
            window.close();
          }
          break;
        case "NextStep":
          {
            let videoEndState = new VideoEndState(this.context);
            this.updateState(new VideoEndTransition(videoEndState))
          }
          break;
        default:
          break;
      }
    }
}

export class IdleState extends IState {
  constructor(context) {
    super("IdleState");
    this.context = context;
  }

  onEnter(prev) {
    super.onEnter(prev);
    console.log("===========> 进入待机");
  }

  onExit(next) {
    super.onExit(next);
    console.log("===========> 离开待机");
  }
}

export class VideoPlayState extends IState {
  
  constructor(context) {
    super("VideoPlayState");
    this.context = context;
  }

  onEnter(prev) {
    super.onEnter(prev);
    console.log("===========> 进入视频播放");
  }

  onExit(next) {
    super.onExit(next);
    console.log("===========> 离开视频播放");
  }
}

export class QuestionShowState extends IState {
  constructor(context) {
    super("QuestionShowState");
    this.context = context;
  }

  onEnter(prev) {
    super.onEnter(prev);
    console.log("===========> 进入试题展示");
  }

  onExit(next) {
    super.onExit(next);
    console.log("===========> 离开试题展示");
  }
}

export class ReportShowState extends IState {
  constructor(context) {
    super("ReportShowState");
    this.context = context;
  }

  onEnter(prev) {
    super.onEnter(prev);
    console.log("===========> 进入课程报告");
  }

  onExit(next) {
    super.onExit(next);
    console.log("===========> 离开试题展示");
  }
}

export class VideoEndState extends IState {
  constructor(context) {
    super("VideoEndState");
    this.context = context;
  }

  onEnter(prev) {
    super.onEnter(prev);
    console.log("===========> 进入视频播放结束提示环节");
  }

  onExit(next) {
    super.onExit(next);
    console.log("===========> 离开视频播放结束提示环节");
  }
}

export class EndResultState extends IState {
  constructor(context) {
    super("EndResultState");
    this.context = context;
  }

  onEnter(prev) {
    super.onEnter(prev);
    console.log("===========> 进入最终结果展示");
  }

  onExit(next) {
    super.onExit(next);
    console.log("===========> 离开最终结果展示");
  }
}

// export class NextState extends IState {
//   constructor(context) {
//     super("NextState");
//     this.context = context;
//   }

//   onEnter(prev) {
//     super.onEnter(prev);
//     console.log("===========> 进入章节过度环节");
//   }

//   onExit(next) {
//     super.onExit(next);
//     console.log("===========> 离开章节过度环节");
//   }
// }

export class StartTransition extends ITransition {
  constructor(toState) {
    super("StartTransition");
    this.toState = toState;
  }
  check() {
    super.check();
    if(this.fromState instanceof EndResultState) return true;
    if(this.fromState instanceof QuestionShowState) return true;
    if(this.fromState instanceof VideoEndState) return true;
    if(this.fromState instanceof VideoPlayState) return true;
    if(this.fromState instanceof IdleState) return true;
    if(this.fromState instanceof EndResultState) return true;
    return false;
  }
  
  onCompleteCallBack() {
    super.onCompleteCallback();
    return false;
  }
}

export class VideoEndTransition extends ITransition {
  constructor(toState) {
    super("VideoEndTransition");
    this.toState = toState;
  }

  check() {
    super.check();
    if(this.fromState instanceof VideoPlayState) return true;
    return false;
  }
  
  onCompleteCallBack() {
    super.onCompleteCallback();
    return false;
  }
}

export class AnswerTransition extends ITransition {
  constructor(toState) {
    super("AnswerTransition");
    this.toState = toState;
  }

  check() {
    super.check();
    if(this.fromState instanceof QuestionShowState) return true;
    return false;
  }
  
  onCompleteCallBack() {
    super.onCompleteCallback();
    return false;
  }
}

export class NextSegmentTransition extends ITransition {
  constructor(toState) {
    super("NextSegmentTransition");
    this.toState = toState;
  }

  check() {
    super.check();
    if(this.fromState instanceof VideoPlayState) return true;
    if(this.fromState instanceof VideoEndState) return true;
    if(this.fromState instanceof QuestionShowState) return true;
    // if(this.fromState instanceof NextState) return true;
    return false;
  }

  onCompleteCallBack() {
    super.onCompleteCallback();
    return false;
  }
}

export class EndResultTransition extends ITransition {
  constructor(toState) {
    super("EndResultTransition");
    this.toState = toState;
  }

  check() {
    super.check();
    if(this.fromState instanceof QuestionShowState) return true;
    if(this.fromState instanceof VideoEndState) return true;
    if(this.fromState instanceof VideoPlayState) return true;
    return false;
  }
  
  onCompleteCallBack() {
    super.onCompleteCallback();
    return false;
  }
}

export class EndTransition extends ITransition {
  constructor(toState) {
    super("EndTransition");
    this.toState = toState;
  }

  check() {
    super.check();
    if(this.fromState instanceof EndResultState) return true;
    if(this.fromState instanceof VideoPlayState) return true;
    return false;
  }

  onCompleteCallBack() {
    super.onCompleteCallback();
    return false;
  }
}

export class VideoReplayTransition extends ITransition {
  constructor(toState) {
    super("VideoReplayTransition");
    this.toState = toState;
  }

  check() {
    super.check();
    if(this.fromState instanceof VideoEndState) return true;
    return false;
  }
  
  onCompleteCallBack() {
    super.onCompleteCallback();
    return false;
  }
}

export class SegmentContext {
  segments = [];
  currentSegment = null;
  currentSegmentIndex = null;
  prevVideo = null;
  prevVideoIndex = null;
  playback = false;
  currentAnsweredIndex = 0;
  currentProgressIndex = 0;
  unitUserId = null;

  get lastSegment() {
    if(this.segments.length === 0) return null;
    return this.segments[this.segments.length-1];
  }


  reset() {}

  calPrevVideo() {
    if(this.currentSegment.aiSectionType == "VIDEO") {
      this.prevVideo = this.segments[this.currentSegmentIndex];
      this.prevVideoIndex = this.currentSegmentIndex;
    } else if(this.currentSegmentIndex != 0) {
      for(var index = this.currentSegmentIndex-1;index>=0;index--) {
        var segment = this.segments[index];
        if(segment.aiSectionType == "VIDEO") {
          this.prevVideo = segment
          this.prevVideoIndex = index;
          break;
        }
      }
    }
  }

  nextSegment() {
    if(this.currentSegmentIndex == null || this.segments.length == 0) return null;
    if(this.currentSegmentIndex == this.segments.length - 1) return null;
    this.currentSegmentIndex = this.currentSegmentIndex + 1;
    this.currentSegment = this.segments[this.currentSegmentIndex];
    this.calPrevVideo();
    return this;
  }

  prevSegment() {
    if(this.currentSegmentIndex == null || this.segments.length == 0) return null;
    if(this.currentSegmentIndex == 0) return null;
    this.currentSegmentIndex = this.currentSegmentIndex - 1;
    this.currentSegment = this.segments[this.currentSegmentIndex];
    this.calPrevVideo();
    return this;
  }

  saveFirst() {
    if(this.currentSegmentIndex == null || this.segments.length == 0) return null;
    this.currentSegmentIndex = 0;
    this.currentSegment = this.segments[0];
    this.calPrevVideo();
    return this;
  }

  saveAnswer(answer) {
    this.currentSegment.answer = answer;
    return this;
  }

  replayVideo() {
    if(this.prevVideo == null) return null;
    this.currentSegment = this.prevVideo;
    this.currentSegmentIndex = this.prevVideoIndex;
    return this;
  }
}

export default AIFSM;
