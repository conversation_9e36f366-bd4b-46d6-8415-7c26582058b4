import router from './router'
import store from './store'

const host = window.location.host
import { getAdminToken } from '@/utils/auth'

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }

  if (host.indexOf('data.cuiya.cn') > -1) {
    if (to.path.indexOf('datacenter') === -1) {
      next({ path: '/datacenter' })
    }
  }
  // 测试环境
  if (host.indexOf('data-qa.cuiya.cn') > -1) {
    if (to.path.indexOf('datacenter') === -1) {
      next({ path: '/datacenter' })
    }
  }
  if (to.name === 'dataCenterLogin' && getAdminToken()) {
    next({ path: '/datacenter' })
  }

  if (!store.getters.loginStatus && to.path.indexOf('datacenter') === -1) {
    store.dispatch('GetUserInfo').then(response => {
      if (+response.data.code === 200) {
        next({
          ...to,
          replace: true
        })
      } else {
        next()
      }
    }).catch(() => {
      store.dispatch('LogOut')
      next()
    })
  } else {
    next()
  }

  if (to.path.indexOf('datacenter') > -1 && to.name !== 'dataCenterLogin') {
    store.dispatch('GetDataUserInfo').then(response => {
      if (+response.data.code === 200) {
        next({
          ...to,
          replace: true
        })
      } else {
        next()
      }
    }).catch(() => {
      console.log('error')
    })
  } else {
    next()
  }
})
