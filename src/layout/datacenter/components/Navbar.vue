<template>
    <div class="datacenter-nav">
      <div v-if="showTime" class="left">
        <div class="l-item time">
          {{ currTime }}
        </div>
        <div class="l-item">
          {{ dateName }}
        </div>
        <div class="l-item">
          {{ week }}
        </div>
      </div>
      <div v-else class="left" @click="back">
        <span><i class="el-icon-arrow-left"></i>返回</span>
      </div>
      <div class="title">空中课堂数据大屏</div>
      <div class="right">
        <div class="r-item">
          <Tree :treeData="organizationList" :defaultName="'全部组织'" :orignSelectNode="organization" @handleNodeClick="selectOrganization"></Tree>
        </div>
        <div class="r-item">
          <Tree :treeData="schoolList" :defaultName="'全部学校'" :orignSelectNode="school" @handleNodeClick="selectSchool"></Tree>
        </div>
        <div class="r-item">
          <Tree :treeData="yearList" :defaultName="'全部学年'" :orignSelectNode="year" @handleNodeClick="selectYear"></Tree>
        </div>
        <div class="r-item">
          <Tree :treeData="termList" :defaultName="'全部学期'" :orignSelectNode="term" @handleNodeClick="selectTerm"></Tree>
        </div>
        <div class="r-item" style="margin-right: 60px">
          <el-popover
            placement="bottom-start"
            popper-class="pop-name"
            trigger="click"
          >
            <div class="pointer" @click="logout">
              <i class="el-icon-switch-button"></i>
              退出登录
            </div>
            <span class="pointer" slot="reference">{{ userInfo.name || '' }}</span>
          </el-popover>
        </div>
      </div>
    </div>
</template>

<script>
import Tree from "@/viewsdatacenter/components/tree.vue"
import { getStartYear, getOrganizationList, getSchoolList } from '@/api/center.js'
import { mapGetters } from "vuex";
export default {
    components: {
        Tree
    },
    data () {
        return {
            currTime: '',
            week: '',
            dateName: '',
            yearList: [],
            termList: [],
            organizationList: [],
            schoolList: [],
            year: this.$store.getters.year,
            term: this.$store.getters.term,
            organization: this.$store.getters.organization,
            school: this.$store.getters.school,
            showTime: false
        }
    },
    computed: {
      ...mapGetters(["userInfo"])
    },
    created () {
      this.getData()
    },
    watch: {
        $route(to, from){
          console.log('to', to)
          console.log('from', from)
          var pathname = window.location.pathname
          if (pathname === '/datacenter') {
            this.showTime = true
          } else {
            this.showTime = false
          }
        },
    },
    mounted () {
        this.currTime = this.$moment.formathhmmss(new Date())
        this.tCurrTime = setInterval(() => {
        this.currTime = this.$moment.formathhmmss(new Date())}, 1000);
        this.week = this.$moment.getWeek2(new Date())
        this.dateName = this.$moment.formatYYYYMMDD2(new Date())

        var pathname = window.location.pathname
        if (pathname === '/datacenter' || pathname === '/datacenter/') {
          this.showTime = true
        } else {
          this.showTime = false
        }
    },
    methods: {
      logout () {
        this.$store
            .dispatch("LogoutByData")
            .then(response => {
              this.loading = false
              if (response.data.code === 200) {
                  this.$router.replace({
                    path: '/datacenter/login'
                  });
              } else {
                this.$toast(response.data.message);
              }
            })
            .catch(err => {
              this.loading = false
              console.log(err);
            })
      },
      getData () {
        this._getStartYear()
        this._getTermList()
        this._getOrganizationList()
        this._getSchoolList()
      },
      _getStartYear () {
        this.yearList = [{'id': -1,'name': '全部学年'}]
        var formData = new FormData()
        if (this.organization.parent_path) formData.append('parent_path', this.organization.parent_path)
        if (this.school.id && this.school.id !== -1) formData.append('school_id', this.school.id)
        getStartYear(formData).then(
          response => {
            const start_year = +response.data.data.start_year
            const now_year = new Date().getFullYear()
            for (let year = start_year; year < now_year + 1; year++) {
              this.yearList.push({ 'id': year, 'name': year })
            }
          }
        )
      },
      _getTermList () {
        if (this.year && this.year.id && this.year.id !== -1) {
          this.termList = [{'id': -1,'name': '全部学期'},{ 'id': 'first', 'name': '上学期'}, { 'id': 'second', 'name': '下学期' }]
        } else {
          this.termList = [{'id': -1,'name': '全部学期'}]
        }
      },
      _getOrganizationList () {
        getOrganizationList().then(
          response => {
            this.organizationList = [{'id': -1,'name': '全部组织'}].concat(response.data.data)
          }
        )
      },
      _getSchoolList () {
        var formData = new FormData()
        if (this.organization.parent_path) formData.append('parent_path', this.organization.parent_path)
        getSchoolList(formData).then(
          response => {
            this.schoolList = [{'id': -1,'name': '全部学校'}].concat(response.data.data)
          }
        )
      },
      selectYear (data) {
        this.$store.dispatch("SaveYear", data);
        this.year = data

        //  学期重置
        this.selectTerm({})
        this._getTermList()

        this._updateData()
      },
      selectTerm (data) {
        this.$store.dispatch("SaveTerm", data);
        this.term = data
        this._updateData()
      },
      selectOrganization (data) {
        this.$store.dispatch("SaveOrganization", data);
        this.organization = data

        //  学校重置
        this._getSchoolList()
        this.$store.dispatch("SaveSchool", {});
        this.school = {}

        //  年份重置
        this.$store.dispatch("SaveYear", {});
        this.year = {}
        this._getStartYear()

        //  学期重置
        this.$store.dispatch("SaveTerm", {});
        this.term = {}
        this._getTermList()

        this._updateData()
      },
      selectSchool (data) {
        this.$store.dispatch("SaveSchool", data);
        this.school = data

        //  年份重置
        this.$store.dispatch("SaveYear", {});
        this.year = {}
        this._getStartYear()

        //  学期重置
        this.$store.dispatch("SaveTerm", {});
        this.term = {}
        this._getTermList()

        this._updateData()
      },
      back () {
        this.$router.push({path: '/datacenter'})
      },
      _updateData () {
        this.$bus.$emit('updateData')
        this.$bus.$emit('updateGoodClass')
      }
    }
}
</script>

<style lang="scss" scoped>
.datacenter-nav {
    width: 100%;
    height: vh(50);
    background: url('~assets/datacenter/nav.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    // position: fixed;

    .title {
        // background: linear-gradient(180deg, #ffffff 0%, #a3d4f6 100%);
        // -webkit-background-clip: text;
        // color: transparent;
        color: #fff;
        font-size: 20px;
    }
    .left {
        position: absolute;
        top: 0;
        left: 0;
        height: vh(40);
        color: #fff;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        .l-item {
          margin-left: vw(20);
        }

        .time {
          font-weight: 500;
          font-size: 18px;
        }

        span {
          font-weight: 400;
          font-size: 16px;
          color: #EAEAEA;
          letter-spacing: 0;
          cursor: pointer;
        }

        .el-icon-arrow-left {
          margin-left: vh(22);
          margin-right: vh(10);
        }
    }
    .right {
        position: absolute;
        top: 0;
        right: 0;
        height: vh(40);
        color: #fff;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        .r-item {
          margin-right: vh(20);
        }
    }
}
</style>