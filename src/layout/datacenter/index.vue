<template>
  <div class="data-center">
    <navbar />
    <div class="container">
      <router-view />
    </div>
  </div>
</template>

<script>
import Navbar from './components/Navbar.vue'
export default {
  components: {
    Navbar
  },
  data () {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.data-center {
  width: 100vw;
  height: 100vh;
  min-width: 1280px;
  min-height: 700px;
  background: url('~assets/datacenter/bg.png') no-repeat no-repeat center;
  background-size: cover;
  overflow-x: hidden;

  .container {
    height: calc(100% - #{vh(50)});
    width: 100%;
    overflow: auto;
  }
}
</style>
