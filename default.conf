# server {
#   listen 80;
#   server_name  _;
#   root /var/app/dist;

#   location / {
#     try_files $uri $uri/ /index.html;
#   }

#   location = /index.html {
#       add_header Cache-Control  no-cache;
#       # add_header Pragma no-cache;
#     }

#   location ~ /\.ht {
#     deny all;
#   }
# }


server {
  listen       80;
  server_name  _;
  root   /usr/share/nginx/html;

  #charset koi8-r;
  access_log  /var/log/nginx/host.access.log  main;
  error_log  /var/log/nginx/error.log  error;

  location / {
      try_files $uri $uri/ /index.html;
  }

    location = /index.html {
      add_header Cache-Control  no-cache;
      # add_header Pragma no-cache;
    }
 location ^~ /jupyterhub {

         proxy_pass https://classroom-v2-qa.bingotalk.cn;  # 注意：去掉末尾的 `/jupyterhub/`

 #         proxy_set_header Host $host;  # 强制覆盖为目标 Host
         proxy_set_header X-Original-Host $host;  # 保留原始 Host 供调试

         # 关键：添加 Ingress 需要的特定头
         proxy_set_header X-Forwarded-Host $host;
         proxy_set_header X-Forwarded-Port $server_port;

         # 基本代理设置
         proxy_set_header X-Real-IP $remote_addr;
         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
         proxy_set_header X-Forwarded-Proto https;
         proxy_set_header X-Forwarded-Ssl on;
         proxy_set_header X-Forwarded-Port $server_port;
         proxy_set_header X-Forwarded-Server $host;
         proxy_set_header Accept-Encoding "";
         proxy_set_header real-host $host;

         # WebSocket 支持
         proxy_http_version 1.1;
         proxy_set_header Upgrade $http_upgrade;
         proxy_set_header Connection "upgrade";

         # 移除所有可能的安全头
         proxy_hide_header X-Frame-Options;
         proxy_hide_header Content-Security-Policy;
         proxy_hide_header Access-Control-Allow-Origin;

         add_header X-Upstream-Addr $upstream_addr;
         add_header X-Upstream-Status $upstream_status;
         add_header X-Request-Time $request_time;

         proxy_buffering off;
         proxy_redirect off;

     }


    location ~ /\.ht {
      deny all;
    }
}
