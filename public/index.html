<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
    content="width=device-width,user-scalable=no,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,minimal-ui">
  <link rel="icon" href="/favicon.ico">
  <meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
  <title>萃雅教育 - 专注于中小学中华文化数字化教育服务</title>
  <meta name="keywords" content="中华优秀传统文化,传统文化,中华文化,中华文化传承,地方特色文化,红色文化,课后延时服务,素质教育,网络教育,网络课堂,趣味学习" />
  <meta name="description" content="四川萃雅教育科技有限公司是专注于中华文化传承教育的国有控股企业，秉承“用心传承文化，用行成就未来”的发展理念，以弘扬中华文化为己任，为中小学提供中华文化数字化教育服务。咨询：4001680260" />
  <style>
    .loading {
      background: rgba(217, 246, 255, 1);
      width: 100%;
      height: 100%;
      position: fixed;
      left: 0px;
      top: 0px;
      bottom: 0px;
      right: 0px;
      z-index: 9999;
      display:inline;
    }
    .loading-gif {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-family: "Lato", sans-serif;
      font-weight: 600;
      font-size: 20px;
      color: #000;
      text-align: center;
      -webkit-font-smoothing: antialiased;
    }


  .pencil {
    width: 10em;
    height: 10em;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .pencil__body1,
  .pencil__body2,
  .pencil__body3,
  .pencil__eraser,
  .pencil__eraser-skew,
  .pencil__point,
  .pencil__rotate,
  .pencil__stroke {
    animation-duration: 3s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
  }
  .pencil__body1,
  .pencil__body2,
  .pencil__body3 {
    transform: rotate(-90deg);
  }
  .pencil__body1 {
    animation-name: pencilBody1;
  }
  .pencil__body2 {
    animation-name: pencilBody2;
  }
  .pencil__body3 {
    animation-name: pencilBody3;
  }
  .pencil__eraser {
    animation-name: pencilEraser;
    transform: rotate(-90deg) translate(49px,0);
  }
  .pencil__eraser-skew {
    animation-name: pencilEraserSkew;
    animation-timing-function: ease-in-out;
  }
  .pencil__point {
    animation-name: pencilPoint;
    transform: rotate(-90deg) translate(49px,-30px);
  }
  .pencil__rotate {
    animation-name: pencilRotate;
  }
  .pencil__stroke {
    animation-name: pencilStroke;
    transform: translate(100px,100px) rotate(-113deg);
  }

  /* Animations */
  @keyframes pencilBody1 {
    from,
    to {
      stroke-dashoffset: 351.86;
      transform: rotate(-90deg);
    }
    50% {
      stroke-dashoffset: 150.8; /* 3/8 of diameter */
      transform: rotate(-225deg);
    }
  }
  @keyframes pencilBody2 {
    from,
    to {
      stroke-dashoffset: 406.84;
      transform: rotate(-90deg);
    }
    50% {
      stroke-dashoffset: 174.36;
      transform: rotate(-225deg);
    }
  }
  @keyframes pencilBody3 {
    from,
    to {
      stroke-dashoffset: 296.88;
      transform: rotate(-90deg);
    }
    50% {
      stroke-dashoffset: 127.23;
      transform: rotate(-225deg);
    }
  }
  @keyframes pencilEraser {
    from,
    to {
      transform: rotate(-45deg) translate(49px,0);
    }
    50% {
      transform: rotate(0deg) translate(49px,0);
    }
  }
  @keyframes pencilEraserSkew {
    from,
    32.5%,
    67.5%,
    to {
      transform: skewX(0);
    }
    35%,
    65% {
      transform: skewX(-4deg);
    }
    37.5%, 
    62.5% {
      transform: skewX(8deg);
    }
    40%,
    45%,
    50%,
    55%,
    60% {
      transform: skewX(-15deg);
    }
    42.5%,
    47.5%,
    52.5%,
    57.5% {
      transform: skewX(15deg);
    }
  }
  @keyframes pencilPoint {
    from,
    to {
      transform: rotate(-90deg) translate(49px,-30px);
    }
    50% {
      transform: rotate(-225deg) translate(49px,-30px);
    }
  }
  @keyframes pencilRotate {
    from {
      transform: translate(100px,100px) rotate(0);
    }
    to {
      transform: translate(100px,100px) rotate(720deg);
    }
  }
  @keyframes pencilStroke {
    from {
      stroke-dashoffset: 439.82;
      transform: translate(100px,100px) rotate(-113deg);
    }
    50% {
      stroke-dashoffset: 164.93;
      transform: translate(100px,100px) rotate(-113deg);
    }
    75%,
    to {
      stroke-dashoffset: 439.82;
      transform: translate(100px,100px) rotate(112deg);
    }
  }
  </style>
  <link rel="stylesheet" type="text/css" href="./static/css/baidu.css">
</head>

<body class="bridge">
  <!-- <div class="loading">
    <div class="loading-gif">
      加载中 ...
    </div>
  </div> -->
  <div class="loading">
    <svg class="pencil" viewBox="0 0 200 200" width="200px" height="200px" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <clipPath id="pencil-eraser">
          <rect rx="5" ry="5" width="30" height="30"></rect>
        </clipPath>
      </defs>
      <circle class="pencil__stroke" r="70" fill="none" stroke="currentColor" stroke-width="2" stroke-dasharray="439.82 439.82" stroke-dashoffset="439.82" stroke-linecap="round" transform="rotate(-113,100,100)" />
      <g class="pencil__rotate" transform="translate(100,100)">
        <g fill="none">
          <circle class="pencil__body1" r="64" stroke="hsl(223,90%,50%)" stroke-width="30" stroke-dasharray="402.12 402.12" stroke-dashoffset="402" transform="rotate(-90)" />
          <circle class="pencil__body2" r="74" stroke="hsl(223,90%,60%)" stroke-width="10" stroke-dasharray="464.96 464.96" stroke-dashoffset="465" transform="rotate(-90)" />
          <circle class="pencil__body3" r="54" stroke="hsl(223,90%,40%)" stroke-width="10" stroke-dasharray="339.29 339.29" stroke-dashoffset="339" transform="rotate(-90)" />
        </g>
        <g class="pencil__eraser" transform="rotate(-90) translate(49,0)">
          <g class="pencil__eraser-skew">
            <rect fill="hsl(223,90%,70%)" rx="5" ry="5" width="30" height="30" />
            <rect fill="hsl(223,90%,60%)" width="5" height="30" clip-path="url(#pencil-eraser)" />
            <rect fill="hsl(223,10%,90%)" width="30" height="20" />
            <rect fill="hsl(223,10%,70%)" width="15" height="20" />
            <rect fill="hsl(223,10%,80%)" width="5" height="20" />
            <rect fill="hsla(223,10%,10%,0.2)" y="6" width="30" height="2" />
            <rect fill="hsla(223,10%,10%,0.2)" y="13" width="30" height="2" />
          </g>
        </g>
        <g class="pencil__point" transform="rotate(-90) translate(49,-30)">
          <polygon fill="hsl(33,90%,70%)" points="15 0,30 30,0 30" />
          <polygon fill="hsl(33,90%,50%)" points="15 0,6 30,0 30" />
          <polygon fill="hsl(223,10%,10%)" points="15 0,20 10,10 10" />
        </g>
      </g>
    </svg>
  </div>
  <div id="app"></div>
</body>
<script>
  document.querySelector(".loading").style.display = "inline";
  window.onload = function () {
    document.querySelector(".loading").style.display = "none";
  };
</script>

</html>
