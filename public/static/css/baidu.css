.bridge #newBridge .icon-right-center {
	width: 60px !important;
	height: 60px !important;
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
	border-radius: 20px !important;
	right: 45px;
	margin-top: 10px !important;
}

.bridge #newBridge .nb-icon-inner-wrap {
	background: transparent !important;
	width: 100% !important;
	height: 60px !important;
}

.bridge #newBridge .nb-icon-skin-9 .nb-icon-bridge0 {}

.bridge #newBridge .nb-icon-skin-0 .nb-icon-inner-wrap .nb-icon-bridge-base {
	width: 42px;
	height: 42px;
	margin-top: -21px;
	margin-left: -21px;
	background: url(../<EMAIL>) center center no-repeat;
	background-size: cover;
}

.bridge #newBridge .nb-icon-group .nb-group-icon {
	width: 0px;
	height: 0px;
	display: none;
}

.bridge #newBridge .nb-icon-group .nb-icon-groups {
	background: transparent;
	border: none;

}

.bridge #newBridge .nb-icon-groups-item {
	width: 96px;
	height: 28px;
	line-height: 28px;
	background-color: #e8e8e8 !important;
	border: none;
	text-align: center;
	margin: 10px auto !important;
	border: none !important;
}

.bridge #newBridge .nb-icon-group .nb-group-text {
	width: 100%;
	margin-left: 0;
	text-align: center;
	color: #4d4d4d !important;
	font-size: 12px;
}


.bridge #newBridge .nb-invite-wrap .nb-invite-text {
	top: 40px;
	left: 178px;
	right: 20px;
}

.bridge #newBridge .nb-invite-wrap-base .nb-invite-text-base p {
	color: #4d4d4d !important;
	font-size: 14px;
	line-height: 1.5;
}

.bridge #newBridge .nb-invite-wrap-base .nb-invite-btn-base {
	right: 35px;
	bottom: 26px;
}

.bridge #newBridge .nb-invite-wrap-base .nb-invite-cancel-base {
	background-color: #e8e8e8;
	color: #8b8b8b;
	font-size: 12px;
}

.bridge #newBridge .nb-invite-wrap-base .nb-invite-ok-base {
	background-color: #ffa438;
	color: #fff;
	font-size: 12px;
}

.bridge #newBridge .nb-invite-wrap-base .nb-invite-tool-base {
	background: url(../images/<EMAIL>) center center no-repeat;
	right: 15px;
	top: 12px;
}

.bridge #newBridge .nb-nodeboard-base .nb-nodeboard-contain-base .nb-nodeboard-top {
	background-color: #ffa438;
}

.bridge #newBridge .nb-nodeboard-base .nb-nodeboard-contain-base .nb-nodeboard-send-btn {
	background-color: #ffa438;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2),
only screen and (min-device-pixel-ratio: 2) {
	.bridge #newBridge .nb-icon-base .nb-icon-bridge-base {
		background: url(../images/<EMAIL>) center center no-repeat;
		background-size: 100%;
	}

	.bridge #newBridge .nb-invite-wrap-base .nb-invite-tool-base {
		background: url(../images/<EMAIL>) center center no-repeat;
		background-size: 100%;
		right: 15px;
		top: 12px;
	}
}